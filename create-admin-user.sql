-- This script creates an admin user with email: <EMAIL> and password: admin@1234
-- The password is stored as a BCrypt hash

-- Make sure the ADMIN role exists
INSERT INTO roles (name, description, is_default, priority, created_at, updated_at, deleted, version)
SELECT 'ADMIN', 'Administrator role with full access', 0, 100, NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'ADMIN');

-- Create the admin user if it doesn't exist
-- The password hash is for 'admin@1234'
INSERT INTO users (first_name, last_name, email, password, enabled, email_verified, created_at, updated_at, deleted, version)
SELECT 'Admin', 'User', '<EMAIL>', '$2a$10$Uj0Aw9Yx3jCkzG7Uo5As0.IfRcJCnZ0Hl4MvbVUXvCdkRZ5vXtGwC', 1, 1, NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- Link the user to the ADMIN role
INSERT INTO user_roles (user_id, role_id)
SELECT 
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM roles WHERE name = 'ADMIN')
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')
    AND role_id = (SELECT id FROM roles WHERE name = 'ADMIN')
);
