<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTP Verification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .otp-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .otp-input {
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 20px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>OTP Verification Test</h1>
    
    <div class="container">
        <h2>Get OTP Code</h2>
        <div>
            <label for="email-get">Email:</label>
            <input type="email" id="email-get" placeholder="Enter your email">
        </div>
        <div style="margin-top: 10px;">
            <button onclick="getOtpCode()">Get OTP Code</button>
        </div>
        <div id="getOtpResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Verify OTP</h2>
        <div>
            <label for="email-verify">Email:</label>
            <input type="email" id="email-verify" placeholder="Enter your email">
        </div>
        <div>
            <label for="otp">OTP Code:</label>
            <div class="otp-container">
                <input type="text" class="otp-input" maxlength="1" id="otp1" onkeyup="moveToNext(this, 'otp2')">
                <input type="text" class="otp-input" maxlength="1" id="otp2" onkeyup="moveToNext(this, 'otp3')">
                <input type="text" class="otp-input" maxlength="1" id="otp3" onkeyup="moveToNext(this, 'otp4')">
                <input type="text" class="otp-input" maxlength="1" id="otp4" onkeyup="moveToNext(this, 'otp5')">
                <input type="text" class="otp-input" maxlength="1" id="otp5" onkeyup="moveToNext(this, 'otp6')">
                <input type="text" class="otp-input" maxlength="1" id="otp6">
            </div>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="verifyOtp()">Verify OTP</button>
        </div>
        <div id="verifyOtpResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Resend OTP</h2>
        <div>
            <label for="email-resend">Email:</label>
            <input type="email" id="email-resend" placeholder="Enter your email">
        </div>
        <div style="margin-top: 10px;">
            <button onclick="resendOtp()">Resend OTP</button>
        </div>
        <div id="resendOtpResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Response</h2>
        <pre id="responseData"></pre>
    </div>

    <script>
        function moveToNext(current, nextId) {
            if (current.value.length === current.maxLength) {
                document.getElementById(nextId).focus();
            }
        }
        
        function getOtpCode() {
            const email = document.getElementById('email-get').value;
            const getOtpResult = document.getElementById('getOtpResult');
            
            if (!email) {
                getOtpResult.innerHTML = '<span class="error">Please enter email</span>';
                return;
            }
            
            getOtpResult.innerHTML = 'Getting OTP code...';
            
            fetch('http://localhost:8080/api/v1/authentication/get-otp-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success' && data.data) {
                    getOtpResult.innerHTML = `<span class="success">OTP code: ${data.data}</span>`;
                    
                    // Fill the OTP inputs
                    const otpCode = data.data;
                    for (let i = 0; i < 6; i++) {
                        document.getElementById(`otp${i+1}`).value = otpCode.charAt(i);
                    }
                    
                    // Fill the email field for verification
                    document.getElementById('email-verify').value = email;
                } else {
                    getOtpResult.innerHTML = `<span class="error">Failed to get OTP code: ${data.message}</span>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                getOtpResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            });
        }
        
        function verifyOtp() {
            const email = document.getElementById('email-verify').value;
            const verifyOtpResult = document.getElementById('verifyOtpResult');
            
            // Get OTP code from inputs
            let otpCode = '';
            for (let i = 1; i <= 6; i++) {
                otpCode += document.getElementById(`otp${i}`).value;
            }
            
            if (!email || !otpCode || otpCode.length !== 6) {
                verifyOtpResult.innerHTML = '<span class="error">Please enter email and 6-digit OTP code</span>';
                return;
            }
            
            verifyOtpResult.innerHTML = 'Verifying OTP...';
            
            fetch('http://localhost:8080/api/v1/authentication/verify-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    otpCode: otpCode
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    verifyOtpResult.innerHTML = '<span class="success">Email verified successfully!</span>';
                } else {
                    verifyOtpResult.innerHTML = `<span class="error">Verification failed: ${data.message}</span>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                verifyOtpResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            });
        }
        
        function resendOtp() {
            const email = document.getElementById('email-resend').value;
            const resendOtpResult = document.getElementById('resendOtpResult');
            
            if (!email) {
                resendOtpResult.innerHTML = '<span class="error">Please enter email</span>';
                return;
            }
            
            resendOtpResult.innerHTML = 'Resending OTP...';
            
            fetch('http://localhost:8080/api/v1/authentication/resend-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    resendOtpResult.innerHTML = '<span class="success">New OTP code sent successfully. Please check your email.</span>';
                    
                    // Fill the email field for getting the OTP
                    document.getElementById('email-get').value = email;
                } else {
                    resendOtpResult.innerHTML = `<span class="error">Failed to resend OTP: ${data.message}</span>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resendOtpResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            });
        }
    </script>
</body>
</html>
