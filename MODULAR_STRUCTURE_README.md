# Modular Package Structure

This project uses a modular package structure organized by feature/domain rather than by layer. This approach improves maintainability, encapsulation, and makes the codebase easier to understand.

## Package Structure

```
src/
└── main/
    ├── java/
    │   └── com/
    │       └── pressmeet247/
    │           ├── AuthServiceApplication.java
    │
    │           ├── auth/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   │   ├── request/
    │           │   │   └── response/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   ├── config/
    │           │   ├── exception/
    │           │   └── util/
    │
    │           ├── admin/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   └── config/
    │
    │           ├── ai_news/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   └── impl/
    │
    │           ├── subscription/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   └── repository/
    │
    │           ├── email/
    │           │   ├── service/
    │           │   └── config/
    │
    │           ├── common/
    │           │   ├── config/
    │           │   ├── security/
    │           │   ├── jwt/
    │           │   ├── oauth2/
    │           │   ├── exception/
    │           │   ├── base/
    │           │   └── util/
```

## Module Descriptions

### Auth Module (`com.pressmeet247.auth`)

Handles user authentication, registration, and account management. This includes:
- User registration and login
- Password reset
- Email verification
- JWT token management
- User profile management

### Admin Module (`com.pressmeet247.admin`)

Handles admin-specific functionality, including:
- Admin dashboard
- User management
- Content management
- Admin authentication with OTP verification
- Statistics and reporting

### AI News Module (`com.pressmeet247.ai_news`)

Handles news-related functionality, including:
- News creation and retrieval
- News approval workflow
- AI-generated content
- News categorization
- Featured news management

### Subscription Module (`com.pressmeet247.subscription`)

Handles subscription-related functionality, including:
- Subscription plans
- Payment processing
- Subscription management
- Subscription status tracking
- Renewal notifications

### Email Module (`com.pressmeet247.email`)

Handles email-related functionality, including:
- Email sending
- Email templating
- Email verification
- Notification emails
- Marketing emails

### Common Module (`com.pressmeet247.common`)

Contains common functionality used across the application, including:
- Base entities
- Security configuration
- JWT handling
- OAuth2 integration
- Exception handling
- Utility classes

## Benefits of Modular Structure

1. **Better Organization**: Code is organized by feature/module, making it easier to find related code
2. **Improved Maintainability**: Changes to one module are less likely to affect other modules
3. **Better Encapsulation**: Each module encapsulates its own functionality
4. **Easier Onboarding**: New developers can understand the system more quickly
5. **Scalability**: Easier to add new features or modules

## Development Guidelines

When working with this modular structure, follow these guidelines:

1. **Keep modules independent**: Minimize dependencies between modules
2. **Use common module judiciously**: Only put truly shared code in the common module
3. **Maintain consistent structure**: Follow the same structure within each module
4. **Document module boundaries**: Use package-info.java to document module purposes
5. **Respect encapsulation**: Don't access internal components of other modules directly
