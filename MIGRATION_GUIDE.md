# Migration Guide: Modular Package Structure

This guide explains how to migrate from the current package structure to the new modular package structure.

## New Package Structure

The new package structure is organized by feature/module rather than by layer:

```
src/
└── main/
    ├── java/
    │   └── com/
    │       └── pressmeet247/
    │           ├── AuthServiceApplication.java
    │
    │           ├── auth/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   │   ├── request/
    │           │   │   └── response/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   ├── config/
    │           │   ├── exception/
    │           │   └── util/
    │
    │           ├── admin/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   └── config/
    │
    │           ├── ai_news/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   ├── repository/
    │           │   └── impl/
    │
    │           ├── subscription/
    │           │   ├── controller/
    │           │   ├── service/
    │           │   ├── dto/
    │           │   ├── entity/
    │           │   └── repository/
    │
    │           ├── email/
    │           │   ├── service/
    │           │   └── config/
    │
    │           ├── common/
    │           │   ├── config/
    │           │   ├── security/
    │           │   ├── jwt/
    │           │   ├── oauth2/
    │           │   ├── exception/
    │           │   ├── base/
    │           │   └── util/
```

## Migration Steps

### 1. Move Files to New Structure

Move files from the old structure to the new structure according to the following mapping:

#### Authentication Module

- `com.auth.service.controller.AuthenticationController` → `com.pressmeet247.auth.controller`
- `com.auth.service.service.auth.*` → `com.pressmeet247.auth.service`
- `com.auth.service.dto.request.*` → `com.pressmeet247.auth.dto.request`
- `com.auth.service.dto.response.*` → `com.pressmeet247.auth.dto.response`
- `com.auth.service.entity.User`, `Role`, etc. → `com.pressmeet247.auth.entity`
- `com.auth.service.repository.UserRepository`, etc. → `com.pressmeet247.auth.repository`
- `com.auth.service.config.auth.*` → `com.pressmeet247.auth.config`
- `com.auth.service.exception.AuthenticationException`, etc. → `com.pressmeet247.auth.exception`
- `com.auth.service.util.auth.*` → `com.pressmeet247.auth.util`

#### Admin Module

- `com.auth.service.controller.AdminDashboardController`, etc. → `com.pressmeet247.admin.controller`
- `com.auth.service.service.AdminDashboardService`, etc. → `com.pressmeet247.admin.service`
- `com.auth.service.dto.admin.*` → `com.pressmeet247.admin.dto`
- Admin-specific entities → `com.pressmeet247.admin.entity`
- Admin-specific repositories → `com.pressmeet247.admin.repository`
- `com.auth.service.config.admin.*` → `com.pressmeet247.admin.config`

#### AI News Module

- News-related controllers → `com.pressmeet247.ai_news.controller`
- News-related services → `com.pressmeet247.ai_news.service`
- News-related DTOs → `com.pressmeet247.ai_news.dto`
- News-related entities → `com.pressmeet247.ai_news.entity`
- News-related repositories → `com.pressmeet247.ai_news.repository`
- News-related implementations → `com.pressmeet247.ai_news.impl`

#### Subscription Module

- Subscription-related controllers → `com.pressmeet247.subscription.controller`
- Subscription-related services → `com.pressmeet247.subscription.service`
- Subscription-related DTOs → `com.pressmeet247.subscription.dto`
- Subscription-related entities → `com.pressmeet247.subscription.entity`
- Subscription-related repositories → `com.pressmeet247.subscription.repository`

#### Email Module

- `com.auth.service.service.email.*` → `com.pressmeet247.email.service`
- `com.auth.service.config.email.*` → `com.pressmeet247.email.config`

#### Common Module

- `com.auth.service.config.*` → `com.pressmeet247.common.config`
- `com.auth.service.config.security.*` → `com.pressmeet247.common.security`
- `com.auth.service.config.jwt.*` → `com.pressmeet247.common.jwt`
- `com.auth.service.config.oauth2.*` → `com.pressmeet247.common.oauth2`
- `com.auth.service.exception.*` → `com.pressmeet247.common.exception`
- `com.auth.service.entity.base.*` → `com.pressmeet247.common.base`
- `com.auth.service.util.*` → `com.pressmeet247.common.util`

### 2. Update Package Declarations

Update the package declaration at the top of each file to reflect its new location.

### 3. Update Import Statements

Update import statements in all files to reference the new package structure.

### 4. Update Configuration Files

Update any configuration files that reference the old package structure:

- `application.properties` or `application.yml`
- `pom.xml` (main class reference)
- Any other configuration files

### 5. Testing

After migration, thoroughly test the application to ensure everything works correctly:

1. Run unit tests
2. Start the application
3. Test all major functionality
4. Check for any runtime errors or exceptions

## Benefits of the New Structure

1. **Better Organization**: Code is organized by feature/module, making it easier to find related code
2. **Improved Maintainability**: Changes to one module are less likely to affect other modules
3. **Better Encapsulation**: Each module encapsulates its own functionality
4. **Easier Onboarding**: New developers can understand the system more quickly
5. **Scalability**: Easier to add new features or modules

## Troubleshooting

If you encounter issues during migration:

1. Check package declarations and import statements
2. Verify that all files have been moved to the correct location
3. Check for circular dependencies between modules
4. Ensure that configuration files have been updated correctly
