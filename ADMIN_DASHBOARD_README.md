# Admin Dashboard

The Admin Dashboard provides a comprehensive interface for administrators to manage the application.

## Features

- **Dashboard Overview**: View key statistics about users, news, and subscriptions
- **Content Management**: Manage news articles (approve, reject, feature)
- **User Management**: View and manage all users and administrators
- **Analytics**: View statistics and reports
- **Communication**: Send notifications to users

## Setup

### Creating an Admin User

To create an admin user, run the application with the `admin-setup` profile:

```bash
java -jar your-application.jar --spring.profiles.active=admin-setup
```

This will create a default admin user with the following credentials:
- Email: <EMAIL>
- Password: Admin@123

**Important**: Change the default password after first login!

### Custom Admin User

To create a custom admin user, you can use the API or modify the database directly:

1. Register a regular user
2. Assign the ADMIN role to the user

## API Endpoints

All admin endpoints are secured and require the ADMIN role.

### Dashboard

- `GET /api/admin/dashboard/stats` - Get dashboard statistics
- `GET /api/admin/dashboard/users` - Get all users
- `GET /api/admin/dashboard/users/admins` - Get admin users
- `GET /api/admin/dashboard/news` - Get all news
- `GET /api/admin/dashboard/news/status/{status}` - Get news by status
- `POST /api/admin/dashboard/news/{id}/approve` - Approve a news article
- `POST /api/admin/dashboard/news/{id}/reject` - Reject a news article
- `PUT /api/admin/dashboard/news/{id}/featured` - Set a news article as featured

## Security

The Admin Dashboard is secured using Spring Security with role-based access control. Only users with the ADMIN role can access the dashboard.

## Frontend Integration

The Admin Dashboard API is designed to be consumed by a frontend application. The frontend should:

1. Authenticate as an admin user
2. Use the JWT token to access the admin endpoints
3. Implement the dashboard UI using the API responses

## Customization

You can customize the Admin Dashboard by:

1. Adding new endpoints to the AdminDashboardController
2. Extending the AdminDashboardService with new functionality
3. Creating new DTOs for additional data

## Troubleshooting

If you encounter issues with the Admin Dashboard:

1. Ensure the user has the ADMIN role
2. Check the JWT token is valid and not expired
3. Verify the API endpoints are accessible
4. Check the server logs for errors
