# Migration script to reorganize the codebase into a modular structure

# Define source and target directories
$sourceBase = "src\main\java\com\auth\service"
$targetBase = "src\main\java\com\pressmeet247"

# Create mapping for files to move
$fileMappings = @(
    # Main application file
    @{
        Source = "src\main\java\com\auth\service\AuthServiceApplication.java"
        Target = "src\main\java\com\pressmeet247\AuthServiceApplication.java"
        PackageFrom = "package com.auth.service"
        PackageTo = "package com.pressmeet247"
    },

    # Auth module
    @{
        Source = "src\main\java\com\auth\service\controller\AuthenticationController.java"
        Target = "src\main\java\com\pressmeet247\auth\controller\AuthController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.auth.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\auth\AuthService.java"
        Target = "src\main\java\com\pressmeet247\auth\service\AuthService.java"
        PackageFrom = "package com.auth.service.service.auth"
        PackageTo = "package com.pressmeet247.auth.service"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\User.java"
        Target = "src\main\java\com\pressmeet247\auth\entity\User.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.auth.entity"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\Role.java"
        Target = "src\main\java\com\pressmeet247\auth\entity\Role.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.auth.entity"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\UserRepository.java"
        Target = "src\main\java\com\pressmeet247\auth\repository\UserRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.auth.repository"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\RoleRepository.java"
        Target = "src\main\java\com\pressmeet247\auth\repository\RoleRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.auth.repository"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\request\LoginRequest.java"
        Target = "src\main\java\com\pressmeet247\auth\dto\request\LoginRequest.java"
        PackageFrom = "package com.auth.service.dto.request"
        PackageTo = "package com.pressmeet247.auth.dto.request"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\request\SignUpRequest.java"
        Target = "src\main\java\com\pressmeet247\auth\dto\request\SignUpRequest.java"
        PackageFrom = "package com.auth.service.dto.request"
        PackageTo = "package com.pressmeet247.auth.dto.request"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\response\ApiResponse.java"
        Target = "src\main\java\com\pressmeet247\auth\dto\response\ApiResponse.java"
        PackageFrom = "package com.auth.service.dto.response"
        PackageTo = "package com.pressmeet247.auth.dto.response"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\response\AuthResponse.java"
        Target = "src\main\java\com\pressmeet247\auth\dto\response\AuthResponse.java"
        PackageFrom = "package com.auth.service.dto.response"
        PackageTo = "package com.pressmeet247.auth.dto.response"
    },
    @{
        Source = "src\main\java\com\auth\service\exception\UserAlreadyExistsException.java"
        Target = "src\main\java\com\pressmeet247\auth\exception\UserAlreadyExistsException.java"
        PackageFrom = "package com.auth.service.exception"
        PackageTo = "package com.pressmeet247.auth.exception"
    },
    @{
        Source = "src\main\java\com\auth\service\exception\AuthenticationException.java"
        Target = "src\main\java\com\pressmeet247\auth\exception\AuthenticationException.java"
        PackageFrom = "package com.auth.service.exception"
        PackageTo = "package com.pressmeet247.auth.exception"
    },

    # Admin module
    @{
        Source = "src\main\java\com\auth\service\controller\AdminDashboardController.java"
        Target = "src\main\java\com\pressmeet247\admin\controller\AdminDashboardController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.admin.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\AdminDashboardService.java"
        Target = "src\main\java\com\pressmeet247\admin\service\AdminDashboardService.java"
        PackageFrom = "package com.auth.service.service"
        PackageTo = "package com.pressmeet247.admin.service"
    },
    @{
        Source = "src\main\java\com\auth\service\controller\AdminAuthController.java"
        Target = "src\main\java\com\pressmeet247\admin\controller\AdminAuthController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.admin.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\admin\AdminOtpService.java"
        Target = "src\main\java\com\pressmeet247\admin\service\AdminOtpService.java"
        PackageFrom = "package com.auth.service.service.admin"
        PackageTo = "package com.pressmeet247.admin.service"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\response\DashboardStatsResponse.java"
        Target = "src\main\java\com\pressmeet247\admin\dto\DashboardStatsResponse.java"
        PackageFrom = "package com.auth.service.dto.response"
        PackageTo = "package com.pressmeet247.admin.dto"
    },

    # AI News module
    @{
        Source = "src\main\java\com\auth\service\controller\NewsController.java"
        Target = "src\main\java\com\pressmeet247\ai_news\controller\NewsController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.ai_news.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\NewsService.java"
        Target = "src\main\java\com\pressmeet247\ai_news\service\NewsService.java"
        PackageFrom = "package com.auth.service.service"
        PackageTo = "package com.pressmeet247.ai_news.service"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\News.java"
        Target = "src\main\java\com\pressmeet247\ai_news\entity\News.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.ai_news.entity"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\NewsRepository.java"
        Target = "src\main\java\com\pressmeet247\ai_news\repository\NewsRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.ai_news.repository"
    },

    # Subscription module
    @{
        Source = "src\main\java\com\auth\service\controller\SubscriptionController.java"
        Target = "src\main\java\com\pressmeet247\subscription\controller\SubscriptionController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.subscription.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\SubscriptionService.java"
        Target = "src\main\java\com\pressmeet247\subscription\service\SubscriptionService.java"
        PackageFrom = "package com.auth.service.service"
        PackageTo = "package com.pressmeet247.subscription.service"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\Subscription.java"
        Target = "src\main\java\com\pressmeet247\subscription\entity\Subscription.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.subscription.entity"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\SubscriptionRepository.java"
        Target = "src\main\java\com\pressmeet247\subscription\repository\SubscriptionRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.subscription.repository"
    },

    # Email module
    @{
        Source = "src\main\java\com\auth\service\service\email\IEmailService.java"
        Target = "src\main\java\com\pressmeet247\email\service\IEmailService.java"
        PackageFrom = "package com.auth.service.service.email"
        PackageTo = "package com.pressmeet247.email.service"
    },
    @{
        Source = "src\main\java\com\auth\service\service\email\EmailService.java"
        Target = "src\main\java\com\pressmeet247\email\service\EmailService.java"
        PackageFrom = "package com.auth.service.service.email"
        PackageTo = "package com.pressmeet247.email.service"
    },
    @{
        Source = "src\main\java\com\auth\service\service\email\DevEmailService.java"
        Target = "src\main\java\com\pressmeet247\email\service\DevEmailService.java"
        PackageFrom = "package com.auth.service.service.email"
        PackageTo = "package com.pressmeet247.email.service"
    },

    # Common module
    @{
        Source = "src\main\java\com\auth\service\entity\base\BaseEntity.java"
        Target = "src\main\java\com\pressmeet247\common\base\BaseEntity.java"
        PackageFrom = "package com.auth.service.entity.base"
        PackageTo = "package com.pressmeet247.common.base"
    },
    @{
        Source = "src\main\java\com\auth\service\config\security\SecurityConfig.java"
        Target = "src\main\java\com\pressmeet247\common\security\SecurityConfig.java"
        PackageFrom = "package com.auth.service.config.security"
        PackageTo = "package com.pressmeet247.common.security"
    },
    @{
        Source = "src\main\java\com\auth\service\config\jwt\JwtAuthenticationFilter.java"
        Target = "src\main\java\com\pressmeet247\common\jwt\JwtAuthenticationFilter.java"
        PackageFrom = "package com.auth.service.config.jwt"
        PackageTo = "package com.pressmeet247.common.jwt"
    },
    @{
        Source = "src\main\java\com\auth\service\config\jwt\JwtService.java"
        Target = "src\main\java\com\pressmeet247\common\jwt\JwtService.java"
        PackageFrom = "package com.auth.service.config.jwt"
        PackageTo = "package com.pressmeet247.common.jwt"
    },
    @{
        Source = "src\main\java\com\auth\service\config\oauth2\OAuth2SuccessHandler.java"
        Target = "src\main\java\com\pressmeet247\common\oauth2\OAuth2SuccessHandler.java"
        PackageFrom = "package com.auth.service.config.oauth2"
        PackageTo = "package com.pressmeet247.common.oauth2"
    },
    @{
        Source = "src\main\java\com\auth\service\exception\GlobalExceptionHandler.java"
        Target = "src\main\java\com\pressmeet247\common\exception\GlobalExceptionHandler.java"
        PackageFrom = "package com.auth.service.exception"
        PackageTo = "package com.pressmeet247.common.exception"
    },
    @{
        Source = "src\main\java\com\auth\service\config\AppConfig.java"
        Target = "src\main\java\com\pressmeet247\common\config\AppConfig.java"
        PackageFrom = "package com.auth.service.config"
        PackageTo = "package com.pressmeet247.common.config"
    }
)

# Function to update imports in a file
function Update-Imports {
    param (
        [string]$filePath,
        [string]$oldPackageBase,
        [string]$newPackageBase
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Update package declaration
        $content = $content -replace "package $oldPackageBase", "package $newPackageBase"
        
        # Update imports
        $content = $content -replace "import $oldPackageBase", "import $newPackageBase"
        
        # Special case for auth.service to auth
        $content = $content -replace "import com.pressmeet247.auth.service.auth", "import com.pressmeet247.auth.service"
        
        # Write updated content back to file
        Set-Content -Path $filePath -Value $content
        
        Write-Host "Updated imports in $filePath"
    }
    else {
        Write-Host "File not found: $filePath"
    }
}

# Process each file mapping
foreach ($mapping in $fileMappings) {
    $sourceFile = $mapping.Source
    $targetFile = $mapping.Target
    $packageFrom = $mapping.PackageFrom
    $packageTo = $mapping.PackageTo
    
    # Create target directory if it doesn't exist
    $targetDir = Split-Path -Path $targetFile -Parent
    if (-not (Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        Write-Host "Created directory: $targetDir"
    }
    
    # Copy file if source exists
    if (Test-Path $sourceFile) {
        Copy-Item -Path $sourceFile -Destination $targetFile -Force
        Write-Host "Copied $sourceFile to $targetFile"
        
        # Update package declaration and imports
        Update-Imports -filePath $targetFile -oldPackageBase $packageFrom -newPackageBase $packageTo
    }
    else {
        Write-Host "Source file not found: $sourceFile"
    }
}

# Update pom.xml to reflect the new main class
$pomFile = "pom.xml"
if (Test-Path $pomFile) {
    $pomContent = Get-Content $pomFile -Raw
    $pomContent = $pomContent -replace "<mainClass>com.auth.service.AuthServiceApplication</mainClass>", "<mainClass>com.pressmeet247.AuthServiceApplication</mainClass>"
    Set-Content -Path $pomFile -Value $pomContent
    Write-Host "Updated main class in pom.xml"
}

Write-Host "Migration completed!"
