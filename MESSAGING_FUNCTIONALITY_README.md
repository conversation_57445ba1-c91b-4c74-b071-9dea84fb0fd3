# Messaging Functionality Documentation

## Overview

This document describes the comprehensive messaging functionality implemented in the Spring Boot application. The system enables administrators to send notifications via **Email** and **WhatsApp** to users with various targeting options.

## Features

### 🎯 **Targeting Options**
1. **Single User** - Send to a specific user by ID
2. **Multiple Users** - Send to a list of selected users
3. **All Users** - Broadcast to all active users
4. **User Type** - Target users by type (STUDENT, COMPANY, COLLEGE, etc.)
5. **Role-based** - Target users by their roles
6. **Activity-based** - Target active/inactive users

### 📱 **Messaging Channels**
- **Email** - Using Spring Boot's JavaMailSender (SMTP)
- **WhatsApp** - Using Twilio API integration
- **SMS** - Framework ready (implementation can be added)

### 📊 **Tracking & Analytics**
- Comprehensive message logging
- Delivery status tracking
- Retry mechanism for failed messages
- Message statistics and reporting
- Broadcast message tracking

## Architecture

### Core Components

#### 1. **Entities**
- `MessageLog` - Comprehensive message tracking
- `User` - Enhanced with `UserType` field
- `UserType` - Enum for user categorization

#### 2. **Services**
- `UnifiedMessagingService` - Main messaging orchestrator
- `WhatsAppService` - Twilio WhatsApp integration
- `EmailService` - Email messaging (existing, enhanced)
- `UserTargetingService` - User discovery and targeting
- `MessageRetryService` - Automatic retry for failed messages

#### 3. **Controllers**
- `MessagingController` - Admin messaging endpoints
- `UserTargetingController` - User discovery endpoints

#### 4. **Enums**
- `MessageChannel` - EMAIL, WHATSAPP, SMS, PUSH_NOTIFICATION
- `MessageType` - ANNOUNCEMENT, NOTIFICATION, MARKETING, etc.
- `MessageStatus` - PENDING, SENT, DELIVERED, READ, FAILED, CANCELLED
- `UserType` - STUDENT, COMPANY, COLLEGE, INDIVIDUAL, etc.

## API Endpoints

### Messaging Endpoints

#### Send Message
```http
POST /api/admin/messaging/send
Authorization: Bearer {admin_jwt_token}
Content-Type: application/json

{
  "channel": "EMAIL",
  "messageType": "ANNOUNCEMENT",
  "subject": "Important Announcement",
  "content": "This is an important message for all users.",
  "targetType": "ALL_USERS"
}
```

#### Get Message History
```http
GET /api/admin/messaging/history?page=0&size=20
Authorization: Bearer {admin_jwt_token}
```

#### Get Message Statistics
```http
GET /api/admin/messaging/statistics
Authorization: Bearer {admin_jwt_token}
```

### User Targeting Endpoints

#### Get Users by Type
```http
GET /api/admin/users/by-type/STUDENT?page=0&size=20
Authorization: Bearer {admin_jwt_token}
```

#### Search Users
```http
GET /api/admin/users/search?searchTerm=john&page=0&size=20
Authorization: Bearer {admin_jwt_token}
```

#### Get Recently Active Users
```http
GET /api/admin/users/recently-active?days=30&page=0&size=20
Authorization: Bearer {admin_jwt_token}
```

## Configuration

### Application Properties

```properties
# Twilio WhatsApp Configuration
app.twilio.enabled=true
app.twilio.account-sid=${TWILIO_ACCOUNT_SID}
app.twilio.auth-token=${TWILIO_AUTH_TOKEN}
app.twilio.whatsapp-from=${TWILIO_WHATSAPP_FROM}

# Messaging Configuration
app.messaging.retry.max-attempts=3
app.messaging.retry.delay-minutes=5
app.messaging.async.enabled=true

# Email Configuration (existing)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME}
spring.mail.password=${EMAIL_PASSWORD}
```

### Environment Variables

```bash
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# Email Configuration
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
```

## Usage Examples

### 1. Send Email to Single User

```json
{
  "channel": "EMAIL",
  "messageType": "NOTIFICATION",
  "subject": "Account Update",
  "content": "Your account has been updated successfully.",
  "targetType": "SINGLE_USER",
  "recipientUserId": 123
}
```

### 2. Send WhatsApp to Multiple Users

```json
{
  "channel": "WHATSAPP",
  "messageType": "REMINDER",
  "content": "Don't forget about tomorrow's meeting!",
  "targetType": "MULTIPLE_USERS",
  "recipientUserIds": [123, 456, 789]
}
```

### 3. Broadcast Email to All Students

```json
{
  "channel": "EMAIL",
  "messageType": "ANNOUNCEMENT",
  "subject": "Student Portal Maintenance",
  "content": "The student portal will be under maintenance this weekend.",
  "targetType": "USER_TYPE",
  "targetUserType": "STUDENT"
}
```

### 4. Send WhatsApp to All Users

```json
{
  "channel": "WHATSAPP",
  "messageType": "ALERT",
  "content": "System maintenance scheduled for tonight at 11 PM.",
  "targetType": "ALL_USERS"
}
```

## Database Schema

### MessageLog Table
```sql
CREATE TABLE message_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sender_admin_id BIGINT NOT NULL,
    recipient_user_id BIGINT NOT NULL,
    channel VARCHAR(20) NOT NULL,
    message_type VARCHAR(30) NOT NULL,
    subject VARCHAR(255),
    content TEXT NOT NULL,
    recipient_identifier VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    error_message VARCHAR(500),
    external_message_id VARCHAR(100),
    retry_count INT NOT NULL DEFAULT 0,
    is_broadcast BOOLEAN NOT NULL DEFAULT FALSE,
    broadcast_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,
    
    INDEX idx_message_log_sender (sender_admin_id),
    INDEX idx_message_log_recipient (recipient_user_id),
    INDEX idx_message_log_channel (channel),
    INDEX idx_message_log_status (status),
    INDEX idx_message_log_sent_at (sent_at),
    
    FOREIGN KEY (sender_admin_id) REFERENCES users(id),
    FOREIGN KEY (recipient_user_id) REFERENCES users(id)
);
```

### User Table Enhancement
```sql
ALTER TABLE users ADD COLUMN user_type VARCHAR(20) DEFAULT 'INDIVIDUAL';
```

## Security

### Authentication & Authorization
- All messaging endpoints require `ADMIN` role
- JWT token-based authentication
- HttpOnly cookies for secure token storage

### Input Validation
- Request DTOs with validation annotations
- Content sanitization
- User existence validation

### Rate Limiting
- Configurable retry mechanisms
- Async processing to prevent blocking
- Failed message tracking and retry

## Monitoring & Logging

### Message Tracking
- Every message is logged with detailed information
- Status tracking throughout delivery lifecycle
- Error logging with detailed error messages
- Retry attempt tracking

### Statistics Available
- Total messages sent
- Success/failure rates
- Channel-wise distribution
- User targeting statistics
- Retry statistics

## Testing

### Unit Tests
- Comprehensive service layer testing
- Mock-based testing for external dependencies
- Edge case coverage

### Integration Tests
- End-to-end API testing
- Database integration testing
- External service integration testing

### Running Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=UnifiedMessagingServiceTest

# Run with coverage
mvn test jacoco:report
```

## Deployment Considerations

### Production Setup
1. Configure proper SMTP settings
2. Set up Twilio account and WhatsApp Business API
3. Configure environment variables
4. Set up monitoring and alerting
5. Configure database indexes for performance

### Scaling Considerations
- Async message processing
- Message queue integration (future enhancement)
- Database partitioning for large message volumes
- Caching for user targeting queries

## Future Enhancements

### Planned Features
1. **Message Templates** - Predefined message templates
2. **Scheduled Messaging** - Send messages at specific times
3. **Message Queue Integration** - RabbitMQ/Apache Kafka
4. **Push Notifications** - Mobile app notifications
5. **Message Analytics Dashboard** - Real-time analytics
6. **A/B Testing** - Message effectiveness testing
7. **Webhook Support** - Delivery status webhooks

### API Improvements
1. **Bulk Operations** - Batch message operations
2. **Message Personalization** - Dynamic content insertion
3. **Attachment Support** - File attachments for emails
4. **Rich Media** - Images and videos for WhatsApp

## Support

For technical support or questions about the messaging functionality:

1. Check the application logs for detailed error messages
2. Verify configuration settings
3. Test with a small user group first
4. Monitor message statistics for delivery issues

## Conclusion

This messaging system provides a robust, scalable solution for multi-channel communication with comprehensive tracking and retry mechanisms. The modular design allows for easy extension and customization based on specific business requirements.
