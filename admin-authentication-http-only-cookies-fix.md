# Admin Authentication Fix: HTTP-Only Cookies Implementation

## Current Issues Identified

After reviewing your current implementation in `admin-authentication-process.md`, I've identified several issues that are causing the authentication problems:

1. **Mixed Authentication Methods**: Your code is trying to use both HTTP-only cookies and localStorage tokens simultaneously, causing conflicts.

2. **Token Handling Inconsistency**: The frontend is still trying to extract and store tokens in localStorage, even though the backend is using HTTP-only cookies.

3. **Authorization Header Issues**: The error `Malformed JWT token: JWT strings must contain exactly 2 period characters. Found: 0` indicates that the JWT token is not being properly sent in the Authorization header.

4. **Redundant Authentication Checks**: Multiple components are performing authentication checks, leading to race conditions and infinite loops.

5. **Improper Cookie Configuration**: The cookies may not be properly configured for cross-domain requests.

## Solution: Complete HTTP-Only Cookies Implementation

### 1. Update axiosConfig.js

This is the most critical change. Your current implementation is still trying to add tokens from localStorage to the Authorization header, which conflicts with HTTP-only cookies.

```javascript
// src/utils/axiosConfig.js
import axios from 'axios';

// Create axios instance with base URL
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
  timeout: 10000,
  withCredentials: true // Critical for HTTP-only cookies
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // REMOVE the code that adds tokens from localStorage
    // With HTTP-only cookies, the browser automatically sends the cookie
    // No need to manually add Authorization headers
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 Unauthorized and the request is not a retry
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token using HTTP-only cookies
        // No need to send the token in the request body
        await axios.post('/api/admin/auth/refresh-token', {}, { 
          baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
          withCredentials: true 
        });

        // No need to extract and store tokens from the response
        // The new token is automatically stored in an HTTP-only cookie

        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);

        // Clear authentication state
        localStorage.removeItem('admin_authenticated');
        localStorage.removeItem('adminEmail');

        // Redirect to login page
        window.location.href = '/admin/login';
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
```

### 2. Update adminAuthApi.js

```javascript
// src/api/adminAuthApi.js
import axiosInstance from '../utils/axiosConfig';

// Base URL for admin auth API
const ADMIN_AUTH_URL = '/api/admin/auth';

/**
 * Initiate admin login by sending OTP to email
 * @param {string} email - Admin email
 * @returns {Promise<Object>} - API response
 */
export const initiateAdminLogin = async (email) => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/login/initiate`, { email });
    return response.data;
  } catch (error) {
    console.error('Error initiating admin login:', error);
    throw error;
  }
};

/**
 * Verify admin login OTP
 * @param {string} email - Admin email
 * @param {string} otp - One-time password
 * @param {string} password - Admin password
 * @returns {Promise<Object>} - API response
 */
export const verifyAdminLoginOTP = async (email, otp, password) => {
  try {
    // Include password in the request as required by the backend
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/login/verify`, { 
      email, 
      otp,
      password // Make sure to include the password
    });
    return response.data;
  } catch (error) {
    console.error('Error verifying admin OTP:', error);
    throw error;
  }
};

/**
 * Get current admin user data
 * @returns {Promise<Object>} - Admin user data
 */
export const getCurrentAdmin = async () => {
  try {
    const response = await axiosInstance.get(`${ADMIN_AUTH_URL}/me`);
    return response.data;
  } catch (error) {
    console.error('Error getting current admin:', error);
    throw error;
  }
};

/**
 * Admin logout
 * @returns {Promise<Object>} - API response
 */
export const adminLogout = async () => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/logout`);
    return response.data;
  } catch (error) {
    console.error('Error logging out admin:', error);
    throw error;
  }
};

/**
 * Refresh admin token
 * @returns {Promise<Object>} - API response
 */
export const refreshAdminToken = async () => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/refresh-token`);
    return response.data;
  } catch (error) {
    console.error('Error refreshing admin token:', error);
    throw error;
  }
};
```

### 3. Update AdminOTPVerificationPage.jsx

The OTP verification page needs to be updated to handle HTTP-only cookies properly:

```jsx
// src/pages/admin/AdminOTPVerificationPage.jsx
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { verifyAdminLoginOTP } from '../../api/adminAuthApi';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { ROUTES } from '../../config';
import './AdminLoginPage.css';

const AdminOTPVerificationPage = () => {
  const [otp, setOtp] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState(''); // Add password state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(60);
  const navigate = useNavigate();
  const { setAdminUserData } = useAdminAuth();

  // Get email and password from session storage
  useEffect(() => {
    const storedEmail = sessionStorage.getItem('adminEmail');
    const storedPassword = sessionStorage.getItem('adminPassword'); // Get password from session storage
    
    if (!storedEmail || !storedPassword) {
      // Redirect to login if email or password is not found
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    } else {
      setEmail(storedEmail);
      setPassword(storedPassword);
    }
  }, [navigate]);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!otp) {
      setError('Please enter the OTP sent to your email');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Call the API to verify OTP with email, OTP, and password
      const apiResponse = await verifyAdminLoginOTP(email, otp, password);

      if (apiResponse && apiResponse.status === 'success') {
        console.log('OTP verification successful');

        // Set admin user data in context
        setAdminUserData({
          email: email,
          role: 'ADMIN'
        });

        // Set authentication flag for UI state management only
        localStorage.setItem('admin_authenticated', 'true');
        
        // Clear sensitive data from session storage
        sessionStorage.removeItem('adminPassword');

        console.log('Admin authentication successful, redirecting to dashboard...');
        
        // Redirect to admin dashboard
        navigate(ROUTES.ADMIN_DASHBOARD, { replace: true });
      } else {
        setError(apiResponse.message || 'Failed to verify OTP. Please try again.');
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      setError(err.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Rest of the component remains the same...
};

export default AdminOTPVerificationPage;
```

### 4. Update AdminLoginPage.jsx

```jsx
// src/pages/admin/AdminLoginPage.jsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { initiateAdminLogin } from '../../api/adminAuthApi';
import { ROUTES } from '../../config';
import './AdminLoginPage.css';

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState(''); // Add password field
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!password) {
      setError('Please enter your password');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Call the API to initiate login
      const response = await initiateAdminLogin(email);

      if (response && response.status === 'success') {
        // Store email and password in session storage for OTP verification
        sessionStorage.setItem('adminEmail', email);
        sessionStorage.setItem('adminPassword', password);

        // Redirect to OTP verification page
        navigate(ROUTES.ADMIN_OTP_VERIFICATION);
      } else {
        setError(response.message || 'Failed to initiate login. Please try again.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-auth-container">
      <div className="admin-auth-card">
        <div className="admin-auth-logo">
          <i className="bi bi-newspaper"></i>
          <span>Press<span className="highlight">Meet</span></span>
        </div>
        <h1 className="admin-auth-title">Admin Login</h1>
        <p className="admin-auth-subtitle">Enter your credentials to receive a one-time password</p>

        {error && <div className="admin-auth-error">{error}</div>}

        <form onSubmit={handleSubmit} className="admin-auth-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <div className="input-with-icon">
              <i className="bi bi-envelope"></i>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="input-with-icon">
              <i className="bi bi-lock"></i>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                disabled={isLoading}
              />
            </div>
          </div>

          <button
            type="submit"
            className="admin-auth-button"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading-spinner-small"></span>
            ) : (
              'Continue'
            )}
          </button>
        </form>

        <div className="admin-auth-links">
          <Link to={ROUTES.HOME}>← Back to Website</Link>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginPage;
```

### 5. Update AdminAuthContext.jsx

```jsx
// src/context/AdminAuthContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { getCurrentAdmin, adminLogout, refreshAdminToken } from '../api/adminAuthApi';

// Create context
const AdminAuthContext = createContext();

// Provider component
export const AdminAuthProvider = ({ children }) => {
  const [adminUser, setAdminUser] = useState(null);
  const [isAdminAuthenticated, setIsAdminAuthenticated] = useState(false);
  const [isAdminLoading, setIsAdminLoading] = useState(true);
  const [adminError, setAdminError] = useState(null);

  // Check if admin is authenticated on mount
  useEffect(() => {
    // Create a flag to track if the component is mounted
    let isMounted = true;

    const checkAdminAuth = async () => {
      // With HTTP-only cookies, we can't directly check if the cookie exists
      // We'll use the admin_authenticated flag as a UI indicator only
      const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';

      // If not authenticated according to UI flag, exit early
      if (!isAuthenticated) {
        if (isMounted) {
          setIsAdminAuthenticated(false);
          setIsAdminLoading(false);
        }
        return;
      }

      try {
        if (isMounted) setIsAdminLoading(true);

        // Try to get admin user data to verify authentication
        const response = await getCurrentAdmin();

        // Only update state if component is still mounted
        if (!isMounted) return;

        if (response && response.status === 'success' && response.data) {
          setAdminUser(response.data);
          setIsAdminAuthenticated(true);
        } else {
          // If API call succeeds but returns no data, clear authentication state
          setIsAdminAuthenticated(false);
          localStorage.removeItem('admin_authenticated');
        }
      } catch (error) {
        // Only update state if component is still mounted
        if (!isMounted) return;

        console.error('Error checking admin authentication:', error);

        // Handle authentication errors
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          setAdminError('Session expired. Please log in again.');
          setIsAdminAuthenticated(false);
          localStorage.removeItem('admin_authenticated');
        }
      } finally {
        // Only update state if component is still mounted
        if (isMounted) setIsAdminLoading(false);
      }
    };

    // Call the check function
    checkAdminAuth();

    // Cleanup function to set the mounted flag to false when the component unmounts
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array means this runs once on mount

  // Admin logout function
  const handleAdminLogout = async () => {
    try {
      // Call the API to clear the HTTP-only cookie on the server
      await adminLogout();

      // Clear UI state
      setAdminUser(null);
      setIsAdminAuthenticated(false);

      // Remove UI flags
      localStorage.removeItem('admin_authenticated');

      return { success: true };
    } catch (error) {
      console.error('Error during admin logout:', error);

      // Even if the API call fails, clear UI state
      setAdminUser(null);
      setIsAdminAuthenticated(false);
      localStorage.removeItem('admin_authenticated');

      setAdminError('Failed to log out. Please try again.');
      return { success: false, error };
    }
  };

  // Set admin user data after successful login
  const setAdminUserData = (userData) => {
    setAdminUser(userData);
    setIsAdminAuthenticated(true);
    localStorage.setItem('admin_authenticated', 'true');
  };

  // Admin token refresh function
  const handleAdminTokenRefresh = async () => {
    try {
      const response = await refreshAdminToken();
      return response && response.status === 'success';
    } catch (error) {
      console.error('Error refreshing admin token:', error);
      
      // Clear UI state on token refresh failure
      setAdminError('Session expired. Please log in again.');
      setIsAdminAuthenticated(false);
      localStorage.removeItem('admin_authenticated');
      
      return false;
    }
  };

  // Context value
  const value = {
    adminUser,
    isAdminAuthenticated,
    isAdminLoading,
    adminError,
    setAdminUserData,
    adminLogout: handleAdminLogout,
    refreshAdminToken: handleAdminTokenRefresh,
    clearAdminError: () => setAdminError(null)
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Custom hook to use the admin auth context
export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};
```

### 6. Update ProtectedAdminRoute.jsx

```jsx
// src/components/ProtectedAdminRoute.jsx
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAdminAuth } from '../context/AdminAuthContext';
import { ROUTES } from '../config';

const ProtectedAdminRoute = ({ children }) => {
  const { isAdminAuthenticated, isAdminLoading, adminError } = useAdminAuth();
  const location = useLocation();

  // Check if we have the authentication flag in localStorage
  const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';

  // Log authentication state for debugging
  useEffect(() => {
    console.log('ProtectedAdminRoute - Authentication check:');
    console.log('- Path:', location.pathname);
    console.log('- isAdminAuthenticated (context):', isAdminAuthenticated);
    console.log('- isAuthenticated (localStorage):', isAuthenticated);
    console.log('- isAdminLoading:', isAdminLoading);
    console.log('- adminError:', adminError);
  }, [isAdminAuthenticated, isAdminLoading, adminError, location.pathname, isAuthenticated]);

  // If we're still loading, show a loading indicator
  if (isAdminLoading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  // If we're not authenticated, redirect to login
  if (!isAdminAuthenticated && !isAuthenticated) {
    console.log('ProtectedAdminRoute - Not authenticated, redirecting to login');
    return <Navigate to={ROUTES.ADMIN_LOGIN} replace />;
  }

  // If we're authenticated, render the children
  console.log('ProtectedAdminRoute - Authenticated, rendering children');
  return children;
};

export default ProtectedAdminRoute;
```

### 7. Update AdminLayout.jsx

Simplify the AdminLayout component to avoid redundant authentication checks:

```jsx
// src/layouts/AdminLayout.jsx
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '../config';
import { useAdminAuth } from '../context/AdminAuthContext';
import './admin.css';

const AdminLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { adminLogout } = useAdminAuth();

  // Close mobile sidebar when route changes
  useEffect(() => {
    setMobileOpen(false);
  }, [location]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 992) {
        setSidebarCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = async () => {
    try {
      console.log('Logging out admin user');
      await adminLogout();
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    } catch (error) {
      console.error('Error during logout:', error);
      // Still navigate to login page even if logout fails
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    }
  };

  // Check if menu item is active
  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="admin-container">
      {/* Sidebar */}
      <aside className={`admin-sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${mobileOpen ? 'open' : ''}`}>
        {/* Sidebar content */}
      </aside>

      {/* Main Content */}
      <main className={`admin-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        {/* Header */}
        <header className="admin-header">
          {/* Header content */}
        </header>

        {/* Content */}
        <div className="admin-content">
          {children}
        </div>
      </main>
    </div>
  );
};

export default AdminLayout;
```

## Backend Changes to Check

Make sure your backend is properly configured for HTTP-only cookies:

1. **Cookie Configuration**: Ensure the backend sets the following cookie attributes:
   - `HttpOnly: true`
   - `Path: /`
   - `SameSite: Lax` (or `None` with `Secure: true` for cross-domain)
   - `Secure: true` (in production)

2. **CORS Configuration**: Ensure the backend allows credentials and specifies the correct origin:
   ```java
   configuration.setAllowedOrigins(Arrays.asList("http://localhost:3000"));
   configuration.setAllowCredentials(true);
   ```

3. **JWT Filter**: Ensure the JWT filter extracts tokens from cookies first, then falls back to the Authorization header.

## Testing Your Changes

1. **Clear Browser Data**: Clear cookies, localStorage, and sessionStorage before testing.

2. **Use Browser DevTools**: Monitor network requests and cookies during the authentication flow.

3. **Check for Errors**: Look for any errors in the browser console or server logs.

4. **Test the Complete Flow**: Go through the entire login process and verify that you can access protected routes.

## Debugging Tips

If you're still experiencing issues:

1. **Check Network Requests**: Look at the request and response headers for the `/api/admin/auth/login/verify` endpoint to ensure cookies are being set.

2. **Verify Cookie Presence**: Use the browser's Application tab to check if the JWT cookie is present after login.

3. **Test with Postman**: Use Postman to test the API endpoints directly, making sure to enable "Send cookies" in the request settings.

4. **Add Debug Logs**: Add more console logs to track the authentication flow.

5. **Check CORS Issues**: Look for CORS errors in the browser console.

## Production Considerations

1. **Enable HTTPS**: Always use HTTPS in production to protect cookies.

2. **Set Secure Flag**: Set the `Secure` flag to `true` for cookies in production.

3. **Configure SameSite**: Use `SameSite=Lax` or `SameSite=None` with `Secure=true` for cross-domain requests.

4. **Implement CSRF Protection**: Consider implementing CSRF protection for sensitive operations.

5. **Set Proper Cookie Domain**: Set the cookie domain to your production domain.

By implementing these changes, you should be able to fix the issues with HTTP-only cookies for admin authentication. The key is to ensure that the frontend doesn't try to manage tokens manually when using HTTP-only cookies, as the browser handles this automatically.
