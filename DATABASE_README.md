# Database Configuration Guide

This application supports different database initialization modes depending on your needs.

## Default Mode (Production)

By default, the application is configured to:
- Create tables only if they don't exist
- Never drop existing tables or data
- Apply schema changes using Hibernate's update mode

This is suitable for production environments where you want to preserve existing data.

## Development Mode

For development, you can use the `dev` profile to start with a clean database each time:

```
java -jar your-application.jar --spring.profiles.active=dev
```

Or when running from Maven:

```
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

The dev profile will:
- Drop all tables on startup
- Recreate the schema from scratch
- Load initial data

## Manual Database Reset

If you need to manually reset the database, you can:

1. Stop the application
2. Run the SQL commands in `schema.sql` directly against your database
3. Restart the application

## Adding New Tables or Columns

When adding new tables or columns:

1. Add them to both `schema.sql` and `schema-create-if-not-exists.sql`
2. For `schema-create-if-not-exists.sql`, use `CREATE TABLE IF NOT EXISTS` and `ALTER TABLE ... ADD COLUMN IF NOT EXISTS` syntax
3. For `schema.sql`, use the standard `CREATE TABLE` and `ALTER TABLE` syntax

## Troubleshooting

If you encounter database issues:

1. Check the application logs for SQL errors
2. Verify that your database user has sufficient privileges
3. For development, try using the `dev` profile to start with a clean database
4. For production issues, never use the `dev` profile as it will delete all data
