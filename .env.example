# Server configuration
SERVER_PORT=8080

# Database configuration
DB_URL=****************************************
DB_USERNAME=root
DB_PASSWORD=your_password

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT configuration
JWT_SECRET=your_secure_jwt_secret_key
JWT_EXPIRATION=3600000
JWT_REFRESH_EXPIRATION=86400000

# Email configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Frontend URLs
ALLOWED_ORIGIN=http://localhost:3000
REGISTRATION_URL=http://localhost:3000/complete-registration
PROFILE_URL=http://localhost:3000/profile
ERROR_URL=http://localhost:3000/error
VERIFICATION_URL=http://localhost:3000/verify
RESET_PASSWORD_URL=http://localhost:3000/reset-password

# Google OAuth2 configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google

# Facebook OAuth2 configuration
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
FACEBOOK_REDIRECT_URI=http://localhost:8080/oauth2/callback/facebook

# Logging configuration
LOG_LEVEL=DEBUG
