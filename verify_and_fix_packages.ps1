# Script to verify and fix package declarations and imports in all Java files

# Define the correct package mappings
$packageMappings = @{
    "src\main\java\com\pressmeet247\auth\controller" = "package com.pressmeet247.auth.controller;"
    "src\main\java\com\pressmeet247\auth\service" = "package com.pressmeet247.auth.service;"
    "src\main\java\com\pressmeet247\auth\dto\request" = "package com.pressmeet247.auth.dto.request;"
    "src\main\java\com\pressmeet247\auth\dto\response" = "package com.pressmeet247.auth.dto.response;"
    "src\main\java\com\pressmeet247\auth\entity" = "package com.pressmeet247.auth.entity;"
    "src\main\java\com\pressmeet247\auth\repository" = "package com.pressmeet247.auth.repository;"
    "src\main\java\com\pressmeet247\auth\exception" = "package com.pressmeet247.auth.exception;"
    "src\main\java\com\pressmeet247\auth\token" = "package com.pressmeet247.auth.token;"
    "src\main\java\com\pressmeet247\auth\util" = "package com.pressmeet247.auth.util;"
    
    "src\main\java\com\pressmeet247\admin\controller" = "package com.pressmeet247.admin.controller;"
    "src\main\java\com\pressmeet247\admin\service" = "package com.pressmeet247.admin.service;"
    "src\main\java\com\pressmeet247\admin\dto" = "package com.pressmeet247.admin.dto;"
    "src\main\java\com\pressmeet247\admin\entity" = "package com.pressmeet247.admin.entity;"
    "src\main\java\com\pressmeet247\admin\repository" = "package com.pressmeet247.admin.repository;"
    "src\main\java\com\pressmeet247\admin\config" = "package com.pressmeet247.admin.config;"
    
    "src\main\java\com\pressmeet247\ai_news\controller" = "package com.pressmeet247.ai_news.controller;"
    "src\main\java\com\pressmeet247\ai_news\service" = "package com.pressmeet247.ai_news.service;"
    "src\main\java\com\pressmeet247\ai_news\dto" = "package com.pressmeet247.ai_news.dto;"
    "src\main\java\com\pressmeet247\ai_news\entity" = "package com.pressmeet247.ai_news.entity;"
    "src\main\java\com\pressmeet247\ai_news\repository" = "package com.pressmeet247.ai_news.repository;"
    "src\main\java\com\pressmeet247\ai_news\impl" = "package com.pressmeet247.ai_news.impl;"
    
    "src\main\java\com\pressmeet247\subscription\controller" = "package com.pressmeet247.subscription.controller;"
    "src\main\java\com\pressmeet247\subscription\service" = "package com.pressmeet247.subscription.service;"
    "src\main\java\com\pressmeet247\subscription\dto" = "package com.pressmeet247.subscription.dto;"
    "src\main\java\com\pressmeet247\subscription\entity" = "package com.pressmeet247.subscription.entity;"
    "src\main\java\com\pressmeet247\subscription\repository" = "package com.pressmeet247.subscription.repository;"
    
    "src\main\java\com\pressmeet247\broadcast\controller" = "package com.pressmeet247.broadcast.controller;"
    "src\main\java\com\pressmeet247\broadcast\service" = "package com.pressmeet247.broadcast.service;"
    "src\main\java\com\pressmeet247\broadcast\dto" = "package com.pressmeet247.broadcast.dto;"
    "src\main\java\com\pressmeet247\broadcast\entity" = "package com.pressmeet247.broadcast.entity;"
    "src\main\java\com\pressmeet247\broadcast\repository" = "package com.pressmeet247.broadcast.repository;"
    
    "src\main\java\com\pressmeet247\email\service" = "package com.pressmeet247.email.service;"
    "src\main\java\com\pressmeet247\email\config" = "package com.pressmeet247.email.config;"
    
    "src\main\java\com\pressmeet247\common\config" = "package com.pressmeet247.common.config;"
    "src\main\java\com\pressmeet247\common\security" = "package com.pressmeet247.common.security;"
    "src\main\java\com\pressmeet247\common\jwt" = "package com.pressmeet247.common.jwt;"
    "src\main\java\com\pressmeet247\common\oauth2" = "package com.pressmeet247.common.oauth2;"
    "src\main\java\com\pressmeet247\common\exception" = "package com.pressmeet247.common.exception;"
    "src\main\java\com\pressmeet247\common\base" = "package com.pressmeet247.common.base;"
    "src\main\java\com\pressmeet247\common\util" = "package com.pressmeet247.common.util;"
    "src\main\java\com\pressmeet247\common\init" = "package com.pressmeet247.common.init;"
    
    "src\main\java\com\pressmeet247\config" = "package com.pressmeet247.config;"
    "src\main\java\com\pressmeet247" = "package com.pressmeet247;"
}

# Define import mappings for common classes
$importMappings = @{
    "com.auth.service" = "com.pressmeet247"
    "com.pressmeet247.entity" = "com.pressmeet247.auth.entity"
    "com.pressmeet247.repository" = "com.pressmeet247.auth.repository"
    "com.pressmeet247.dto.request" = "com.pressmeet247.auth.dto.request"
    "com.pressmeet247.dto.response" = "com.pressmeet247.auth.dto.response"
    "com.pressmeet247.service.auth" = "com.pressmeet247.auth.service"
    "com.pressmeet247.service.token" = "com.pressmeet247.auth.token"
    "com.pressmeet247.exception" = "com.pressmeet247.common.exception"
    "com.pressmeet247.config.jwt" = "com.pressmeet247.common.jwt"
    "com.pressmeet247.config.security" = "com.pressmeet247.common.security"
    "com.pressmeet247.config.oauth2" = "com.pressmeet247.common.oauth2"
    "com.pressmeet247.entity.base" = "com.pressmeet247.common.base"
    "com.pressmeet247.util" = "com.pressmeet247.common.util"
    "com.pressmeet247.service.email" = "com.pressmeet247.email.service"
    "com.pressmeet247.config.redis" = "com.pressmeet247.common.config.redis"
}

# Function to fix package declaration and imports in a file
function Fix-PackageAndImports {
    param (
        [string]$filePath
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        $fileDirectory = Split-Path -Parent $filePath
        $correctPackage = $null
        
        # Find the correct package for this file
        foreach ($dir in $packageMappings.Keys) {
            if ($fileDirectory -like "*$dir*") {
                $correctPackage = $packageMappings[$dir]
                break
            }
        }
        
        if ($correctPackage) {
            # Check if the package declaration is correct
            $packagePattern = "package\s+([^;]+);"
            $match = [regex]::Match($content, $packagePattern)
            
            if ($match.Success) {
                $currentPackage = $match.Value
                
                if ($currentPackage -ne $correctPackage) {
                    Write-Host "Fixing package in $filePath"
                    Write-Host "  From: $currentPackage"
                    Write-Host "  To:   $correctPackage"
                    
                    $content = $content -replace [regex]::Escape($currentPackage), $correctPackage
                }
            }
        }
        
        # Fix imports
        foreach ($oldImport in $importMappings.Keys) {
            $newImport = $importMappings[$oldImport]
            $importPattern = "import\s+$oldImport\."
            
            if ($content -match $importPattern) {
                Write-Host "Fixing imports in $filePath"
                Write-Host "  From: $oldImport"
                Write-Host "  To:   $newImport"
                
                $content = $content -replace $importPattern, "import $newImport."
            }
        }
        
        # Write the updated content back to the file
        Set-Content -Path $filePath -Value $content
    }
    else {
        Write-Host "File not found: $filePath"
    }
}

# Get all Java files in the new structure
$javaFiles = Get-ChildItem -Path "src\main\java\com\pressmeet247" -Filter "*.java" -Recurse

# Fix package declarations and imports in each file
foreach ($file in $javaFiles) {
    Fix-PackageAndImports -filePath $file.FullName
}

Write-Host "Package and import verification completed!"
