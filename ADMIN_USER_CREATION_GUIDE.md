# Admin User Creation Guide

This guide provides multiple methods to create an admin user with the following credentials:

- **Email**: <EMAIL>
- **Password**: admin@1234

## Method 1: Using the Java Tool

1. Run the provided batch file:
   ```
   create-admin-user.bat
   ```

   This will execute the `AdminUserCreationTool` class which creates the admin user programmatically.

## Method 2: Using SQL Script

1. Open your database management tool (e.g., MySQL Workbench, phpMyAdmin)
2. Connect to your application database
3. Execute the SQL script:
   ```sql
   -- create-admin-user.sql
   
   -- Make sure the ADMIN role exists
   INSERT INTO roles (name, description, is_default, priority, created_at, updated_at, deleted, version)
   SELECT 'ADMIN', 'Administrator role with full access', 0, 100, NOW(), NOW(), 0, 0
   WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'ADMIN');
   
   -- Create the admin user if it doesn't exist
   -- The password hash is for 'admin@1234'
   INSERT INTO users (first_name, last_name, email, password, enabled, email_verified, created_at, updated_at, deleted, version)
   SELECT 'Admin', 'User', '<EMAIL>', '$2a$10$Uj0Aw9Yx3jCkzG7Uo5As0.IfRcJCnZ0Hl4MvbVUXvCdkRZ5vXtGwC', 1, 1, NOW(), NOW(), 0, 0
   WHERE NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');
   
   -- Link the user to the ADMIN role
   INSERT INTO user_roles (user_id, role_id)
   SELECT 
       (SELECT id FROM users WHERE email = '<EMAIL>'),
       (SELECT id FROM roles WHERE name = 'ADMIN')
   WHERE NOT EXISTS (
       SELECT 1 FROM user_roles 
       WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')
       AND role_id = (SELECT id FROM roles WHERE name = 'ADMIN')
   );
   ```

## Method 3: Using the Admin Setup Profile

1. Add the following to your `application-admin-setup.properties` file:
   ```properties
   # Admin user configuration
   app.admin.email=<EMAIL>
   app.admin.password=admin@1234
   app.admin.firstName=Admin
   app.admin.lastName=User
   ```

2. Run your application with the admin-setup profile:
   ```
   java -jar your-application.jar --spring.profiles.active=admin-setup
   ```

## Verifying Admin User Creation

After creating the admin user, you can verify it by:

1. Logging in to the admin dashboard using the credentials:
   - Email: <EMAIL>
   - Password: admin@1234

2. Checking the database directly:
   ```sql
   SELECT u.id, u.email, u.enabled, u.email_verified, r.name as role_name
   FROM users u
   JOIN user_roles ur ON u.id = ur.user_id
   JOIN roles r ON ur.role_id = r.id
   WHERE u.email = '<EMAIL>';
   ```

## Security Note

The password in the SQL script is stored as a BCrypt hash. For security reasons:

1. Change the admin password after first login
2. Do not share these credentials
3. In production, use more secure methods to create admin users
