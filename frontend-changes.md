# Frontend Changes for HTTP-only Cookies

## 1. Update Axios Configuration

Make sure your Axios instance is configured to include credentials with all requests:

```javascript
// In your API service or axios configuration file
const api = axios.create({
  baseURL: 'http://localhost:8080/api', // Your backend URL
  withCredentials: true, // CRITICAL: This enables sending cookies with cross-origin requests
  headers: {
    'Content-Type': 'application/json',
  }
});
```

## 2. Remove Token Storage

Remove any code that stores tokens in localStorage or sessionStorage:

```javascript
// REMOVE code like this:
localStorage.setItem('token', response.data.accessToken);
// OR
sessionStorage.setItem('token', response.data.accessToken);
```

## 3. Remove Manual Token Headers

Remove any code that manually adds Authorization headers to requests:

```javascript
// REMOVE code like this:
const token = localStorage.getItem('token');
config.headers.Authorization = `Bearer ${token}`;
```

## 4. Update Login Function

Simplify your login function since cookies are handled automatically:

```javascript
const login = async (email, password) => {
  try {
    // The cookie will be automatically set by the server
    const response = await api.post('/auth/login', { email, password });
    
    // No need to extract and store the token
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Login failed' };
  }
};
```

## 5. Update Admin Login Function

For admin login, you need to handle the two-step process with OTP:

```javascript
// Step 1: Initiate login
const initiateAdminLogin = async (email, password) => {
  try {
    const response = await api.post('/api/admin/auth/login/initiate', { email, password });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Login initiation failed' };
  }
};

// Step 2: Verify OTP
const verifyAdminOtp = async (email, password, otp) => {
  try {
    const response = await api.post('/api/admin/auth/login/verify', { email, password, otp });
    
    // No need to store tokens - they're in cookies now
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'OTP verification failed' };
  }
};
```

## 6. Update Logout Function

Simplify your logout function:

```javascript
const logout = async () => {
  try {
    // This will clear the cookie on the server side
    await api.post('/auth/logout');
    
    // No need to remove token from localStorage
  } catch (error) {
    console.error('Logout error:', error);
  }
};
```

## 7. Add Token Refresh Logic

Add an interceptor to handle token refresh:

```javascript
// Add a response interceptor for automatic token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If error is 401 and not a retry, attempt to refresh token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // The refresh token is in the cookie, no need to send it
        await api.post('/api/auth/refresh');
        
        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);
```

## 8. Testing Your Changes

To test if the HTTP-only cookies are working correctly:

1. Open your browser's developer tools
2. Go to the "Application" tab (Chrome) or "Storage" tab (Firefox)
3. Look for "Cookies" in the sidebar
4. You should see the `jwt_token` cookie after logging in
5. The cookie should have the "HttpOnly" flag set to true

## 9. Debugging Tips

If you're having issues with authentication:

1. Check the browser console for any CORS errors
2. Use the `/api/auth/debug/status` endpoint to check authentication status
3. For admin endpoints, use `/api/admin/debug/auth-info` to check authentication details
4. Make sure `withCredentials: true` is set for all requests
5. Check that your backend CORS configuration allows credentials and your frontend origin
