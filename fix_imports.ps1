# Script to fix remaining import issues

# Function to update imports in a file
function Update-Imports {
    param (
        [string]$filePath
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Update imports for old package structure
        $content = $content -replace "import com.auth.service", "import com.pressmeet247"
        
        # Update specific imports for new module structure
        $content = $content -replace "import com.pressmeet247.entity", "import com.pressmeet247.auth.entity"
        $content = $content -replace "import com.pressmeet247.repository", "import com.pressmeet247.auth.repository"
        $content = $content -replace "import com.pressmeet247.dto", "import com.pressmeet247.auth.dto"
        $content = $content -replace "import com.pressmeet247.service.token", "import com.pressmeet247.auth.token"
        $content = $content -replace "import com.pressmeet247.service.auth", "import com.pressmeet247.auth.service"
        $content = $content -replace "import com.pressmeet247.service.email", "import com.pressmeet247.email.service"
        $content = $content -replace "import com.pressmeet247.service.ai", "import com.pressmeet247.ai_news.service"
        $content = $content -replace "import com.pressmeet247.config.redis", "import com.pressmeet247.common.config.redis"
        $content = $content -replace "import com.pressmeet247.config.jwt", "import com.pressmeet247.common.jwt"
        $content = $content -replace "import com.pressmeet247.config.oauth2", "import com.pressmeet247.common.oauth2"
        $content = $content -replace "import com.pressmeet247.config.security", "import com.pressmeet247.common.security"
        $content = $content -replace "import com.pressmeet247.exception", "import com.pressmeet247.common.exception"
        $content = $content -replace "import com.pressmeet247.entity.base", "import com.pressmeet247.common.base"
        $content = $content -replace "import com.pressmeet247.util", "import com.pressmeet247.common.util"
        
        # Write updated content back to file
        Set-Content -Path $filePath -Value $content
        
        Write-Host "Updated imports in $filePath"
    }
    else {
        Write-Host "File not found: $filePath"
    }
}

# Get all Java files in the new structure
$javaFiles = Get-ChildItem -Path "src\main\java\com\pressmeet247" -Filter "*.java" -Recurse

# Update imports in each file
foreach ($file in $javaFiles) {
    Update-Imports -filePath $file.FullName
}

Write-Host "Import fixes completed!"
