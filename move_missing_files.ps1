# Script to move missing files to the new modular structure

# Function to copy and update a file
function Copy-And-Update-File {
    param (
        [string]$sourceFile,
        [string]$targetFile,
        [string]$oldPackage,
        [string]$newPackage
    )

    if (Test-Path $sourceFile) {
        # Create target directory if it doesn't exist
        $targetDir = Split-Path -Parent $targetFile
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
            Write-Host "Created directory: $targetDir"
        }
        
        # Copy file
        Copy-Item -Path $sourceFile -Destination $targetFile -Force
        Write-Host "Copied $sourceFile to $targetFile"
        
        # Update package declaration
        $content = Get-Content $targetFile -Raw
        $content = $content -replace "package $oldPackage", "package $newPackage"
        
        # Update imports
        $content = $content -replace "import com.auth.service", "import com.pressmeet247"
        $content = $content -replace "import com.pressmeet247.entity", "import com.pressmeet247.auth.entity"
        $content = $content -replace "import com.pressmeet247.repository", "import com.pressmeet247.auth.repository"
        $content = $content -replace "import com.pressmeet247.dto.request", "import com.pressmeet247.auth.dto.request"
        $content = $content -replace "import com.pressmeet247.dto.response", "import com.pressmeet247.auth.dto.response"
        $content = $content -replace "import com.pressmeet247.service.token", "import com.pressmeet247.auth.token"
        $content = $content -replace "import com.pressmeet247.service.auth", "import com.pressmeet247.auth.service"
        $content = $content -replace "import com.pressmeet247.service.email", "import com.pressmeet247.email.service"
        $content = $content -replace "import com.pressmeet247.service.ai", "import com.pressmeet247.ai_news.service"
        $content = $content -replace "import com.pressmeet247.config.jwt", "import com.pressmeet247.common.jwt"
        $content = $content -replace "import com.pressmeet247.config.oauth2", "import com.pressmeet247.common.oauth2"
        $content = $content -replace "import com.pressmeet247.config.security", "import com.pressmeet247.common.security"
        $content = $content -replace "import com.pressmeet247.exception", "import com.pressmeet247.common.exception"
        $content = $content -replace "import com.pressmeet247.entity.base", "import com.pressmeet247.common.base"
        $content = $content -replace "import com.pressmeet247.util", "import com.pressmeet247.common.util"
        
        # Write updated content back to file
        Set-Content -Path $targetFile -Value $content
    }
    else {
        Write-Host "Source file not found: $sourceFile"
    }
}

# 1. Configuration Files
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\AdminRoleInitializer.java" -targetFile "src\main\java\com\pressmeet247\admin\config\AdminRoleInitializer.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.admin.config"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\AdminUserCreator.java" -targetFile "src\main\java\com\pressmeet247\admin\config\AdminUserCreator.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.admin.config"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\AuditingConfig.java" -targetFile "src\main\java\com\pressmeet247\common\config\AuditingConfig.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.common.config"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\AzureConfig.java" -targetFile "src\main\java\com\pressmeet247\common\config\AzureConfig.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.common.config"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\InMemoryRedisConfig.java" -targetFile "src\main\java\com\pressmeet247\common\config\redis\InMemoryRedisConfig.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.common.config.redis"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\RedisConfig.java" -targetFile "src\main\java\com\pressmeet247\common\config\redis\RedisConfig.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.common.config.redis"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\ThymeleafConfig.java" -targetFile "src\main\java\com\pressmeet247\email\config\ThymeleafConfig.java" -oldPackage "com.auth.service.config" -newPackage "com.pressmeet247.email.config"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\jwt\JwtProperties.java" -targetFile "src\main\java\com\pressmeet247\common\jwt\JwtProperties.java" -oldPackage "com.auth.service.config.jwt" -newPackage "com.pressmeet247.common.jwt"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\jwt\JwtUtils.java" -targetFile "src\main\java\com\pressmeet247\common\jwt\JwtUtils.java" -oldPackage "com.auth.service.config.jwt" -newPackage "com.pressmeet247.common.jwt"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\oauth2\OAuth2Properties.java" -targetFile "src\main\java\com\pressmeet247\common\oauth2\OAuth2Properties.java" -oldPackage "com.auth.service.config.oauth2" -newPackage "com.pressmeet247.common.oauth2"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\security\CustomAuthenticationProvider.java" -targetFile "src\main\java\com\pressmeet247\common\security\CustomAuthenticationProvider.java" -oldPackage "com.auth.service.config.security" -newPackage "com.pressmeet247.common.security"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\security\OAuth2SuccessHandler.java" -targetFile "src\main\java\com\pressmeet247\common\oauth2\OAuth2SuccessHandler.java" -oldPackage "com.auth.service.config.security" -newPackage "com.pressmeet247.common.oauth2"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\config\security\SecurityProperties.java" -targetFile "src\main\java\com\pressmeet247\common\security\SecurityProperties.java" -oldPackage "com.auth.service.config.security" -newPackage "com.pressmeet247.common.security"

# 2. Controllers
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\controller\AuthenticationController.java" -targetFile "src\main\java\com\pressmeet247\auth\controller\AuthenticationController.java" -oldPackage "com.auth.service.controller" -newPackage "com.pressmeet247.auth.controller"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\controller\HealthController.java" -targetFile "src\main\java\com\pressmeet247\common\controller\HealthController.java" -oldPackage "com.auth.service.controller" -newPackage "com.pressmeet247.common.controller"

# 3. DTOs
# Broadcast DTOs
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\BroadcastMessageRequest.java" -targetFile "src\main\java\com\pressmeet247\broadcast\dto\BroadcastMessageRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.broadcast.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\BroadcastMessageResponse.java" -targetFile "src\main\java\com\pressmeet247\broadcast\dto\BroadcastMessageResponse.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.broadcast.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\MessageDeliveryStatusResponse.java" -targetFile "src\main\java\com\pressmeet247\broadcast\dto\MessageDeliveryStatusResponse.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.broadcast.dto"

# News DTOs
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\NewsGenerateRequest.java" -targetFile "src\main\java\com\pressmeet247\ai_news\dto\NewsGenerateRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.ai_news.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\NewsResponse.java" -targetFile "src\main\java\com\pressmeet247\ai_news\dto\NewsResponse.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.ai_news.dto"

# Subscription DTOs
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionPlanRequest.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionPlanRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionPlanResponse.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionPlanResponse.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionPlanUpdateRequest.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionPlanUpdateRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionRenewalRequest.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionRenewalRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionRequest.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionRequest.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\SubscriptionResponse.java" -targetFile "src\main\java\com\pressmeet247\subscription\dto\SubscriptionResponse.java" -oldPackage "com.auth.service.dto" -newPackage "com.pressmeet247.subscription.dto"

# Auth Request DTOs
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\AdminLoginRequest.java" -targetFile "src\main\java\com\pressmeet247\admin\dto\AdminLoginRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.admin.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\AdminOtpVerificationRequest.java" -targetFile "src\main\java\com\pressmeet247\admin\dto\AdminOtpVerificationRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.admin.dto"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\EmailRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\EmailRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\OtpVerificationRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\OtpVerificationRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\PasswordResetConfirmRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\PasswordResetConfirmRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\PasswordResetRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\PasswordResetRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\RefreshTokenRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\RefreshTokenRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\request\SetPasswordRequest.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\request\SetPasswordRequest.java" -oldPackage "com.auth.service.dto.request" -newPackage "com.pressmeet247.auth.dto.request"

# Auth Response DTOs
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\dto\response\UserResponse.java" -targetFile "src\main\java\com\pressmeet247\auth\dto\response\UserResponse.java" -oldPackage "com.auth.service.dto.response" -newPackage "com.pressmeet247.auth.dto.response"

# 4. Entities
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\AccountVerification.java" -targetFile "src\main\java\com\pressmeet247\auth\entity\AccountVerification.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.auth.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\BroadcastMessageType.java" -targetFile "src\main\java\com\pressmeet247\broadcast\entity\BroadcastMessageType.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.broadcast.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\LoginHistory.java" -targetFile "src\main\java\com\pressmeet247\auth\entity\LoginHistory.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.auth.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\MessageDeliveryStatus.java" -targetFile "src\main\java\com\pressmeet247\broadcast\entity\MessageDeliveryStatus.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.broadcast.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\MessageDeliveryStatusType.java" -targetFile "src\main\java\com\pressmeet247\broadcast\entity\MessageDeliveryStatusType.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.broadcast.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\NewsStatus.java" -targetFile "src\main\java\com\pressmeet247\ai_news\entity\NewsStatus.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.ai_news.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\ResetPasswordToken.java" -targetFile "src\main\java\com\pressmeet247\auth\entity\ResetPasswordToken.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.auth.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\SecurityEvent.java" -targetFile "src\main\java\com\pressmeet247\auth\entity\SecurityEvent.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.auth.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\SecurityEventType.java" -targetFile "src\main\java\com\pressmeet247\auth\entity\SecurityEventType.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.auth.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\SubscriptionPlan.java" -targetFile "src\main\java\com\pressmeet247\subscription\entity\SubscriptionPlan.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.subscription.entity"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\entity\SubscriptionStatus.java" -targetFile "src\main\java\com\pressmeet247\subscription\entity\SubscriptionStatus.java" -oldPackage "com.auth.service.entity" -newPackage "com.pressmeet247.subscription.entity"

# 5. Exceptions
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\exception\ResourceNotFoundException.java" -targetFile "src\main\java\com\pressmeet247\common\exception\ResourceNotFoundException.java" -oldPackage "com.auth.service.exception" -newPackage "com.pressmeet247.common.exception"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\exception\TokenException.java" -targetFile "src\main\java\com\pressmeet247\auth\exception\TokenException.java" -oldPackage "com.auth.service.exception" -newPackage "com.pressmeet247.auth.exception"

# 6. Repositories
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\AccountVerificationRepository.java" -targetFile "src\main\java\com\pressmeet247\auth\repository\AccountVerificationRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.auth.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\LoginHistoryRepository.java" -targetFile "src\main\java\com\pressmeet247\auth\repository\LoginHistoryRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.auth.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\MessageDeliveryStatusRepository.java" -targetFile "src\main\java\com\pressmeet247\broadcast\repository\MessageDeliveryStatusRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.broadcast.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\ResetPasswordTokenRepository.java" -targetFile "src\main\java\com\pressmeet247\auth\repository\ResetPasswordTokenRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.auth.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\SecurityEventRepository.java" -targetFile "src\main\java\com\pressmeet247\auth\repository\SecurityEventRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.auth.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\SubscriptionPlanRepository.java" -targetFile "src\main\java\com\pressmeet247\subscription\repository\SubscriptionPlanRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.subscription.repository"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\repository\UserPreferencesRepository.java" -targetFile "src\main\java\com\pressmeet247\auth\repository\UserPreferencesRepository.java" -oldPackage "com.auth.service.repository" -newPackage "com.pressmeet247.auth.repository"

# 7. Services
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\AIService.java" -targetFile "src\main\java\com\pressmeet247\ai_news\service\AIService.java" -oldPackage "com.auth.service.service" -newPackage "com.pressmeet247.ai_news.service"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\auth\CustomUserDetailsService.java" -targetFile "src\main\java\com\pressmeet247\auth\service\CustomUserDetailsService.java" -oldPackage "com.auth.service.service.auth" -newPackage "com.pressmeet247.auth.service"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\auth\OAuth2UserServiceImpl.java" -targetFile "src\main\java\com\pressmeet247\auth\service\OAuth2UserServiceImpl.java" -oldPackage "com.auth.service.service.auth" -newPackage "com.pressmeet247.auth.service"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\email\ProdEmailService.java" -targetFile "src\main\java\com\pressmeet247\email\service\ProdEmailService.java" -oldPackage "com.auth.service.service.email" -newPackage "com.pressmeet247.email.service"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\impl\AIServiceImpl.java" -targetFile "src\main\java\com\pressmeet247\ai_news\impl\AIServiceImpl.java" -oldPackage "com.auth.service.service.impl" -newPackage "com.pressmeet247.ai_news.impl"
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\service\user\UserService.java" -targetFile "src\main\java\com\pressmeet247\auth\service\UserService.java" -oldPackage "com.auth.service.service.user" -newPackage "com.pressmeet247.auth.service"

# 8. Utils
Copy-And-Update-File -sourceFile "src\main\java\com\auth\service\util\UserSecurity.java" -targetFile "src\main\java\com\pressmeet247\auth\util\UserSecurity.java" -oldPackage "com.auth.service.util" -newPackage "com.pressmeet247.auth.util"

# 9. Create common/controller directory for HealthController
New-Item -ItemType Directory -Path "src\main\java\com\pressmeet247\common\controller" -Force | Out-Null

Write-Host "Migration of missing files completed!"
