-- Drop all tables in the correct order to avoid foreign key constraints
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS oauth_tokens;
DROP TABLE IF EXISTS reset_password_tokens;
DROP TABLE IF EXISTS account_verifications;
DROP TABLE IF EXISTS login_history;
DROP TABLE IF EXISTS security_events;
DROP TABLE IF EXISTS user_preferences;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS roles;

SET FOREIGN_KEY_CHECKS = 1;

-- Create roles table
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at DATETIME(6) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at DATETIME(6),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by <PERSON><PERSON>HA<PERSON>(255),
    version INTEGER,
    is_default BIT NOT NULL DEFAULT 0,
    priority INTEGER NOT NULL DEFAULT 100
);

-- Create users table
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    enabled BIT DEFAULT 0,
    email_verified BIT DEFAULT 0,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    last_login_at DATETIME(6),
    last_password_change_at DATETIME(6),
    failed_login_attempts INT DEFAULT 0,
    locked_until DATETIME(6),
    profile_image_url VARCHAR(255),
    phone_number VARCHAR(20),
    phone_verified BIT DEFAULT 0,
    two_factor_enabled BIT DEFAULT 0,
    two_factor_secret VARCHAR(100)
);

-- Create user_roles table
CREATE TABLE user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Create account_verifications table
CREATE TABLE account_verifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    token VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expires_at DATETIME(6) NOT NULL,
    verified_at DATETIME(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create reset_password_tokens table
CREATE TABLE reset_password_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expiration_time DATETIME(6) NOT NULL,
    used_at DATETIME(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create oauth_tokens table
CREATE TABLE oauth_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    access_token TEXT NOT NULL,
    refresh_token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expiration_time DATETIME(6) NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    revoked BIT DEFAULT 0,
    revoked_at DATETIME(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create login_history table
CREATE TABLE login_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    attempt_time DATETIME(6) NOT NULL,
    successful BIT NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    failure_reason VARCHAR(100),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create security_events table
CREATE TABLE security_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    event_type VARCHAR(50) NOT NULL,
    description VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    additional_data TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create user_preferences table
CREATE TABLE user_preferences (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    language VARCHAR(10) DEFAULT 'en-US',
    timezone VARCHAR(50) DEFAULT 'UTC',
    date_format VARCHAR(20) DEFAULT 'MM/dd/yyyy',
    time_format VARCHAR(20) DEFAULT 'HH:mm:ss',
    theme VARCHAR(20) DEFAULT 'light',
    notifications_enabled BIT DEFAULT 1,
    email_notifications BIT DEFAULT 1,
    sms_notifications BIT DEFAULT 0,
    push_notifications BIT DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_created_at ON users(created_at);
CREATE INDEX idx_user_deleted ON users(deleted);

CREATE INDEX idx_role_name ON roles(name);
CREATE INDEX idx_role_deleted ON roles(deleted);

CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);

CREATE INDEX idx_account_verification_user_id ON account_verifications(user_id);
CREATE INDEX idx_account_verification_token ON account_verifications(token);
CREATE INDEX idx_account_verification_expires_at ON account_verifications(expires_at);
CREATE INDEX idx_account_verification_deleted ON account_verifications(deleted);

CREATE INDEX idx_reset_password_token_token ON reset_password_tokens(token);
CREATE INDEX idx_reset_password_token_user_id ON reset_password_tokens(user_id);
CREATE INDEX idx_reset_password_token_expiration_time ON reset_password_tokens(expiration_time);
CREATE INDEX idx_reset_password_token_deleted ON reset_password_tokens(deleted);

CREATE INDEX idx_oauth_token_refresh_token ON oauth_tokens(refresh_token);
CREATE INDEX idx_oauth_token_user_id ON oauth_tokens(user_id);
CREATE INDEX idx_oauth_token_expiration_time ON oauth_tokens(expiration_time);
CREATE INDEX idx_oauth_token_deleted ON oauth_tokens(deleted);

CREATE INDEX idx_login_history_user_id ON login_history(user_id);
CREATE INDEX idx_login_history_attempt_time ON login_history(attempt_time);
CREATE INDEX idx_login_history_successful ON login_history(successful);
CREATE INDEX idx_login_history_deleted ON login_history(deleted);

CREATE INDEX idx_security_events_user_id ON security_events(user_id);
CREATE INDEX idx_security_events_event_type ON security_events(event_type);
CREATE INDEX idx_security_events_created_at ON security_events(created_at);
CREATE INDEX idx_security_events_deleted ON security_events(deleted);

CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX idx_user_preferences_deleted ON user_preferences(deleted);
