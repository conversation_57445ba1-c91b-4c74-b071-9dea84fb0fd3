spring:
  datasource:
    url: ${JDBC_DATABASE_URL}
    username: ${JDBC_DATABASE_USERNAME}
    password: ${JDBC_DATABASE_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
    show-sql: false
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

server:
  port: ${PORT:8080}
  error:
    include-stacktrace: never
  servlet:
    context-path: /

logging:
  level:
    root: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    com.pressmeet247: INFO

# JWT Configuration
jwt:
  secret: ${JWT_SECRET}
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days
  cookie:
    secure: true
    http-only: true
    same-site: none

# CORS Configuration
cors:
  allowed-origins: ${ALLOWED_ORIGINS}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: Authorization,Content-Type,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
  exposed-headers: Authorization,Content-Disposition
  allow-credentials: true
  max-age: 3600

# OpenAPI Configuration
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# Application-specific configuration
app:
  frontend-url: ${FRONTEND_URL}
  admin:
    email: ${ADMIN_EMAIL}
    password: ${ADMIN_PASSWORD}
  file-storage:
    location: ${FILE_STORAGE_LOCATION:/tmp/uploads}
  email:
    enabled: true
    from: ${EMAIL_FROM}
    reply-to: ${EMAIL_REPLY_TO}
  security:
    require-email-verification: true
