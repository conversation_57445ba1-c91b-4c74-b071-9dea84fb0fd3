<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4285f4;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .button {
            display: inline-block;
            background-color: #4285f4;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Reset Your Password</h1>
    </div>
    <div class="content">
        <p>Hello <span th:text="${name}">User</span>,</p>

        <p>We received a request to reset your password. Click the button below to create a new password:</p>

        <p style="text-align: center;">
            <a href="#" style="display: inline-block; background-color: #4285f4; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 20px 0; font-weight: bold;" th:href="${resetLink}">Reset Password</a>
        </p>

        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>

        <p style="word-break: break-all;" th:text="${resetLink}">Reset Link</p>

        <p>This link will expire in 24 hours.</p>

        <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>

        <p>Best regards,<br>The Auth Service Team</p>
    </div>
    <div class="footer">
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>&copy; 2023 Auth Service. All rights reserved.</p>
    </div>
</body>
</html>
