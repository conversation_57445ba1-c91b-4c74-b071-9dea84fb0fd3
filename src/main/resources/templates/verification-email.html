<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Account</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4285f4;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .button {
            display: inline-block;
            background-color: #4285f4;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Verify Your Account</h1>
    </div>
    <div class="content">
        <p>Hello <span th:text="${name}">User</span>,</p>

        <p>Thank you for registering! Please verify your account by clicking the button below:</p>

        <p style="text-align: center;">
            <a href="#" style="display: inline-block; background-color: #4285f4; color: white; text-decoration: none; padding: 15px 30px; border-radius: 5px; margin: 20px 0; font-weight: bold; font-size: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);" th:href="${verificationLink}">Verify My Account</a>
        </p>
        <p style="text-align: center; color: #666;">
            After verification, you will be redirected to the login page.
        </p>
        <p style="text-align: center; font-size: 12px; color: #888;">
            If the button doesn't work, copy and paste this link into your browser: <br>
            <span th:text="${verificationLink}" style="word-break: break-all;"></span>
        </p>

        <p>This link will expire in 24 hours.</p>

        <p>If you didn't register for an account, please ignore this email.</p>

        <p>Best regards,<br>The Auth Service Team</p>
    </div>
    <div class="footer">
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>&copy; 2023 Auth Service. All rights reserved.</p>
    </div>
</body>
</html>
