-- Create roles table if not exists
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at DATETIME(6) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at DATETIME(6),
    updated_by VARC<PERSON><PERSON>(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VA<PERSON><PERSON><PERSON>(255),
    version INTEGER,
    is_default BIT NOT NULL DEFAULT 0,
    priority INTEGER NOT NULL DEFAULT 100
);

-- Create users table if not exists
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    enabled BIT DEFAULT 0,
    email_verified BIT DEFAULT 0,
    created_at DATETIME(6) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at DATETIME(6),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    last_login_at DATETIME(6),
    last_password_change_at DATETIME(6),
    failed_login_attempts INT DEFAULT 0,
    locked_until DATETIME(6),
    profile_image_url VARCHAR(255),
    phone_number VARCHAR(20),
    phone_verified BIT DEFAULT 0,
    two_factor_enabled BIT DEFAULT 0,
    two_factor_secret VARCHAR(100)
);

-- Create user_roles table if not exists
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Create account_verifications table if not exists
CREATE TABLE IF NOT EXISTS account_verifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    token VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expires_at DATETIME(6) NOT NULL,
    verified_at DATETIME(6),
    otp_code VARCHAR(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create reset_password_tokens table if not exists
CREATE TABLE IF NOT EXISTS reset_password_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expiration_time DATETIME(6) NOT NULL,
    used_at DATETIME(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create oauth_tokens table if not exists
CREATE TABLE IF NOT EXISTS oauth_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    access_token TEXT NOT NULL,
    refresh_token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    expiration_time DATETIME(6) NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    revoked BIT DEFAULT 0,
    revoked_at DATETIME(6),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create login_history table if not exists
CREATE TABLE IF NOT EXISTS login_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    attempt_time DATETIME(6) NOT NULL,
    successful BIT NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    failure_reason VARCHAR(100),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create security_events table if not exists
CREATE TABLE IF NOT EXISTS security_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    event_type VARCHAR(50) NOT NULL,
    description VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    device_type VARCHAR(50),
    location VARCHAR(100),
    additional_data TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create user_preferences table if not exists
CREATE TABLE IF NOT EXISTS user_preferences (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    language VARCHAR(10) DEFAULT 'en-US',
    timezone VARCHAR(50) DEFAULT 'UTC',
    date_format VARCHAR(20) DEFAULT 'MM/dd/yyyy',
    time_format VARCHAR(20) DEFAULT 'HH:mm:ss',
    theme VARCHAR(20) DEFAULT 'light',
    notifications_enabled BIT DEFAULT 1,
    email_notifications BIT DEFAULT 1,
    sms_notifications BIT DEFAULT 0,
    push_notifications BIT DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create news table if not exists
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(20) NOT NULL,
    news_type VARCHAR(50),
    featured BIT DEFAULT 0,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    published_at DATETIME(6),
    published_by BIGINT,
    rejected_at DATETIME(6),
    rejected_by BIGINT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes if they don't exist
-- Note: MySQL doesn't support IF NOT EXISTS for indexes, so we need to check if they exist first

-- User indexes
CREATE INDEX IF NOT EXISTS idx_user_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_user_deleted ON users(deleted);

-- Role indexes
CREATE INDEX IF NOT EXISTS idx_role_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_role_deleted ON roles(deleted);

-- User roles indexes
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);

-- Account verification indexes
CREATE INDEX IF NOT EXISTS idx_account_verification_user_id ON account_verifications(user_id);
CREATE INDEX IF NOT EXISTS idx_account_verification_token ON account_verifications(token);
CREATE INDEX IF NOT EXISTS idx_account_verification_expires_at ON account_verifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_account_verification_deleted ON account_verifications(deleted);

-- Reset password token indexes
CREATE INDEX IF NOT EXISTS idx_reset_password_token_token ON reset_password_tokens(token);
CREATE INDEX IF NOT EXISTS idx_reset_password_token_user_id ON reset_password_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_reset_password_token_expiration_time ON reset_password_tokens(expiration_time);
CREATE INDEX IF NOT EXISTS idx_reset_password_token_deleted ON reset_password_tokens(deleted);

-- OAuth token indexes
CREATE INDEX IF NOT EXISTS idx_oauth_token_refresh_token ON oauth_tokens(refresh_token);
CREATE INDEX IF NOT EXISTS idx_oauth_token_user_id ON oauth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_token_expiration_time ON oauth_tokens(expiration_time);
CREATE INDEX IF NOT EXISTS idx_oauth_token_deleted ON oauth_tokens(deleted);

-- Login history indexes
CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_login_history_attempt_time ON login_history(attempt_time);
CREATE INDEX IF NOT EXISTS idx_login_history_successful ON login_history(successful);
CREATE INDEX IF NOT EXISTS idx_login_history_deleted ON login_history(deleted);

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_deleted ON security_events(deleted);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_deleted ON user_preferences(deleted);

-- News indexes
CREATE INDEX IF NOT EXISTS idx_news_user_id ON news(user_id);
CREATE INDEX IF NOT EXISTS idx_news_status ON news(status);
CREATE INDEX IF NOT EXISTS idx_news_news_type ON news(news_type);
CREATE INDEX IF NOT EXISTS idx_news_featured ON news(featured);
CREATE INDEX IF NOT EXISTS idx_news_created_at ON news(created_at);
CREATE INDEX IF NOT EXISTS idx_news_deleted ON news(deleted);
