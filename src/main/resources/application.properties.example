# Email Configuration
# To enable email sending, create a copy of this file named application.properties
# and update the values below with your email provider details

# Gmail SMTP Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000

# Note: To use Gmail SMTP, you need to:
# 1. Enable 2-Step Verification on your Google account
# 2. Generate an App Password: Google Account > Security > App Passwords
# 3. Use that App Password here instead of your regular password

# Outlook/Office365 SMTP Configuration
# spring.mail.host=smtp.office365.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-password
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# Yahoo SMTP Configuration
# spring.mail.host=smtp.mail.yahoo.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-password
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true
