spring:
  # Email configuration - using Gmail SMTP
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>  # Replace with your Gmail address
    password: your-app-password     # Replace with your Gmail app password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

# Note: To use Gmail SMTP, you need to:
# 1. Enable 2-Step Verification on your Google account
# 2. Generate an App Password: Google Account > Security > App Passwords
# 3. Use that App Password here instead of your regular password
#
# Instructions:
# 1. Copy this file to application-mail.yml
# 2. Replace the username and password with your own
# 3. The application-mail.yml file is gitignored, so your credentials won't be committed
