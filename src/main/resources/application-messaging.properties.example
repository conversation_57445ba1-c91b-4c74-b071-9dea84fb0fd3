# Messaging Configuration Example
# Copy this file to application-messaging.properties and configure with your actual values

# =============================================================================
# TWILIO WHATSAPP CONFIGURATION
# =============================================================================
# Enable/disable Twilio WhatsApp service
app.twilio.enabled=true

# Twilio Account SID (get from Twilio Console)
app.twilio.account-sid=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Twilio Auth Token (get from Twilio Console)
app.twilio.auth-token=your_auth_token_here

# Twilio WhatsApp From Number (your WhatsApp Business number)
# Format: whatsapp:+**********
app.twilio.whatsapp-from=whatsapp:+***********

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email service configuration (already configured in main application.properties)
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your_app_password

# From email address for messaging
app.email.from=<EMAIL>

# =============================================================================
# MESSAGING RETRY CONFIGURATION
# =============================================================================
# Maximum number of retry attempts for failed messages
app.messaging.retry.max-attempts=3

# Delay between retry attempts (in minutes)
app.messaging.retry.delay-minutes=5

# Enable/disable async message processing
app.messaging.async.enabled=true

# =============================================================================
# MESSAGE RATE LIMITING
# =============================================================================
# Maximum messages per minute per admin (future enhancement)
app.messaging.rate-limit.per-minute=60

# Maximum broadcast recipients per request
app.messaging.broadcast.max-recipients=1000

# =============================================================================
# TWILIO SETUP INSTRUCTIONS
# =============================================================================
# 1. Create a Twilio account at https://www.twilio.com/
# 2. Get your Account SID and Auth Token from the Twilio Console
# 3. Set up WhatsApp Business API:
#    - Go to Twilio Console > Messaging > Try it out > Send a WhatsApp message
#    - Follow the setup instructions for WhatsApp Business API
#    - Get your WhatsApp-enabled phone number
# 4. Update the configuration above with your actual values
# 5. Test with a small group before broadcasting to all users

# =============================================================================
# WHATSAPP BUSINESS API NOTES
# =============================================================================
# - WhatsApp Business API requires approval for production use
# - During sandbox testing, you can only send messages to verified numbers
# - Message templates may be required for certain types of messages
# - Rate limits apply based on your WhatsApp Business API tier

# =============================================================================
# ENVIRONMENT VARIABLES (RECOMMENDED FOR PRODUCTION)
# =============================================================================
# Instead of hardcoding values, use environment variables:
# app.twilio.account-sid=${TWILIO_ACCOUNT_SID}
# app.twilio.auth-token=${TWILIO_AUTH_TOKEN}
# app.twilio.whatsapp-from=${TWILIO_WHATSAPP_FROM}

# =============================================================================
# SECURITY CONSIDERATIONS
# =============================================================================
# - Never commit actual credentials to version control
# - Use environment variables or external configuration in production
# - Rotate credentials regularly
# - Monitor usage and costs in Twilio Console
# - Set up billing alerts to avoid unexpected charges
