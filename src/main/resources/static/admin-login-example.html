<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Admin Login Example</h1>
    
    <div class="container" id="loginContainer">
        <h2>Step 1: Initiate Admin Login</h2>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="Enter admin email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter password" value="admin@1234">
        </div>
        <button onclick="initiateLogin()">Initiate Login</button>
        <div id="loginResult" class="result hidden"></div>
    </div>
    
    <div class="container hidden" id="otpContainer">
        <h2>Step 2: Verify OTP</h2>
        <div class="form-group">
            <label for="otp">OTP Code:</label>
            <input type="text" id="otp" placeholder="Enter 6-digit OTP">
        </div>
        <button onclick="verifyOtp()">Verify OTP</button>
        <div id="otpResult" class="result hidden"></div>
    </div>
    
    <div class="container hidden" id="dashboardContainer">
        <h2>Admin Dashboard</h2>
        <p>You have successfully logged in as an admin.</p>
        <div id="dashboardData" class="result"></div>
        <button onclick="fetchDashboardData()">Fetch Dashboard Data</button>
    </div>
    
    <script>
        // Store user credentials and tokens
        const userData = {
            email: '',
            password: '',
            accessToken: '',
            tokenType: '',
            redirectUrl: ''
        };
        
        // Base URL for API calls
        const baseUrl = 'http://localhost:8080';
        
        // Initiate admin login
        async function initiateLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginResult = document.getElementById('loginResult');
            
            if (!email || !password) {
                loginResult.innerHTML = '<span class="error">Please enter email and password</span>';
                loginResult.classList.remove('hidden');
                return;
            }
            
            // Store credentials for later use
            userData.email = email;
            userData.password = password;
            
            loginResult.innerHTML = 'Initiating login...';
            loginResult.classList.remove('hidden');
            
            try {
                const response = await fetch(`${baseUrl}/api/admin/auth/login/initiate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    loginResult.innerHTML = `<span class="success">${data.message}</span>`;
                    
                    // Show OTP container
                    document.getElementById('otpContainer').classList.remove('hidden');
                    
                    // Focus on OTP input
                    document.getElementById('otp').focus();
                } else {
                    loginResult.innerHTML = `<span class="error">${data.message || 'Login initiation failed'}</span>`;
                }
            } catch (error) {
                loginResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
        
        // Verify OTP
        async function verifyOtp() {
            const otp = document.getElementById('otp').value;
            const otpResult = document.getElementById('otpResult');
            
            if (!otp) {
                otpResult.innerHTML = '<span class="error">Please enter OTP</span>';
                otpResult.classList.remove('hidden');
                return;
            }
            
            otpResult.innerHTML = 'Verifying OTP...';
            otpResult.classList.remove('hidden');
            
            try {
                const response = await fetch(`${baseUrl}/api/admin/auth/login/verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: userData.email,
                        password: userData.password,
                        otp: otp
                    })
                });
                
                // Check for redirect headers
                userData.redirectUrl = response.headers.get('X-Auth-Redirect');
                userData.accessToken = response.headers.get('X-Auth-Token');
                userData.tokenType = response.headers.get('X-Auth-Token-Type');
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    otpResult.innerHTML = `<span class="success">${data.message}</span>`;
                    
                    // Store tokens from response body as fallback
                    if (!userData.accessToken && data.data && data.data.authResponse) {
                        userData.accessToken = data.data.authResponse.accessToken;
                        userData.tokenType = data.data.authResponse.tokenType;
                    }
                    
                    // Store redirect URL from response body as fallback
                    if (!userData.redirectUrl && data.data && data.data.redirectUrl) {
                        userData.redirectUrl = data.data.redirectUrl;
                    }
                    
                    // Show dashboard container
                    document.getElementById('dashboardContainer').classList.remove('hidden');
                    
                    // Fetch dashboard data
                    fetchDashboardData();
                } else {
                    otpResult.innerHTML = `<span class="error">${data.message || 'OTP verification failed'}</span>`;
                }
            } catch (error) {
                otpResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
        
        // Fetch dashboard data
        async function fetchDashboardData() {
            const dashboardData = document.getElementById('dashboardData');
            
            dashboardData.innerHTML = 'Fetching dashboard data...';
            
            try {
                const response = await fetch(`${baseUrl}/api/admin/dashboard`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `${userData.tokenType} ${userData.accessToken}`
                    }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    dashboardData.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    dashboardData.innerHTML = `<span class="error">${data.message || 'Failed to fetch dashboard data'}</span>`;
                }
            } catch (error) {
                dashboardData.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
