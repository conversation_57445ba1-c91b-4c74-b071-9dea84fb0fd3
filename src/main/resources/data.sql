-- Insert roles
INSERT INTO roles (name, description, is_default, priority, created_at, deleted)
VALUES ('USER', 'Regular user with basic privileges', 1, 100, NOW(), 0);

INSERT INTO roles (name, description, is_default, priority, created_at, deleted)
VALUES ('ADMIN', 'Administrator with full privileges', 0, 1, NOW(), 0);

-- Create news table if it doesn't exist
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    date VARCHAR(50),
    place VARCHAR(255),
    what_happened TEXT,
    where_happened VARCHAR(255),
    who_involved <PERSON><PERSON><PERSON><PERSON>(255),
    when_happened VARCHAR(100),
    who_told <PERSON><PERSON><PERSON><PERSON>(255),
    how_happened TEXT,
    why_happened TEXT,
    news_type VARCHAR(50),
    status VARCHAR(20) NOT NULL,
    published_at DATETIME(6),
    approved_by B<PERSON>IN<PERSON>,
    approved_at DATETIME(6),
    featured BIT DEFAULT 0,
    created_at DATETIME(6) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Create subscription_plans table if it doesn't exist
CREATE TABLE IF NOT EXISTS subscription_plans (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    duration_months INT NOT NULL,
    active BIT DEFAULT 1,
    features TEXT,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER
);

-- Create subscriptions table if it doesn't exist
CREATE TABLE IF NOT EXISTS subscriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    plan_id BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_date DATETIME(6) NOT NULL,
    end_date DATETIME(6) NOT NULL,
    auto_renew BIT DEFAULT 1,
    last_payment_date DATETIME(6),
    next_payment_date DATETIME(6),
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
);

-- Create broadcast_messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS broadcast_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(20) NOT NULL,
    target_all_users BIT DEFAULT 0,
    target_premium_users BIT DEFAULT 0,
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    FOREIGN KEY (sender_id) REFERENCES users(id)
);

-- Create broadcast_message_recipients table if it doesn't exist
CREATE TABLE IF NOT EXISTS broadcast_message_recipients (
    message_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    PRIMARY KEY (message_id, user_id),
    FOREIGN KEY (message_id) REFERENCES broadcast_messages(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create message_delivery_statuses table if it doesn't exist
CREATE TABLE IF NOT EXISTS message_delivery_statuses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL,
    delivered_at DATETIME(6),
    read_at DATETIME(6),
    error_message VARCHAR(255),
    created_at DATETIME(6) NOT NULL,
    created_by VARCHAR(255),
    updated_at DATETIME(6),
    updated_by VARCHAR(255),
    deleted BIT DEFAULT 0,
    deleted_at DATETIME(6),
    deleted_by VARCHAR(255),
    version INTEGER,
    FOREIGN KEY (message_id) REFERENCES broadcast_messages(id),
    FOREIGN KEY (recipient_id) REFERENCES users(id)
);

-- Insert default subscription plans if they don't exist
INSERT INTO subscription_plans (name, description, price, currency, duration_months, active, features, created_at, updated_at, deleted, version)
SELECT 'Free', 'Basic access to the platform', 0.00, 'USD', 0, 1, '{"canReadNews": true, "canSubmitNews": true, "prioritySupport": false, "exclusiveContent": false}', NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM subscription_plans WHERE name = 'Free');

INSERT INTO subscription_plans (name, description, price, currency, duration_months, active, features, created_at, updated_at, deleted, version)
SELECT 'Premium Monthly', 'Premium access with exclusive features', 9.99, 'USD', 1, 1, '{"canReadNews": true, "canSubmitNews": true, "prioritySupport": true, "exclusiveContent": true}', NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM subscription_plans WHERE name = 'Premium Monthly');

INSERT INTO subscription_plans (name, description, price, currency, duration_months, active, features, created_at, updated_at, deleted, version)
SELECT 'Premium Annual', 'Premium access with exclusive features and annual discount', 99.99, 'USD', 12, 1, '{"canReadNews": true, "canSubmitNews": true, "prioritySupport": true, "exclusiveContent": true}', NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM subscription_plans WHERE name = 'Premium Annual');
