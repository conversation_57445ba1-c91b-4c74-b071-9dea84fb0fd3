package com.pressmeet247.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for component scanning.
 * This ensures that all modules are properly detected by Spring.
 */
@Configuration
@ComponentScan(basePackages = {
        "com.pressmeet247.auth",
        "com.pressmeet247.admin",
        "com.pressmeet247.ai_news",
        "com.pressmeet247.subscription",
        "com.pressmeet247.email",
        "com.pressmeet247.broadcast",
        "com.pressmeet247.common"
})
public class ComponentScanConfig {
    // Configuration class to ensure all modules are scanned
}


