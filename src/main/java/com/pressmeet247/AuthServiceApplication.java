package com.pressmeet247;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

/**
 * Main application class for the Authentication Service.
 * This service provides comprehensive authentication and authorization functionality
 * including user registration, login, OAuth2 integration, and JWT token management.
 *
 * Database initialization is configured to:
 * - Create tables only if they don't exist (using CREATE TABLE IF NOT EXISTS)
 * - Never drop existing tables or data
 * - Apply schema changes using Hibernate's update mode
 *
 * For development purposes, you can use the 'dev' profile to start with a clean database:
 * java -jar your-application.jar --spring.profiles.active=dev
 */
@Slf4j
@SpringBootApplication(scanBasePackages = "com.pressmeet247")
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class AuthServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }

    /**
     * Logs application startup information.
     *
     * @return CommandLineRunner
     */
    @Bean
    public CommandLineRunner logStartupInfo() {
        return args -> {
            log.info("\n\n=====================================================");
            log.info("  Authentication Service Started Successfully");
            log.info("=====================================================");
            log.info("Admin Dashboard API Endpoints:");
            log.info("  - /api/admin/dashboard/stats");
            log.info("  - /api/v1/admin/dashboard/stats");
            log.info("Debug Endpoints:");
            log.info("  - /api/admin/debug/auth-info");
            log.info("  - /api/admin/debug/admin-only");
            log.info("  - /api/admin/debug/public");
            log.info("Debug UI:");
            log.info("  - /admin-debug.html");
            log.info("=====================================================");
        };
    }

    /**
     * Provides the current auditor (user) for JPA auditing.
     * This is used to automatically populate createdBy and updatedBy fields.
     *
     * @return AuditorAware implementation that gets the current authenticated user
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return Optional.of("system");
            }
            return Optional.of(authentication.getName());
        };
    }
}


