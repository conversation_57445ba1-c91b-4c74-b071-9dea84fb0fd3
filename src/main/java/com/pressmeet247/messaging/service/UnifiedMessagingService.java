package com.pressmeet247.messaging.service;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.EmailService;
import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import com.pressmeet247.messaging.repository.MessageLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Unified messaging service that handles both email and WhatsApp messaging.
 * Provides comprehensive messaging functionality with logging and tracking.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnifiedMessagingService {

    private final EmailService emailService;
    private final WhatsAppService whatsAppService;
    private final UserRepository userRepository;
    private final MessageLogRepository messageLogRepository;

    /**
     * Sends a message to a single user via specified channel.
     *
     * @param senderAdminId ID of the admin sending the message
     * @param recipientUserId ID of the recipient user
     * @param channel Message channel (EMAIL or WHATSAPP)
     * @param messageType Type of message
     * @param subject Message subject (for email)
     * @param content Message content
     * @return MessageLog of the sent message
     */
    @Transactional
    public MessageLog sendMessageToUser(Long senderAdminId, Long recipientUserId, 
                                       MessageChannel channel, MessageType messageType,
                                       String subject, String content) {
        
        User senderAdmin = userRepository.findById(senderAdminId)
                .orElseThrow(() -> new RuntimeException("Sender admin not found: " + senderAdminId));
        
        User recipient = userRepository.findById(recipientUserId)
                .orElseThrow(() -> new RuntimeException("Recipient user not found: " + recipientUserId));

        return sendMessageToUserInternal(senderAdmin, recipient, channel, messageType, subject, content, false, null);
    }

    /**
     * Sends messages to multiple users via specified channel.
     *
     * @param senderAdminId ID of the admin sending the message
     * @param recipientUserIds List of recipient user IDs
     * @param channel Message channel
     * @param messageType Type of message
     * @param subject Message subject
     * @param content Message content
     * @return List of MessageLogs for sent messages
     */
    @Transactional
    public List<MessageLog> sendMessageToMultipleUsers(Long senderAdminId, List<Long> recipientUserIds,
                                                       MessageChannel channel, MessageType messageType,
                                                       String subject, String content) {
        
        User senderAdmin = userRepository.findById(senderAdminId)
                .orElseThrow(() -> new RuntimeException("Sender admin not found: " + senderAdminId));
        
        List<User> recipients = userRepository.findAllById(recipientUserIds);
        String broadcastId = UUID.randomUUID().toString();

        return recipients.stream()
                .map(recipient -> sendMessageToUserInternal(senderAdmin, recipient, channel, messageType, 
                                                          subject, content, true, broadcastId))
                .toList();
    }

    /**
     * Sends broadcast message to all users via specified channel.
     *
     * @param senderAdminId ID of the admin sending the message
     * @param channel Message channel
     * @param messageType Type of message
     * @param subject Message subject
     * @param content Message content
     * @return List of MessageLogs for sent messages
     */
    @Transactional
    public List<MessageLog> sendBroadcastMessage(Long senderAdminId, MessageChannel channel, 
                                                MessageType messageType, String subject, String content) {
        
        User senderAdmin = userRepository.findById(senderAdminId)
                .orElseThrow(() -> new RuntimeException("Sender admin not found: " + senderAdminId));
        
        List<User> allUsers = userRepository.findAll();
        String broadcastId = UUID.randomUUID().toString();

        return allUsers.stream()
                .map(recipient -> sendMessageToUserInternal(senderAdmin, recipient, channel, messageType, 
                                                          subject, content, true, broadcastId))
                .toList();
    }

    /**
     * Sends broadcast message to users of specific type.
     *
     * @param senderAdminId ID of the admin sending the message
     * @param userType Target user type
     * @param channel Message channel
     * @param messageType Type of message
     * @param subject Message subject
     * @param content Message content
     * @return List of MessageLogs for sent messages
     */
    @Transactional
    public List<MessageLog> sendBroadcastMessageToUserType(Long senderAdminId, UserType userType,
                                                           MessageChannel channel, MessageType messageType,
                                                           String subject, String content) {
        
        User senderAdmin = userRepository.findById(senderAdminId)
                .orElseThrow(() -> new RuntimeException("Sender admin not found: " + senderAdminId));
        
        List<User> targetUsers = userRepository.findByUserType(userType);
        String broadcastId = UUID.randomUUID().toString();

        return targetUsers.stream()
                .map(recipient -> sendMessageToUserInternal(senderAdmin, recipient, channel, messageType, 
                                                          subject, content, true, broadcastId))
                .toList();
    }

    /**
     * Internal method to send message to a single user.
     */
    private MessageLog sendMessageToUserInternal(User senderAdmin, User recipient, MessageChannel channel,
                                                MessageType messageType, String subject, String content,
                                                boolean isBroadcast, String broadcastId) {
        
        // Create message log entry
        MessageLog messageLog = new MessageLog();
        messageLog.setSenderAdmin(senderAdmin);
        messageLog.setRecipientUser(recipient);
        messageLog.setChannel(channel);
        messageLog.setMessageType(messageType);
        messageLog.setSubject(subject);
        messageLog.setContent(content);
        messageLog.setIsBroadcast(isBroadcast);
        messageLog.setBroadcastId(broadcastId);
        messageLog.setStatus(MessageStatus.PENDING);

        // Set recipient identifier based on channel
        String recipientIdentifier = switch (channel) {
            case EMAIL -> recipient.getEmail();
            case WHATSAPP, SMS -> recipient.getPhoneNumber();
            default -> recipient.getEmail();
        };
        messageLog.setRecipientIdentifier(recipientIdentifier);

        // Save message log
        messageLog = messageLogRepository.save(messageLog);

        // Send message asynchronously
        sendMessageAsync(messageLog);

        return messageLog;
    }

    /**
     * Sends message asynchronously based on channel.
     */
    @Async
    public void sendMessageAsync(MessageLog messageLog) {
        try {
            String result = null;
            
            switch (messageLog.getChannel()) {
                case EMAIL -> {
                    emailService.sendSimpleEmail(
                            messageLog.getRecipientIdentifier(),
                            messageLog.getSubject(),
                            messageLog.getContent()
                    );
                    result = "EMAIL_SENT";
                }
                case WHATSAPP -> {
                    result = whatsAppService.sendWhatsAppMessage(
                            messageLog.getRecipientIdentifier(),
                            messageLog.getContent()
                    );
                }
                case SMS -> {
                    // SMS implementation can be added here
                    log.warn("SMS channel not yet implemented");
                    result = null;
                }
            }

            // Update message log status
            if (result != null) {
                messageLog.setExternalMessageId(result);
                messageLog.markAsSent();
                log.info("Message sent successfully via {}: {}", messageLog.getChannel(), result);
            } else {
                messageLog.markAsFailed("Failed to send message via " + messageLog.getChannel());
                log.error("Failed to send message via {}", messageLog.getChannel());
            }

        } catch (Exception e) {
            messageLog.markAsFailed("Exception: " + e.getMessage());
            log.error("Exception while sending message: {}", e.getMessage(), e);
        } finally {
            messageLogRepository.save(messageLog);
        }
    }

    /**
     * Gets message history for an admin.
     *
     * @param adminId Admin user ID
     * @param pageable Pagination information
     * @return Page of message logs
     */
    public Page<MessageLog> getMessageHistory(Long adminId, Pageable pageable) {
        User admin = userRepository.findById(adminId)
                .orElseThrow(() -> new RuntimeException("Admin not found: " + adminId));
        return messageLogRepository.findBySenderAdmin(admin, pageable);
    }

    /**
     * Gets messages received by a user.
     *
     * @param userId User ID
     * @param pageable Pagination information
     * @return Page of message logs
     */
    public Page<MessageLog> getReceivedMessages(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found: " + userId));
        return messageLogRepository.findByRecipientUser(user, pageable);
    }

    /**
     * Gets message statistics.
     *
     * @return Message statistics
     */
    public MessageStatistics getMessageStatistics() {
        return MessageStatistics.builder()
                .totalMessages(messageLogRepository.count())
                .sentMessages(messageLogRepository.countByStatus(MessageStatus.SENT))
                .deliveredMessages(messageLogRepository.countByStatus(MessageStatus.DELIVERED))
                .failedMessages(messageLogRepository.countByStatus(MessageStatus.FAILED))
                .emailMessages(messageLogRepository.countByChannel(MessageChannel.EMAIL))
                .whatsappMessages(messageLogRepository.countByChannel(MessageChannel.WHATSAPP))
                .build();
    }

    /**
     * Inner class for message statistics.
     */
    public static class MessageStatistics {
        public final long totalMessages;
        public final long sentMessages;
        public final long deliveredMessages;
        public final long failedMessages;
        public final long emailMessages;
        public final long whatsappMessages;

        private MessageStatistics(long totalMessages, long sentMessages, long deliveredMessages,
                                 long failedMessages, long emailMessages, long whatsappMessages) {
            this.totalMessages = totalMessages;
            this.sentMessages = sentMessages;
            this.deliveredMessages = deliveredMessages;
            this.failedMessages = failedMessages;
            this.emailMessages = emailMessages;
            this.whatsappMessages = whatsappMessages;
        }

        public static MessageStatisticsBuilder builder() {
            return new MessageStatisticsBuilder();
        }

        public static class MessageStatisticsBuilder {
            private long totalMessages;
            private long sentMessages;
            private long deliveredMessages;
            private long failedMessages;
            private long emailMessages;
            private long whatsappMessages;

            public MessageStatisticsBuilder totalMessages(long totalMessages) {
                this.totalMessages = totalMessages;
                return this;
            }

            public MessageStatisticsBuilder sentMessages(long sentMessages) {
                this.sentMessages = sentMessages;
                return this;
            }

            public MessageStatisticsBuilder deliveredMessages(long deliveredMessages) {
                this.deliveredMessages = deliveredMessages;
                return this;
            }

            public MessageStatisticsBuilder failedMessages(long failedMessages) {
                this.failedMessages = failedMessages;
                return this;
            }

            public MessageStatisticsBuilder emailMessages(long emailMessages) {
                this.emailMessages = emailMessages;
                return this;
            }

            public MessageStatisticsBuilder whatsappMessages(long whatsappMessages) {
                this.whatsappMessages = whatsappMessages;
                return this;
            }

            public MessageStatistics build() {
                return new MessageStatistics(totalMessages, sentMessages, deliveredMessages,
                                           failedMessages, emailMessages, whatsappMessages);
            }
        }
    }
}
