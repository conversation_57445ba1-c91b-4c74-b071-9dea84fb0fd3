package com.pressmeet247.messaging.service;

import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.repository.MessageLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for retrying failed messages.
 * Automatically retries failed messages based on configuration.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MessageRetryService {

    private final MessageLogRepository messageLogRepository;
    private final UnifiedMessagingService messagingService;

    @Value("${app.messaging.retry.max-attempts:3}")
    private int maxRetryAttempts;

    @Value("${app.messaging.retry.delay-minutes:5}")
    private int retryDelayMinutes;

    /**
     * Scheduled task to retry failed messages.
     * Runs every 10 minutes to check for failed messages that can be retried.
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    @Transactional
    public void retryFailedMessages() {
        log.info("Starting retry process for failed messages");

        try {
            List<MessageLog> failedMessages = messageLogRepository.findFailedMessagesForRetry(maxRetryAttempts);
            
            if (failedMessages.isEmpty()) {
                log.debug("No failed messages found for retry");
                return;
            }

            log.info("Found {} failed messages to retry", failedMessages.size());

            for (MessageLog messageLog : failedMessages) {
                if (shouldRetryMessage(messageLog)) {
                    retryMessage(messageLog);
                }
            }

        } catch (Exception e) {
            log.error("Error during message retry process: {}", e.getMessage(), e);
        }
    }

    /**
     * Manually retries a specific message.
     *
     * @param messageLogId Message log ID to retry
     * @return true if retry was attempted, false otherwise
     */
    @Transactional
    public boolean retryMessage(Long messageLogId) {
        MessageLog messageLog = messageLogRepository.findById(messageLogId).orElse(null);
        
        if (messageLog == null) {
            log.warn("Message log not found for retry: {}", messageLogId);
            return false;
        }

        if (!shouldRetryMessage(messageLog)) {
            log.warn("Message {} should not be retried", messageLogId);
            return false;
        }

        return retryMessage(messageLog);
    }

    /**
     * Retries a message.
     *
     * @param messageLog Message log to retry
     * @return true if retry was attempted, false otherwise
     */
    private boolean retryMessage(MessageLog messageLog) {
        try {
            log.info("Retrying message {} (attempt {} of {})", 
                    messageLog.getId(), messageLog.getRetryCount() + 1, maxRetryAttempts);

            // Increment retry count
            messageLog.incrementRetryCount();
            messageLog.setStatus(MessageStatus.PENDING);
            messageLog.setErrorMessage(null);
            
            // Save the updated message log
            messageLogRepository.save(messageLog);

            // Retry sending the message
            messagingService.sendMessageAsync(messageLog);

            log.info("Message {} queued for retry", messageLog.getId());
            return true;

        } catch (Exception e) {
            log.error("Error retrying message {}: {}", messageLog.getId(), e.getMessage(), e);
            messageLog.markAsFailed("Retry failed: " + e.getMessage());
            messageLogRepository.save(messageLog);
            return false;
        }
    }

    /**
     * Determines if a message should be retried.
     *
     * @param messageLog Message log to check
     * @return true if message should be retried, false otherwise
     */
    private boolean shouldRetryMessage(MessageLog messageLog) {
        // Check if message is in failed status
        if (messageLog.getStatus() != MessageStatus.FAILED) {
            return false;
        }

        // Check if retry count is below maximum
        if (messageLog.getRetryCount() >= maxRetryAttempts) {
            log.debug("Message {} has reached maximum retry attempts", messageLog.getId());
            return false;
        }

        // Check if enough time has passed since last attempt
        LocalDateTime lastAttempt = messageLog.getUpdatedAt();
        if (lastAttempt != null) {
            LocalDateTime nextRetryTime = lastAttempt.plusMinutes(retryDelayMinutes);
            if (LocalDateTime.now().isBefore(nextRetryTime)) {
                log.debug("Message {} is not ready for retry yet", messageLog.getId());
                return false;
            }
        }

        return true;
    }

    /**
     * Gets retry statistics.
     *
     * @return Retry statistics
     */
    public RetryStatistics getRetryStatistics() {
        long totalFailedMessages = messageLogRepository.countByStatus(MessageStatus.FAILED);
        List<MessageLog> retryableMessages = messageLogRepository.findFailedMessagesForRetry(maxRetryAttempts);
        
        return RetryStatistics.builder()
                .totalFailedMessages(totalFailedMessages)
                .retryableMessages(retryableMessages.size())
                .maxRetryAttempts(maxRetryAttempts)
                .retryDelayMinutes(retryDelayMinutes)
                .build();
    }

    /**
     * Cancels retry for a specific message.
     *
     * @param messageLogId Message log ID
     * @return true if cancelled, false otherwise
     */
    @Transactional
    public boolean cancelRetry(Long messageLogId) {
        MessageLog messageLog = messageLogRepository.findById(messageLogId).orElse(null);
        
        if (messageLog == null) {
            log.warn("Message log not found for cancel retry: {}", messageLogId);
            return false;
        }

        if (messageLog.getStatus() == MessageStatus.FAILED) {
            messageLog.setStatus(MessageStatus.CANCELLED);
            messageLog.setErrorMessage("Retry cancelled by admin");
            messageLogRepository.save(messageLog);
            
            log.info("Retry cancelled for message {}", messageLogId);
            return true;
        }

        return false;
    }

    /**
     * Resets retry count for a message.
     *
     * @param messageLogId Message log ID
     * @return true if reset, false otherwise
     */
    @Transactional
    public boolean resetRetryCount(Long messageLogId) {
        MessageLog messageLog = messageLogRepository.findById(messageLogId).orElse(null);
        
        if (messageLog == null) {
            log.warn("Message log not found for reset retry: {}", messageLogId);
            return false;
        }

        messageLog.setRetryCount(0);
        messageLog.setErrorMessage(null);
        messageLogRepository.save(messageLog);
        
        log.info("Retry count reset for message {}", messageLogId);
        return true;
    }

    /**
     * Inner class for retry statistics.
     */
    public static class RetryStatistics {
        public final long totalFailedMessages;
        public final long retryableMessages;
        public final int maxRetryAttempts;
        public final int retryDelayMinutes;

        private RetryStatistics(long totalFailedMessages, long retryableMessages,
                               int maxRetryAttempts, int retryDelayMinutes) {
            this.totalFailedMessages = totalFailedMessages;
            this.retryableMessages = retryableMessages;
            this.maxRetryAttempts = maxRetryAttempts;
            this.retryDelayMinutes = retryDelayMinutes;
        }

        public static RetryStatisticsBuilder builder() {
            return new RetryStatisticsBuilder();
        }

        public static class RetryStatisticsBuilder {
            private long totalFailedMessages;
            private long retryableMessages;
            private int maxRetryAttempts;
            private int retryDelayMinutes;

            public RetryStatisticsBuilder totalFailedMessages(long totalFailedMessages) {
                this.totalFailedMessages = totalFailedMessages;
                return this;
            }

            public RetryStatisticsBuilder retryableMessages(long retryableMessages) {
                this.retryableMessages = retryableMessages;
                return this;
            }

            public RetryStatisticsBuilder maxRetryAttempts(int maxRetryAttempts) {
                this.maxRetryAttempts = maxRetryAttempts;
                return this;
            }

            public RetryStatisticsBuilder retryDelayMinutes(int retryDelayMinutes) {
                this.retryDelayMinutes = retryDelayMinutes;
                return this;
            }

            public RetryStatistics build() {
                return new RetryStatistics(totalFailedMessages, retryableMessages, 
                                         maxRetryAttempts, retryDelayMinutes);
            }
        }
    }
}
