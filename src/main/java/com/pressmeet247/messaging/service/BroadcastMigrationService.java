package com.pressmeet247.messaging.service;

import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.repository.BroadcastMessageRepository;
import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import com.pressmeet247.messaging.repository.MessageLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * Service to migrate existing broadcast messages to the new unified messaging system.
 * This allows for gradual transition from old to new system.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BroadcastMigrationService {

    private final BroadcastMessageRepository broadcastMessageRepository;
    private final MessageLogRepository messageLogRepository;

    /**
     * Migrates all existing broadcast messages to the new MessageLog system.
     *
     * @return Number of messages migrated
     */
    @Transactional
    public int migrateAllBroadcastMessages() {
        log.info("Starting migration of broadcast messages to unified messaging system");
        
        List<BroadcastMessage> broadcastMessages = broadcastMessageRepository.findAll();
        int migratedCount = 0;

        for (BroadcastMessage broadcastMessage : broadcastMessages) {
            try {
                migrateBroadcastMessage(broadcastMessage);
                migratedCount++;
            } catch (Exception e) {
                log.error("Failed to migrate broadcast message {}: {}", 
                         broadcastMessage.getId(), e.getMessage(), e);
            }
        }

        log.info("Migration completed. {} broadcast messages migrated", migratedCount);
        return migratedCount;
    }

    /**
     * Migrates a single broadcast message to MessageLog entries.
     *
     * @param broadcastMessage Broadcast message to migrate
     */
    @Transactional
    public void migrateBroadcastMessage(BroadcastMessage broadcastMessage) {
        String broadcastId = UUID.randomUUID().toString();
        
        // Convert broadcast message type to new message type
        MessageType messageType = convertBroadcastType(broadcastMessage.getType().toString());
        
        // Create MessageLog entries for each delivery status
        broadcastMessage.getDeliveryStatuses().forEach(deliveryStatus -> {
            MessageLog messageLog = new MessageLog();
            messageLog.setSenderAdmin(broadcastMessage.getSender());
            messageLog.setRecipientUser(deliveryStatus.getRecipient());
            messageLog.setChannel(MessageChannel.EMAIL); // Old system was email only
            messageLog.setMessageType(messageType);
            messageLog.setSubject(broadcastMessage.getTitle());
            messageLog.setContent(broadcastMessage.getContent());
            messageLog.setRecipientIdentifier(deliveryStatus.getRecipient().getEmail());
            messageLog.setStatus(convertDeliveryStatus(deliveryStatus.getStatus().toString()));
            messageLog.setSentAt(deliveryStatus.getCreatedAt());
            messageLog.setDeliveredAt(deliveryStatus.getDeliveredAt());
            messageLog.setReadAt(deliveryStatus.getReadAt());
            messageLog.setErrorMessage(deliveryStatus.getErrorMessage());
            messageLog.setIsBroadcast(true);
            messageLog.setBroadcastId(broadcastId);
            
            // Copy audit fields
            messageLog.setCreatedAt(broadcastMessage.getCreatedAt());
            messageLog.setUpdatedAt(broadcastMessage.getUpdatedAt());
            
            messageLogRepository.save(messageLog);
        });
        
        log.info("Migrated broadcast message {} with {} recipients", 
                broadcastMessage.getId(), broadcastMessage.getDeliveryStatuses().size());
    }

    /**
     * Converts old broadcast message type to new message type.
     */
    private MessageType convertBroadcastType(String oldType) {
        return switch (oldType.toUpperCase()) {
            case "ANNOUNCEMENT" -> MessageType.ANNOUNCEMENT;
            case "NOTIFICATION" -> MessageType.NOTIFICATION;
            case "MARKETING" -> MessageType.MARKETING;
            case "REMINDER" -> MessageType.REMINDER;
            case "ALERT" -> MessageType.ALERT;
            default -> MessageType.CUSTOM;
        };
    }

    /**
     * Converts old delivery status to new message status.
     */
    private MessageStatus convertDeliveryStatus(String oldStatus) {
        return switch (oldStatus.toUpperCase()) {
            case "PENDING" -> MessageStatus.PENDING;
            case "SENT" -> MessageStatus.SENT;
            case "DELIVERED" -> MessageStatus.DELIVERED;
            case "READ" -> MessageStatus.READ;
            case "FAILED" -> MessageStatus.FAILED;
            default -> MessageStatus.PENDING;
        };
    }

    /**
     * Creates a new broadcast message using the unified messaging system
     * while maintaining compatibility with the old BroadcastMessage entity.
     *
     * @param broadcastMessage Broadcast message to create
     * @return List of created MessageLog entries
     */
    @Transactional
    public List<MessageLog> createUnifiedBroadcast(BroadcastMessage broadcastMessage) {
        // This method can be used to create new broadcasts using the unified system
        // while still saving to the old BroadcastMessage table for compatibility
        
        // Implementation would depend on specific requirements
        // This is a placeholder for future enhancement
        
        log.info("Creating unified broadcast for message: {}", broadcastMessage.getTitle());
        return List.of(); // Placeholder
    }

    /**
     * Gets migration statistics.
     *
     * @return Migration statistics
     */
    public MigrationStatistics getMigrationStatistics() {
        long totalBroadcastMessages = broadcastMessageRepository.count();
        long totalMessageLogs = messageLogRepository.count();
        long broadcastMessageLogs = messageLogRepository.countByIsBroadcast(true);
        
        return MigrationStatistics.builder()
                .totalBroadcastMessages(totalBroadcastMessages)
                .totalMessageLogs(totalMessageLogs)
                .migratedBroadcastMessages(broadcastMessageLogs)
                .migrationPercentage(totalBroadcastMessages > 0 ? 
                    (double) broadcastMessageLogs / totalBroadcastMessages * 100 : 0.0)
                .build();
    }

    /**
     * Inner class for migration statistics.
     */
    public static class MigrationStatistics {
        public final long totalBroadcastMessages;
        public final long totalMessageLogs;
        public final long migratedBroadcastMessages;
        public final double migrationPercentage;

        private MigrationStatistics(long totalBroadcastMessages, long totalMessageLogs,
                                   long migratedBroadcastMessages, double migrationPercentage) {
            this.totalBroadcastMessages = totalBroadcastMessages;
            this.totalMessageLogs = totalMessageLogs;
            this.migratedBroadcastMessages = migratedBroadcastMessages;
            this.migrationPercentage = migrationPercentage;
        }

        public static MigrationStatisticsBuilder builder() {
            return new MigrationStatisticsBuilder();
        }

        public static class MigrationStatisticsBuilder {
            private long totalBroadcastMessages;
            private long totalMessageLogs;
            private long migratedBroadcastMessages;
            private double migrationPercentage;

            public MigrationStatisticsBuilder totalBroadcastMessages(long totalBroadcastMessages) {
                this.totalBroadcastMessages = totalBroadcastMessages;
                return this;
            }

            public MigrationStatisticsBuilder totalMessageLogs(long totalMessageLogs) {
                this.totalMessageLogs = totalMessageLogs;
                return this;
            }

            public MigrationStatisticsBuilder migratedBroadcastMessages(long migratedBroadcastMessages) {
                this.migratedBroadcastMessages = migratedBroadcastMessages;
                return this;
            }

            public MigrationStatisticsBuilder migrationPercentage(double migrationPercentage) {
                this.migrationPercentage = migrationPercentage;
                return this;
            }

            public MigrationStatistics build() {
                return new MigrationStatistics(totalBroadcastMessages, totalMessageLogs,
                                             migratedBroadcastMessages, migrationPercentage);
            }
        }
    }
}
