package com.pressmeet247.messaging.service;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Service for sending WhatsApp messages using Twilio.
 */
@Service
@Slf4j
public class WhatsAppService {

    @Value("${app.twilio.account-sid:}")
    private String accountSid;

    @Value("${app.twilio.auth-token:}")
    private String authToken;

    @Value("${app.twilio.whatsapp-from:}")
    private String whatsappFrom;

    @Value("${app.twilio.enabled:false}")
    private boolean twilioEnabled;

    /**
     * Initializes Twilio client if enabled.
     */
    private void initializeTwilio() {
        if (twilioEnabled && accountSid != null && authToken != null && !accountSid.isEmpty() && !authToken.isEmpty()) {
            Twilio.init(accountSid, authToken);
        }
    }

    /**
     * Sends a WhatsApp message to a phone number.
     *
     * @param to Phone number (with country code, e.g., +**********)
     * @param messageBody Message content
     * @return Message SID if successful, null if failed
     */
    public String sendWhatsAppMessage(String to, String messageBody) {
        if (!twilioEnabled) {
            log.warn("Twilio is not enabled. WhatsApp message not sent to: {}", to);
            return null;
        }

        if (accountSid == null || authToken == null || whatsappFrom == null || 
            accountSid.isEmpty() || authToken.isEmpty() || whatsappFrom.isEmpty()) {
            log.error("Twilio configuration is incomplete. Cannot send WhatsApp message.");
            return null;
        }

        try {
            initializeTwilio();

            // Format phone numbers for WhatsApp
            String formattedTo = formatWhatsAppNumber(to);
            String formattedFrom = formatWhatsAppNumber(whatsappFrom);

            log.info("Sending WhatsApp message to: {} from: {}", formattedTo, formattedFrom);

            Message message = Message.creator(
                    new PhoneNumber(formattedTo),
                    new PhoneNumber(formattedFrom),
                    messageBody
            ).create();

            log.info("WhatsApp message sent successfully. SID: {}", message.getSid());
            return message.getSid();

        } catch (Exception e) {
            log.error("Failed to send WhatsApp message to {}: {}", to, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Sends a WhatsApp message with a template (for future enhancement).
     *
     * @param to Phone number
     * @param templateName Template name
     * @param templateParams Template parameters
     * @return Message SID if successful, null if failed
     */
    public String sendWhatsAppTemplate(String to, String templateName, String... templateParams) {
        // This is a placeholder for template-based messaging
        // Twilio WhatsApp templates require approval and specific formatting
        log.info("Template messaging not yet implemented. Sending as regular message.");
        
        // For now, send as regular message
        String messageBody = String.format("Template: %s with params: %s", 
                templateName, String.join(", ", templateParams));
        return sendWhatsAppMessage(to, messageBody);
    }

    /**
     * Formats phone number for WhatsApp (adds whatsapp: prefix).
     *
     * @param phoneNumber Phone number
     * @return Formatted WhatsApp number
     */
    private String formatWhatsAppNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            throw new IllegalArgumentException("Phone number cannot be null or empty");
        }

        // Remove any existing whatsapp: prefix
        String cleanNumber = phoneNumber.replace("whatsapp:", "");
        
        // Ensure the number starts with +
        if (!cleanNumber.startsWith("+")) {
            cleanNumber = "+" + cleanNumber;
        }

        // Add WhatsApp prefix
        return "whatsapp:" + cleanNumber;
    }

    /**
     * Validates if WhatsApp service is properly configured.
     *
     * @return true if configured, false otherwise
     */
    public boolean isConfigured() {
        return twilioEnabled && 
               accountSid != null && !accountSid.isEmpty() &&
               authToken != null && !authToken.isEmpty() &&
               whatsappFrom != null && !whatsappFrom.isEmpty();
    }

    /**
     * Gets the status of WhatsApp service configuration.
     *
     * @return Configuration status message
     */
    public String getConfigurationStatus() {
        if (!twilioEnabled) {
            return "Twilio WhatsApp service is disabled";
        }
        
        if (accountSid == null || accountSid.isEmpty()) {
            return "Twilio Account SID is not configured";
        }
        
        if (authToken == null || authToken.isEmpty()) {
            return "Twilio Auth Token is not configured";
        }
        
        if (whatsappFrom == null || whatsappFrom.isEmpty()) {
            return "Twilio WhatsApp From number is not configured";
        }
        
        return "Twilio WhatsApp service is properly configured";
    }
}
