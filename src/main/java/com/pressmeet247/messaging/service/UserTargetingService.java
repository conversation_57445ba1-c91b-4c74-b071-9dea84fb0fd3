package com.pressmeet247.messaging.service;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for targeting users based on various criteria.
 * Helps with finding users for messaging purposes.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserTargetingService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    /**
     * Gets all active users.
     *
     * @return List of active users
     */
    public List<User> getAllActiveUsers() {
        return userRepository.findByEnabled(true);
    }

    /**
     * Gets all active users with pagination.
     *
     * @param pageable Pagination parameters
     * @return Page of active users
     */
    public Page<User> getAllActiveUsers(Pageable pageable) {
        return userRepository.findByEnabled(true, pageable);
    }

    /**
     * Gets users by user type.
     *
     * @param userType User type
     * @return List of users with the specified type
     */
    public List<User> getUsersByType(UserType userType) {
        return userRepository.findByUserType(userType);
    }

    /**
     * Gets users by user type with pagination.
     *
     * @param userType User type
     * @param pageable Pagination parameters
     * @return Page of users with the specified type
     */
    public Page<User> getUsersByType(UserType userType, Pageable pageable) {
        return userRepository.findByUserType(userType, pageable);
    }

    /**
     * Gets users by role name.
     *
     * @param roleName Role name
     * @param pageable Pagination parameters
     * @return Page of users with the specified role
     */
    public Page<User> getUsersByRole(String roleName, Pageable pageable) {
        Optional<Role> role = roleRepository.findByName(roleName);
        if (role.isPresent()) {
            return userRepository.findByRoles(role.get(), pageable);
        }
        return Page.empty();
    }

    /**
     * Gets users with verified emails.
     *
     * @param pageable Pagination parameters
     * @return Page of users with verified emails
     */
    public Page<User> getUsersWithVerifiedEmails(Pageable pageable) {
        return userRepository.findByEmailVerified(true, pageable);
    }

    /**
     * Gets users with verified phone numbers.
     *
     * @param pageable Pagination parameters
     * @return Page of users with verified phone numbers
     */
    public Page<User> getUsersWithVerifiedPhones(Pageable pageable) {
        return userRepository.findByPhoneVerified(true, pageable);
    }

    /**
     * Gets recently active users (logged in within specified days).
     *
     * @param days Number of days to look back
     * @param pageable Pagination parameters
     * @return Page of recently active users
     */
    public Page<User> getRecentlyActiveUsers(int days, Pageable pageable) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        return userRepository.findByLastLoginAtAfter(cutoffDate, pageable);
    }

    /**
     * Gets inactive users (haven't logged in within specified days).
     *
     * @param days Number of days to look back
     * @param pageable Pagination parameters
     * @return Page of inactive users
     */
    public Page<User> getInactiveUsers(int days, Pageable pageable) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        return userRepository.findByLastLoginAtBefore(cutoffDate, pageable);
    }

    /**
     * Gets new users (registered within specified days).
     *
     * @param days Number of days to look back
     * @param pageable Pagination parameters
     * @return Page of new users
     */
    public Page<User> getNewUsers(int days, Pageable pageable) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        LocalDateTime now = LocalDateTime.now();
        return userRepository.findByCreatedAtBetween(cutoffDate, now, pageable);
    }

    /**
     * Searches users by name or email.
     *
     * @param searchTerm Search term
     * @param pageable Pagination parameters
     * @return Page of matching users
     */
    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return userRepository.searchUsers(searchTerm, pageable);
    }

    /**
     * Gets user targeting statistics.
     *
     * @return User targeting statistics
     */
    public UserTargetingStatistics getTargetingStatistics() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.findByEnabled(true).size();
        long verifiedEmailUsers = userRepository.findByEmailVerified(true, Pageable.unpaged()).getTotalElements();
        
        UserTargetingStatistics.UserTargetingStatisticsBuilder builder = UserTargetingStatistics.builder()
                .totalUsers(totalUsers)
                .activeUsers(activeUsers)
                .verifiedEmailUsers(verifiedEmailUsers);

        // Add user type counts
        for (UserType userType : UserType.values()) {
            long count = userRepository.countByUserType(userType);
            builder.userTypeCount(userType, count);
        }

        return builder.build();
    }

    /**
     * Validates if users can receive messages via specified channel.
     *
     * @param userIds List of user IDs
     * @param requireEmail Whether email is required
     * @param requirePhone Whether phone is required
     * @return Validation result
     */
    public UserValidationResult validateUsersForMessaging(List<Long> userIds, 
                                                          boolean requireEmail, 
                                                          boolean requirePhone) {
        List<User> users = userRepository.findAllById(userIds);
        
        List<User> validUsers = users.stream()
                .filter(user -> user.isEnabled())
                .filter(user -> !requireEmail || (user.getEmail() != null && !user.getEmail().isEmpty()))
                .filter(user -> !requirePhone || (user.getPhoneNumber() != null && !user.getPhoneNumber().isEmpty()))
                .toList();

        List<Long> invalidUserIds = userIds.stream()
                .filter(id -> users.stream().noneMatch(user -> user.getId().equals(id)) ||
                             validUsers.stream().noneMatch(user -> user.getId().equals(id)))
                .toList();

        return UserValidationResult.builder()
                .validUsers(validUsers)
                .invalidUserIds(invalidUserIds)
                .totalRequested(userIds.size())
                .totalValid(validUsers.size())
                .totalInvalid(invalidUserIds.size())
                .build();
    }

    /**
     * Inner class for user targeting statistics.
     */
    public static class UserTargetingStatistics {
        public final long totalUsers;
        public final long activeUsers;
        public final long verifiedEmailUsers;
        public final java.util.Map<UserType, Long> userTypeCounts;

        private UserTargetingStatistics(long totalUsers, long activeUsers, long verifiedEmailUsers,
                                       java.util.Map<UserType, Long> userTypeCounts) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.verifiedEmailUsers = verifiedEmailUsers;
            this.userTypeCounts = userTypeCounts;
        }

        public static UserTargetingStatisticsBuilder builder() {
            return new UserTargetingStatisticsBuilder();
        }

        public static class UserTargetingStatisticsBuilder {
            private long totalUsers;
            private long activeUsers;
            private long verifiedEmailUsers;
            private java.util.Map<UserType, Long> userTypeCounts = new java.util.HashMap<>();

            public UserTargetingStatisticsBuilder totalUsers(long totalUsers) {
                this.totalUsers = totalUsers;
                return this;
            }

            public UserTargetingStatisticsBuilder activeUsers(long activeUsers) {
                this.activeUsers = activeUsers;
                return this;
            }

            public UserTargetingStatisticsBuilder verifiedEmailUsers(long verifiedEmailUsers) {
                this.verifiedEmailUsers = verifiedEmailUsers;
                return this;
            }

            public UserTargetingStatisticsBuilder userTypeCount(UserType userType, long count) {
                this.userTypeCounts.put(userType, count);
                return this;
            }

            public UserTargetingStatistics build() {
                return new UserTargetingStatistics(totalUsers, activeUsers, verifiedEmailUsers, userTypeCounts);
            }
        }
    }

    /**
     * Inner class for user validation results.
     */
    public static class UserValidationResult {
        public final List<User> validUsers;
        public final List<Long> invalidUserIds;
        public final int totalRequested;
        public final int totalValid;
        public final int totalInvalid;

        private UserValidationResult(List<User> validUsers, List<Long> invalidUserIds,
                                   int totalRequested, int totalValid, int totalInvalid) {
            this.validUsers = validUsers;
            this.invalidUserIds = invalidUserIds;
            this.totalRequested = totalRequested;
            this.totalValid = totalValid;
            this.totalInvalid = totalInvalid;
        }

        public static UserValidationResultBuilder builder() {
            return new UserValidationResultBuilder();
        }

        public static class UserValidationResultBuilder {
            private List<User> validUsers;
            private List<Long> invalidUserIds;
            private int totalRequested;
            private int totalValid;
            private int totalInvalid;

            public UserValidationResultBuilder validUsers(List<User> validUsers) {
                this.validUsers = validUsers;
                return this;
            }

            public UserValidationResultBuilder invalidUserIds(List<Long> invalidUserIds) {
                this.invalidUserIds = invalidUserIds;
                return this;
            }

            public UserValidationResultBuilder totalRequested(int totalRequested) {
                this.totalRequested = totalRequested;
                return this;
            }

            public UserValidationResultBuilder totalValid(int totalValid) {
                this.totalValid = totalValid;
                return this;
            }

            public UserValidationResultBuilder totalInvalid(int totalInvalid) {
                this.totalInvalid = totalInvalid;
                return this;
            }

            public UserValidationResult build() {
                return new UserValidationResult(validUsers, invalidUserIds, totalRequested, totalValid, totalInvalid);
            }
        }
    }
}
