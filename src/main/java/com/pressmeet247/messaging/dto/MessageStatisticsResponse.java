package com.pressmeet247.messaging.dto;

import com.pressmeet247.messaging.service.UnifiedMessagingService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for message statistics.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response containing message statistics")
public class MessageStatisticsResponse {

    @Schema(description = "Total number of messages sent", example = "1500")
    private long totalMessages;

    @Schema(description = "Number of successfully sent messages", example = "1450")
    private long sentMessages;

    @Schema(description = "Number of delivered messages", example = "1400")
    private long deliveredMessages;

    @Schema(description = "Number of failed messages", example = "50")
    private long failedMessages;

    @Schema(description = "Number of email messages", example = "1200")
    private long emailMessages;

    @Schema(description = "Number of WhatsApp messages", example = "300")
    private long whatsappMessages;

    @Schema(description = "Success rate percentage", example = "96.67")
    private double successRate;

    @Schema(description = "Delivery rate percentage", example = "93.33")
    private double deliveryRate;

    /**
     * Creates a MessageStatisticsResponse from MessageStatistics.
     *
     * @param stats MessageStatistics
     * @return MessageStatisticsResponse
     */
    public static MessageStatisticsResponse fromStatistics(UnifiedMessagingService.MessageStatistics stats) {
        double successRate = stats.totalMessages > 0 ? 
                (double) stats.sentMessages / stats.totalMessages * 100 : 0.0;
        double deliveryRate = stats.totalMessages > 0 ? 
                (double) stats.deliveredMessages / stats.totalMessages * 100 : 0.0;

        return MessageStatisticsResponse.builder()
                .totalMessages(stats.totalMessages)
                .sentMessages(stats.sentMessages)
                .deliveredMessages(stats.deliveredMessages)
                .failedMessages(stats.failedMessages)
                .emailMessages(stats.emailMessages)
                .whatsappMessages(stats.whatsappMessages)
                .successRate(Math.round(successRate * 100.0) / 100.0)
                .deliveryRate(Math.round(deliveryRate * 100.0) / 100.0)
                .build();
    }
}
