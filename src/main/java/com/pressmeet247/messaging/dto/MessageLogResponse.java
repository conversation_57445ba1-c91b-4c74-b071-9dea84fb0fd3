package com.pressmeet247.messaging.dto;

import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for message log information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response containing message log information")
public class MessageLogResponse {

    @Schema(description = "Message log ID", example = "1")
    private Long id;

    @Schema(description = "Sender admin user ID", example = "1")
    private Long senderAdminId;

    @Schema(description = "Sender admin name", example = "John Admin")
    private String senderAdminName;

    @Schema(description = "Recipient user ID", example = "123")
    private Long recipientUserId;

    @Schema(description = "Recipient user name", example = "Jane Doe")
    private String recipientUserName;

    @Schema(description = "Message channel", example = "EMAIL")
    private MessageChannel channel;

    @Schema(description = "Message type", example = "ANNOUNCEMENT")
    private MessageType messageType;

    @Schema(description = "Message subject", example = "Important Announcement")
    private String subject;

    @Schema(description = "Message content", example = "This is an important announcement.")
    private String content;

    @Schema(description = "Recipient identifier (email or phone)", example = "<EMAIL>")
    private String recipientIdentifier;

    @Schema(description = "Message status", example = "SENT")
    private MessageStatus status;

    @Schema(description = "Timestamp when message was sent")
    private LocalDateTime sentAt;

    @Schema(description = "Timestamp when message was delivered")
    private LocalDateTime deliveredAt;

    @Schema(description = "Timestamp when message was read")
    private LocalDateTime readAt;

    @Schema(description = "Error message if delivery failed")
    private String errorMessage;

    @Schema(description = "External message ID from service provider")
    private String externalMessageId;

    @Schema(description = "Number of retry attempts", example = "0")
    private int retryCount;

    @Schema(description = "Whether this is part of a broadcast", example = "false")
    private boolean isBroadcast;

    @Schema(description = "Broadcast ID if part of a broadcast")
    private String broadcastId;

    @Schema(description = "Timestamp when message log was created")
    private LocalDateTime createdAt;

    /**
     * Creates a MessageLogResponse from a MessageLog entity.
     *
     * @param messageLog MessageLog entity
     * @return MessageLogResponse
     */
    public static MessageLogResponse fromEntity(MessageLog messageLog) {
        return MessageLogResponse.builder()
                .id(messageLog.getId())
                .senderAdminId(messageLog.getSenderAdmin().getId())
                .senderAdminName(messageLog.getSenderAdmin().getFirstName() + " " + 
                               messageLog.getSenderAdmin().getLastName())
                .recipientUserId(messageLog.getRecipientUser().getId())
                .recipientUserName(messageLog.getRecipientUser().getFirstName() + " " + 
                                 messageLog.getRecipientUser().getLastName())
                .channel(messageLog.getChannel())
                .messageType(messageLog.getMessageType())
                .subject(messageLog.getSubject())
                .content(messageLog.getContent())
                .recipientIdentifier(messageLog.getRecipientIdentifier())
                .status(messageLog.getStatus())
                .sentAt(messageLog.getSentAt())
                .deliveredAt(messageLog.getDeliveredAt())
                .readAt(messageLog.getReadAt())
                .errorMessage(messageLog.getErrorMessage())
                .externalMessageId(messageLog.getExternalMessageId())
                .retryCount(messageLog.getRetryCount())
                .isBroadcast(messageLog.isBroadcast())
                .broadcastId(messageLog.getBroadcastId())
                .createdAt(messageLog.getCreatedAt())
                .build();
    }
}
