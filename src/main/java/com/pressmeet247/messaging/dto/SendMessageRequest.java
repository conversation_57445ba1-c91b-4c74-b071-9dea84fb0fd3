package com.pressmeet247.messaging.dto;

import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for sending messages.
 */
@Data
@Schema(description = "Request for sending messages to users")
public class SendMessageRequest {

    @NotNull(message = "Message channel is required")
    @Schema(description = "Channel to send the message through", example = "EMAIL")
    private MessageChannel channel;

    @NotNull(message = "Message type is required")
    @Schema(description = "Type of message being sent", example = "ANNOUNCEMENT")
    private MessageType messageType;

    @Schema(description = "Message subject/title (required for email)", example = "Important Announcement")
    private String subject;

    @NotBlank(message = "Message content is required")
    @Schema(description = "Message content/body", example = "This is an important announcement for all users.")
    private String content;

    @Schema(description = "Target type for the message", example = "SINGLE_USER")
    private TargetType targetType = TargetType.SINGLE_USER;

    @Schema(description = "Single recipient user ID (for SINGLE_USER target type)", example = "123")
    private Long recipientUserId;

    @Schema(description = "List of recipient user IDs (for MULTIPLE_USERS target type)", 
            example = "[123, 456, 789]")
    private List<Long> recipientUserIds;

    @Schema(description = "User type to target (for USER_TYPE target type)", example = "STUDENT")
    private UserType targetUserType;

    /**
     * Enum for different targeting types.
     */
    public enum TargetType {
        SINGLE_USER("Single User"),
        MULTIPLE_USERS("Multiple Users"),
        ALL_USERS("All Users"),
        USER_TYPE("User Type");

        private final String displayName;

        TargetType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
