package com.pressmeet247.messaging.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing a comprehensive log of all sent messages across different channels.
 * This entity tracks individual message deliveries for audit and monitoring purposes.
 */
@Entity
@Table(
    name = "message_logs",
    indexes = {
        @Index(name = "idx_message_log_sender", columnList = "sender_admin_id"),
        @Index(name = "idx_message_log_recipient", columnList = "recipient_user_id"),
        @Index(name = "idx_message_log_channel", columnList = "channel"),
        @Index(name = "idx_message_log_status", columnList = "status"),
        @Index(name = "idx_message_log_sent_at", columnList = "sent_at"),
        @Index(name = "idx_message_log_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE message_logs SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a comprehensive log of sent messages")
public class MessageLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_admin_id", nullable = false)
    @Schema(description = "Admin user who sent the message")
    private User senderAdmin;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_user_id", nullable = false)
    @Schema(description = "User who received the message")
    private User recipientUser;

    @Enumerated(EnumType.STRING)
    @Column(name = "channel", nullable = false, length = 20)
    @Schema(description = "Channel through which the message was sent", example = "EMAIL")
    private MessageChannel channel;

    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false, length = 30)
    @Schema(description = "Type of message sent", example = "ANNOUNCEMENT")
    private MessageType messageType;

    @Column(name = "subject", length = 255)
    @Schema(description = "Message subject/title", example = "Important Announcement")
    private String subject;

    @Column(name = "content", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "Message content/body")
    private String content;

    @Column(name = "recipient_identifier", length = 100, nullable = false)
    @Schema(description = "Recipient's email or phone number", example = "<EMAIL>")
    private String recipientIdentifier;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Schema(description = "Current status of the message", example = "SENT")
    private MessageStatus status = MessageStatus.PENDING;

    @Column(name = "sent_at")
    @Schema(description = "Timestamp when the message was sent")
    private LocalDateTime sentAt;

    @Column(name = "delivered_at")
    @Schema(description = "Timestamp when the message was delivered")
    private LocalDateTime deliveredAt;

    @Column(name = "read_at")
    @Schema(description = "Timestamp when the message was read")
    private LocalDateTime readAt;

    @Column(name = "error_message", length = 500)
    @Schema(description = "Error message if delivery failed")
    private String errorMessage;

    @Column(name = "external_message_id", length = 100)
    @Schema(description = "External service message ID (e.g., Twilio message SID)")
    private String externalMessageId;

    @Column(name = "retry_count", nullable = false)
    @Schema(description = "Number of retry attempts", example = "0")
    private int retryCount = 0;

    @Column(name = "is_broadcast", nullable = false)
    @Schema(description = "Whether this message is part of a broadcast", example = "false")
    private boolean isBroadcast = false;

    @Column(name = "broadcast_id", length = 50)
    @Schema(description = "Unique identifier for broadcast messages")
    private String broadcastId;

    /**
     * Marks the message as sent.
     */
    public void markAsSent() {
        this.status = MessageStatus.SENT;
        this.sentAt = LocalDateTime.now();
    }

    /**
     * Marks the message as delivered.
     */
    public void markAsDelivered() {
        this.status = MessageStatus.DELIVERED;
        this.deliveredAt = LocalDateTime.now();
    }

    /**
     * Marks the message as read.
     */
    public void markAsRead() {
        this.status = MessageStatus.READ;
        this.readAt = LocalDateTime.now();
    }

    /**
     * Marks the message as failed with an error message.
     *
     * @param errorMessage Error message
     */
    public void markAsFailed(String errorMessage) {
        this.status = MessageStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * Increments the retry count.
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }
}
