package com.pressmeet247.messaging.enums;

/**
 * Enum representing the status of a message delivery.
 */
public enum MessageStatus {
    PENDING("Pending"),
    SENT("Sent"),
    DELIVERED("Delivered"),
    READ("Read"),
    FAILED("Failed"),
    CANCELLED("Cancelled");

    private final String displayName;

    MessageStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
