package com.pressmeet247.messaging.enums;

/**
 * Enum representing different types of messages.
 */
public enum MessageType {
    ANNOUNCEMENT("Announcement"),
    NOTIFICATION("Notification"),
    MARKETING("Marketing"),
    REMINDER("Reminder"),
    ALERT("Alert"),
    WELCOME("Welcome"),
    VERIFICATION("Verification"),
    PASSWORD_RESET("Password Reset"),
    SUBSCRIPTION_EXPIRY("Subscription Expiry"),
    CUSTOM("Custom");

    private final String displayName;

    MessageType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
