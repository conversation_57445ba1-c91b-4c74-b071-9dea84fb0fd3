package com.pressmeet247.messaging.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.messaging.service.UserTargetingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for user targeting operations.
 * Helps admins find and target users for messaging.
 */
@RestController
@RequestMapping("/api/admin/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin User Targeting", description = "Admin API for targeting users for messaging")
public class UserTargetingController {

    private final UserTargetingService userTargetingService;

    /**
     * Gets all active users.
     *
     * @param pageable Pagination parameters
     * @return Page of active users
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get active users", description = "Gets all active users (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getActiveUsers(Pageable pageable) {
        
        Page<User> users = userTargetingService.getAllActiveUsers(pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Active users retrieved successfully",
                response
        ));
    }

    /**
     * Gets users by user type.
     *
     * @param userType User type
     * @param pageable Pagination parameters
     * @return Page of users with the specified type
     */
    @GetMapping("/by-type/{userType}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get users by type", description = "Gets users by user type (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByType(
            @PathVariable @Parameter(description = "User type") UserType userType,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.getUsersByType(userType, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Users by type retrieved successfully",
                response
        ));
    }

    /**
     * Gets users by role.
     *
     * @param roleName Role name
     * @param pageable Pagination parameters
     * @return Page of users with the specified role
     */
    @GetMapping("/by-role/{roleName}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get users by role", description = "Gets users by role name (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByRole(
            @PathVariable @Parameter(description = "Role name") String roleName,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.getUsersByRole(roleName, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Users by role retrieved successfully",
                response
        ));
    }

    /**
     * Gets users with verified emails.
     *
     * @param pageable Pagination parameters
     * @return Page of users with verified emails
     */
    @GetMapping("/verified-email")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get users with verified emails", description = "Gets users with verified email addresses (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersWithVerifiedEmails(Pageable pageable) {
        
        Page<User> users = userTargetingService.getUsersWithVerifiedEmails(pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Users with verified emails retrieved successfully",
                response
        ));
    }

    /**
     * Gets users with verified phone numbers.
     *
     * @param pageable Pagination parameters
     * @return Page of users with verified phone numbers
     */
    @GetMapping("/verified-phone")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get users with verified phones", description = "Gets users with verified phone numbers (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersWithVerifiedPhones(Pageable pageable) {
        
        Page<User> users = userTargetingService.getUsersWithVerifiedPhones(pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Users with verified phones retrieved successfully",
                response
        ));
    }

    /**
     * Gets recently active users.
     *
     * @param days Number of days to look back (default: 30)
     * @param pageable Pagination parameters
     * @return Page of recently active users
     */
    @GetMapping("/recently-active")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get recently active users", description = "Gets users who have logged in within specified days (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getRecentlyActiveUsers(
            @RequestParam(defaultValue = "30") @Parameter(description = "Number of days to look back") int days,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.getRecentlyActiveUsers(days, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Recently active users retrieved successfully",
                response
        ));
    }

    /**
     * Gets inactive users.
     *
     * @param days Number of days to look back (default: 90)
     * @param pageable Pagination parameters
     * @return Page of inactive users
     */
    @GetMapping("/inactive")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get inactive users", description = "Gets users who haven't logged in within specified days (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getInactiveUsers(
            @RequestParam(defaultValue = "90") @Parameter(description = "Number of days to look back") int days,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.getInactiveUsers(days, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "Inactive users retrieved successfully",
                response
        ));
    }

    /**
     * Gets new users.
     *
     * @param days Number of days to look back (default: 7)
     * @param pageable Pagination parameters
     * @return Page of new users
     */
    @GetMapping("/new")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get new users", description = "Gets users who registered within specified days (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getNewUsers(
            @RequestParam(defaultValue = "7") @Parameter(description = "Number of days to look back") int days,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.getNewUsers(days, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "New users retrieved successfully",
                response
        ));
    }

    /**
     * Searches users by name or email.
     *
     * @param searchTerm Search term
     * @param pageable Pagination parameters
     * @return Page of matching users
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Search users", description = "Searches users by name or email (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> searchUsers(
            @RequestParam @Parameter(description = "Search term") String searchTerm,
            Pageable pageable) {
        
        Page<User> users = userTargetingService.searchUsers(searchTerm, pageable);
        Page<UserResponse> response = users.map(this::convertToUserResponse);

        return ResponseEntity.ok(ApiResponse.success(
                "User search completed successfully",
                response
        ));
    }

    /**
     * Gets user targeting statistics.
     *
     * @return User targeting statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get user targeting statistics", description = "Gets user targeting statistics (requires ADMIN role)")
    public ResponseEntity<ApiResponse<UserTargetingService.UserTargetingStatistics>> getUserTargetingStatistics() {
        
        UserTargetingService.UserTargetingStatistics stats = userTargetingService.getTargetingStatistics();

        return ResponseEntity.ok(ApiResponse.success(
                "User targeting statistics retrieved successfully",
                stats
        ));
    }

    /**
     * Validates users for messaging.
     *
     * @param userIds List of user IDs to validate
     * @param requireEmail Whether email is required
     * @param requirePhone Whether phone is required
     * @return Validation result
     */
    @PostMapping("/validate-for-messaging")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Validate users for messaging", description = "Validates if users can receive messages via specified channels (requires ADMIN role)")
    public ResponseEntity<ApiResponse<UserTargetingService.UserValidationResult>> validateUsersForMessaging(
            @RequestBody List<Long> userIds,
            @RequestParam(defaultValue = "false") @Parameter(description = "Whether email is required") boolean requireEmail,
            @RequestParam(defaultValue = "false") @Parameter(description = "Whether phone is required") boolean requirePhone) {
        
        UserTargetingService.UserValidationResult result = userTargetingService.validateUsersForMessaging(
                userIds, requireEmail, requirePhone);

        return ResponseEntity.ok(ApiResponse.success(
                "User validation completed successfully",
                result
        ));
    }

    /**
     * Converts User entity to UserResponse DTO.
     *
     * @param user User entity
     * @return UserResponse DTO
     */
    private UserResponse convertToUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .enabled(user.isEnabled())
                .emailVerified(user.isEmailVerified())
                .phoneVerified(user.isPhoneVerified())
                .roles(user.getRoles().stream().map(role -> role.getName()).toList())
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();
    }
}
