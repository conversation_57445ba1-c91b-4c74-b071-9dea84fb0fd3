package com.pressmeet247.messaging.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.messaging.dto.MessageLogResponse;
import com.pressmeet247.messaging.dto.MessageStatisticsResponse;
import com.pressmeet247.messaging.dto.SendMessageRequest;
import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.service.UnifiedMessagingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Controller for unified messaging operations.
 * Provides endpoints for sending messages via email and WhatsApp.
 */
@RestController
@RequestMapping("/api/admin/messaging")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin Messaging", description = "Admin messaging API for email and WhatsApp")
public class MessagingController {

    private final UnifiedMessagingService messagingService;
    private final UserRepository userRepository;

    /**
     * Sends a message based on the request configuration.
     *
     * @param request Message request
     * @param userDetails Authenticated admin user
     * @return Response with message log(s)
     */
    @PostMapping("/send")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Send message", description = "Sends a message via email or WhatsApp to specified recipients (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Object>> sendMessage(
            @Valid @RequestBody SendMessageRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        String adminEmail = userDetails.getUsername();
        User admin = userRepository.findByEmail(adminEmail);
        
        if (admin == null) {
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Admin user not found", null)
            );
        }

        try {
            Object result = switch (request.getTargetType()) {
                case SINGLE_USER -> {
                    if (request.getRecipientUserId() == null) {
                        throw new IllegalArgumentException("Recipient user ID is required for single user messaging");
                    }
                    MessageLog messageLog = messagingService.sendMessageToUser(
                            admin.getId(),
                            request.getRecipientUserId(),
                            request.getChannel(),
                            request.getMessageType(),
                            request.getSubject(),
                            request.getContent()
                    );
                    yield MessageLogResponse.fromEntity(messageLog);
                }
                case MULTIPLE_USERS -> {
                    if (request.getRecipientUserIds() == null || request.getRecipientUserIds().isEmpty()) {
                        throw new IllegalArgumentException("Recipient user IDs are required for multiple user messaging");
                    }
                    List<MessageLog> messageLogs = messagingService.sendMessageToMultipleUsers(
                            admin.getId(),
                            request.getRecipientUserIds(),
                            request.getChannel(),
                            request.getMessageType(),
                            request.getSubject(),
                            request.getContent()
                    );
                    yield messageLogs.stream().map(MessageLogResponse::fromEntity).toList();
                }
                case ALL_USERS -> {
                    List<MessageLog> messageLogs = messagingService.sendBroadcastMessage(
                            admin.getId(),
                            request.getChannel(),
                            request.getMessageType(),
                            request.getSubject(),
                            request.getContent()
                    );
                    yield messageLogs.stream().map(MessageLogResponse::fromEntity).toList();
                }
                case USER_TYPE -> {
                    if (request.getTargetUserType() == null) {
                        throw new IllegalArgumentException("Target user type is required for user type messaging");
                    }
                    List<MessageLog> messageLogs = messagingService.sendBroadcastMessageToUserType(
                            admin.getId(),
                            request.getTargetUserType(),
                            request.getChannel(),
                            request.getMessageType(),
                            request.getSubject(),
                            request.getContent()
                    );
                    yield messageLogs.stream().map(MessageLogResponse::fromEntity).toList();
                }
            };

            return ResponseEntity.ok(ApiResponse.success(
                    "Message(s) sent successfully",
                    result
            ));

        } catch (Exception e) {
            log.error("Error sending message: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Failed to send message: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Gets message history for the authenticated admin.
     *
     * @param userDetails Authenticated admin user
     * @param pageable Pagination parameters
     * @return Page of message logs
     */
    @GetMapping("/history")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get message history", description = "Gets message history for the authenticated admin (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<MessageLogResponse>>> getMessageHistory(
            @AuthenticationPrincipal UserDetails userDetails,
            Pageable pageable) {

        String adminEmail = userDetails.getUsername();
        User admin = userRepository.findByEmail(adminEmail);
        
        if (admin == null) {
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Admin user not found", null)
            );
        }

        Page<MessageLog> messageLogs = messagingService.getMessageHistory(admin.getId(), pageable);
        Page<MessageLogResponse> response = messageLogs.map(MessageLogResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Message history retrieved successfully",
                response
        ));
    }

    /**
     * Gets message statistics.
     *
     * @return Message statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get message statistics", description = "Gets overall message statistics (requires ADMIN role)")
    public ResponseEntity<ApiResponse<MessageStatisticsResponse>> getMessageStatistics() {
        
        UnifiedMessagingService.MessageStatistics stats = messagingService.getMessageStatistics();
        MessageStatisticsResponse response = MessageStatisticsResponse.fromStatistics(stats);

        return ResponseEntity.ok(ApiResponse.success(
                "Message statistics retrieved successfully",
                response
        ));
    }

    /**
     * Gets messages received by a specific user.
     *
     * @param userId User ID
     * @param pageable Pagination parameters
     * @return Page of message logs
     */
    @GetMapping("/user/{userId}/received")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get user received messages", description = "Gets messages received by a specific user (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<MessageLogResponse>>> getUserReceivedMessages(
            @PathVariable Long userId,
            Pageable pageable) {

        try {
            Page<MessageLog> messageLogs = messagingService.getReceivedMessages(userId, pageable);
            Page<MessageLogResponse> response = messageLogs.map(MessageLogResponse::fromEntity);

            return ResponseEntity.ok(ApiResponse.success(
                    "User received messages retrieved successfully",
                    response
            ));
        } catch (Exception e) {
            log.error("Error retrieving user received messages: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Failed to retrieve user messages: " + e.getMessage(), null)
            );
        }
    }
}
