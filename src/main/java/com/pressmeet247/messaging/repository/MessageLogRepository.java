package com.pressmeet247.messaging.repository;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for MessageLog entity operations.
 */
@Repository
public interface MessageLogRepository extends JpaRepository<MessageLog, Long> {

    /**
     * Find all message logs by sender admin.
     *
     * @param senderAdmin Sender admin user
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findBySenderAdmin(User senderAdmin, Pageable pageable);

    /**
     * Find all message logs by recipient user.
     *
     * @param recipientUser Recipient user
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findByRecipientUser(User recipientUser, Pageable pageable);

    /**
     * Find all message logs by channel.
     *
     * @param channel Message channel
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findByChannel(MessageChannel channel, Pageable pageable);

    /**
     * Find all message logs by status.
     *
     * @param status Message status
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findByStatus(MessageStatus status, Pageable pageable);

    /**
     * Find all message logs by message type.
     *
     * @param messageType Message type
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findByMessageType(MessageType messageType, Pageable pageable);

    /**
     * Find all message logs by broadcast ID.
     *
     * @param broadcastId Broadcast ID
     * @return List of message logs
     */
    List<MessageLog> findByBroadcastId(String broadcastId);

    /**
     * Find all message logs sent within a date range.
     *
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pageable
     * @return Page of message logs
     */
    Page<MessageLog> findBySentAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find failed messages that can be retried.
     *
     * @param maxRetryCount Maximum retry count
     * @return List of message logs
     */
    @Query("SELECT ml FROM MessageLog ml WHERE ml.status = 'FAILED' AND ml.retryCount < :maxRetryCount")
    List<MessageLog> findFailedMessagesForRetry(@Param("maxRetryCount") int maxRetryCount);

    /**
     * Count messages by status.
     *
     * @param status Message status
     * @return Count of messages
     */
    long countByStatus(MessageStatus status);

    /**
     * Count messages by channel.
     *
     * @param channel Message channel
     * @return Count of messages
     */
    long countByChannel(MessageChannel channel);

    /**
     * Count messages sent by admin within date range.
     *
     * @param senderAdmin Sender admin
     * @param startDate Start date
     * @param endDate End date
     * @return Count of messages
     */
    long countBySenderAdminAndSentAtBetween(User senderAdmin, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find recent message logs for dashboard.
     *
     * @param limit Number of recent messages to fetch
     * @return List of recent message logs
     */
    @Query("SELECT ml FROM MessageLog ml ORDER BY ml.sentAt DESC")
    List<MessageLog> findRecentMessages(Pageable pageable);
}
