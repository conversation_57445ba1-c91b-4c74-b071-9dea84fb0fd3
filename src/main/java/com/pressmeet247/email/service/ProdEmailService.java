package com.pressmeet247.email.service;

import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.token.TokenService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

/**
 * Production implementation of EmailService that sends real emails.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("mail")
public class ProdEmailService implements IEmailService {

    private final JavaMailSender mailSender;
    private final TokenService tokenService;
    @Qualifier("emailTemplateEngine")
    private final TemplateEngine emailTemplateEngine;

    @Value("${app.email.from}")
    private String fromEmail;

    @Value("${app.email.verification-url}")
    private String verificationBaseUrl;

    @Value("${app.email.reset-password-url}")
    private String resetPasswordBaseUrl;

    /**
     * Sends a verification email to the user with OTP code.
     *
     * @param user User
     */
    @Override
    @Transactional
    public void sendVerificationEmail(User user) {
        try {
            AccountVerification verification = tokenService.createVerificationToken(user);
            String otpCode = verification.getOtpCode();

            Context context = new Context();
            context.setVariable("name", user.getFirstName());
            context.setVariable("otpCode", otpCode);
            String emailContent = emailTemplateEngine.process("otp-verification-email", context);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Verify Your Email");
            helper.setText(emailContent, true);

            mailSender.send(message);
            log.info("OTP verification email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send OTP verification email to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send verification email", e);
        }
    }

    /**
     * Sends a password reset email to the user.
     *
     * @param user User
     * @param token Reset password token
     */
    @Override
    @Transactional
    public void sendPasswordResetEmail(User user, String token) {
        try {
            String resetUrl = resetPasswordBaseUrl + "?token=" + token;

            Context context = new Context();
            context.setVariable("name", user.getFirstName());
            context.setVariable("resetUrl", resetUrl);
            String emailContent = emailTemplateEngine.process("reset-password-email", context);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Reset Your Password");
            helper.setText(emailContent, true);

            mailSender.send(message);
            log.info("Password reset email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send password reset email to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send password reset email", e);
        }
    }

    /**
     * Sends a welcome email to the user.
     *
     * @param user User
     */
    @Override
    @Transactional
    public void sendWelcomeEmail(User user) {
        try {
            Context context = new Context();
            context.setVariable("name", user.getFirstName());
            String emailContent = emailTemplateEngine.process("welcome-email", context);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Welcome to Our Service");
            helper.setText(emailContent, true);

            mailSender.send(message);
            log.info("Welcome email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send welcome email to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send welcome email", e);
        }
    }

    /**
     * Sends a simple text email.
     *
     * @param to Recipient email address
     * @param subject Email subject
     * @param text Email content
     */
    @Override
    @Transactional
    public void sendSimpleEmail(String to, String subject, String text) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text, false);

            mailSender.send(message);
            log.info("Simple email sent to: {}", to);
        } catch (MessagingException e) {
            log.error("Failed to send simple email to: {}", to, e);
            throw new RuntimeException("Failed to send simple email", e);
        }
    }
}
