package com.pressmeet247.email.service;

import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.token.TokenService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

/**
 * Service for sending emails.
 * This is the default implementation that will be used if no profile-specific implementation is active.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService implements IEmailService {

    private final JavaMailSender mailSender;
    private final TokenService tokenService;
    @Qualifier("emailTemplateEngine")
    private final TemplateEngine emailTemplateEngine;

    @Value("${app.email.from}")
    private String fromEmail;

    @Value("${app.email.verification-url}")
    private String verificationBaseUrl;

    @Value("${app.email.reset-password-url}")
    private String resetPasswordBaseUrl;

    /**
     * Sends a verification email to a user with OTP code.
     *
     * @param user User
     */
    @Override
    @Async
    @Transactional
    public void sendVerificationEmail(User user) {
        try {
            // Create verification token with OTP code
            AccountVerification verification = tokenService.createVerificationToken(user);
            String otpCode = verification.getOtpCode();

            // Log the OTP code for testing purposes (remove in production)
            log.info("Generated OTP code for {}: {}", user.getEmail(), otpCode);

            // Create email context
            Context context = new Context();
            context.setVariable("name", user.getFirstName());
            context.setVariable("otpCode", otpCode);

            // Process template
            String emailContent = emailTemplateEngine.process("otp-verification-email", context);

            // Send email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Verify Your Account");
            helper.setText(emailContent, true);

            mailSender.send(mimeMessage);
            log.info("OTP verification email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send verification email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    /**
     * Sends a password reset email to a user.
     *
     * @param user User
     * @param token Reset token
     */
    @Override
    @Async
    public void sendPasswordResetEmail(User user, String token) {
        try {
            String resetLink = resetPasswordBaseUrl + "?token=" + token;

            // Create email context
            Context context = new Context();
            context.setVariable("name", user.getFirstName());
            context.setVariable("resetLink", resetLink);

            // Process template
            String emailContent = emailTemplateEngine.process("reset-password-email", context);

            // Send email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Reset Your Password");
            helper.setText(emailContent, true);

            mailSender.send(mimeMessage);
            log.info("Password reset email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send password reset email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    /**
     * Sends a welcome email to a user.
     *
     * @param user User
     */
    @Override
    @Async
    public void sendWelcomeEmail(User user) {
        try {
            // Create email context
            Context context = new Context();
            context.setVariable("name", user.getFirstName());

            // Process template
            String emailContent = emailTemplateEngine.process("welcome-email", context);

            // Send email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("Welcome to Our Service");
            helper.setText(emailContent, true);

            mailSender.send(mimeMessage);
            log.info("Welcome email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            log.error("Failed to send welcome email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    /**
     * Sends a simple text email.
     *
     * @param to Recipient
     * @param subject Subject
     * @param text Email text
     */
    @Override
    @Async
    public void sendSimpleEmail(String to, String subject, String text) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);

            mailSender.send(message);
            log.info("Simple email sent to: {}", to);
        } catch (Exception e) {
            log.error("Failed to send simple email to {}: {}", to, e.getMessage());
        }
    }
}



