package com.pressmeet247.email.service;

import com.pressmeet247.auth.entity.User;

/**
 * Interface for email services.
 */
public interface IEmailService {

    /**
     * Sends a verification email to a user.
     *
     * @param user User
     */
    void sendVerificationEmail(User user);

    /**
     * Sends a password reset email to a user.
     *
     * @param user User
     * @param token Reset token
     */
    void sendPasswordResetEmail(User user, String token);

    /**
     * Sends a welcome email to a user.
     *
     * @param user User
     */
    void sendWelcomeEmail(User user);

    /**
     * Sends a simple text email.
     *
     * @param to Recipient email address
     * @param subject Email subject
     * @param text Email content
     */
    void sendSimpleEmail(String to, String subject, String text);
}



