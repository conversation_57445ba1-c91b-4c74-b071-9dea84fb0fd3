package com.pressmeet247.email.service;

import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.token.TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

/**
 * Development implementation of EmailService that logs emails instead of sending them.
 * This is useful for development when you don't have an email server.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("!mail")
public class DevEmailService implements IEmailService {

    private final TokenService tokenService;
    @Qualifier("emailTemplateEngine")
    private final TemplateEngine emailTemplateEngine;

    @Value("${app.email.from}")
    private String fromEmail;

    @Value("${app.email.verification-url}")
    private String verificationBaseUrl;

    @Value("${app.email.reset-password-url}")
    private String resetPasswordBaseUrl;

    /**
     * Logs a verification email instead of sending it.
     *
     * @param user User
     */
    @Override
    @Transactional
    public void sendVerificationEmail(User user) {
        AccountVerification verification = tokenService.createVerificationToken(user);
        String token = verification.getToken();
        String verificationUrl = verificationBaseUrl + "?token=" + token;

        Context context = new Context();
        context.setVariable("name", user.getFirstName());
        context.setVariable("verificationLink", verificationUrl);
        String emailContent = emailTemplateEngine.process("verification-email", context);

        log.info("=== VERIFICATION EMAIL ===");
        log.info("To: {}", user.getEmail());
        log.info("From: {}", fromEmail);
        log.info("Subject: Verify Your Email");
        log.info("Content: {}", emailContent);
        log.info("Verification URL: {}", verificationUrl);
        log.info("=========================");
    }

    /**
     * Logs a password reset email instead of sending it.
     *
     * @param user User
     * @param token Reset password token
     */
    @Override
    @Transactional
    public void sendPasswordResetEmail(User user, String token) {
        String resetUrl = resetPasswordBaseUrl + "?token=" + token;

        Context context = new Context();
        context.setVariable("name", user.getFirstName());
        context.setVariable("resetUrl", resetUrl);
        String emailContent = emailTemplateEngine.process("reset-password-email", context);

        log.info("=== PASSWORD RESET EMAIL ===");
        log.info("To: {}", user.getEmail());
        log.info("From: {}", fromEmail);
        log.info("Subject: Reset Your Password");
        log.info("Content: {}", emailContent);
        log.info("Reset URL: {}", resetUrl);
        log.info("============================");
    }

    /**
     * Logs a welcome email instead of sending it.
     *
     * @param user User
     */
    @Override
    @Transactional
    public void sendWelcomeEmail(User user) {
        Context context = new Context();
        context.setVariable("name", user.getFirstName());
        String emailContent = emailTemplateEngine.process("welcome-email", context);

        log.info("=== WELCOME EMAIL ===");
        log.info("To: {}", user.getEmail());
        log.info("From: {}", fromEmail);
        log.info("Subject: Welcome to Our Service");
        log.info("Content: {}", emailContent);
        log.info("=====================");
    }

    /**
     * Logs a simple email instead of sending it.
     *
     * @param to Recipient email address
     * @param subject Email subject
     * @param text Email content
     */
    @Override
    public void sendSimpleEmail(String to, String subject, String text) {
        log.info("=== SIMPLE EMAIL ===");
        log.info("To: {}", to);
        log.info("From: {}", fromEmail);
        log.info("Subject: {}", subject);
        log.info("Content: {}", text);
        log.info("====================");
    }
}



