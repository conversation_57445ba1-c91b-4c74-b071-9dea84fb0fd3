package com.pressmeet247.common.util;

import com.pressmeet247.common.jwt.JwtProperties;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Utility class for cookie operations.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CookieUtils {

    private final JwtProperties jwtProperties;

    /**
     * Creates a cookie with the JWT token.
     *
     * @param token JWT token
     * @return Cookie with JWT token
     */
    public Cookie createJwtCookie(String token) {
        Cookie cookie = new Cookie(jwtProperties.getCookieName(), token);
        cookie.setHttpOnly(jwtProperties.isCookieHttpOnly());
        cookie.setSecure(jwtProperties.isCookieSecure());
        cookie.setPath(jwtProperties.getCookiePath());
        cookie.setMaxAge((int) (jwtProperties.getExpiration() / 1000)); // Convert milliseconds to seconds

        return cookie;
    }

    /**
     * Creates a cookie for the refresh token.
     *
     * @param token Refresh token
     * @return Cookie with refresh token
     */
    public Cookie createRefreshTokenCookie(String token) {
        Cookie cookie = new Cookie(jwtProperties.getCookieName() + "_refresh", token);
        cookie.setHttpOnly(jwtProperties.isCookieHttpOnly());
        cookie.setSecure(jwtProperties.isCookieSecure());
        cookie.setPath(jwtProperties.getCookiePath());
        cookie.setMaxAge((int) (jwtProperties.getRefreshExpiration() / 1000)); // Convert milliseconds to seconds

        return cookie;
    }

    /**
     * Adds a JWT cookie to the response.
     *
     * @param response HTTP response
     * @param token JWT token
     */
    public void addJwtCookie(HttpServletResponse response, String token) {
        Cookie cookie = createJwtCookie(token);
        response.addCookie(cookie);

        // Add SameSite attribute (not directly supported by Cookie class)
        String sameSite = jwtProperties.getCookieSameSite();
        if (sameSite != null && !sameSite.isEmpty()) {
            // Create a complete cookie header with SameSite attribute
            StringBuilder cookieHeader = new StringBuilder();
            cookieHeader.append(cookie.getName()).append("=").append(cookie.getValue());
            cookieHeader.append("; Max-Age=").append(cookie.getMaxAge());
            cookieHeader.append("; Path=").append(cookie.getPath());

            if (cookie.isHttpOnly()) {
                cookieHeader.append("; HttpOnly");
            }

            if (cookie.getSecure()) {
                cookieHeader.append("; Secure");
            }

            cookieHeader.append("; SameSite=").append(sameSite);

            // Set the complete cookie header
            response.addHeader("Set-Cookie", cookieHeader.toString());
        }
    }

    /**
     * Adds a refresh token cookie to the response.
     *
     * @param response HTTP response
     * @param token Refresh token
     */
    public void addRefreshTokenCookie(HttpServletResponse response, String token) {
        Cookie cookie = createRefreshTokenCookie(token);
        response.addCookie(cookie);

        // Add SameSite attribute (not directly supported by Cookie class)
        String sameSite = jwtProperties.getCookieSameSite();
        if (sameSite != null && !sameSite.isEmpty()) {
            // Create a complete cookie header with SameSite attribute
            StringBuilder cookieHeader = new StringBuilder();
            cookieHeader.append(cookie.getName()).append("=").append(cookie.getValue());
            cookieHeader.append("; Max-Age=").append(cookie.getMaxAge());
            cookieHeader.append("; Path=").append(cookie.getPath());

            if (cookie.isHttpOnly()) {
                cookieHeader.append("; HttpOnly");
            }

            if (cookie.getSecure()) {
                cookieHeader.append("; Secure");
            }

            cookieHeader.append("; SameSite=").append(sameSite);

            // Set the complete cookie header
            response.addHeader("Set-Cookie", cookieHeader.toString());
        }
    }

    /**
     * Clears the JWT cookie.
     *
     * @param response HTTP response
     */
    public void clearJwtCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(jwtProperties.getCookieName(), null);
        cookie.setHttpOnly(jwtProperties.isCookieHttpOnly());
        cookie.setSecure(jwtProperties.isCookieSecure());
        cookie.setPath(jwtProperties.getCookiePath());
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }

    /**
     * Clears the refresh token cookie.
     *
     * @param response HTTP response
     */
    public void clearRefreshTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(jwtProperties.getCookieName() + "_refresh", null);
        cookie.setHttpOnly(jwtProperties.isCookieHttpOnly());
        cookie.setSecure(jwtProperties.isCookieSecure());
        cookie.setPath(jwtProperties.getCookiePath());
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }

    /**
     * Gets the JWT token from the request cookies.
     *
     * @param request HTTP request
     * @return JWT token if present, null otherwise
     */
    public String getJwtFromCookies(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (jwtProperties.getCookieName().equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * Gets the refresh token from the request cookies.
     *
     * @param request HTTP request
     * @return Refresh token if present, null otherwise
     */
    public String getRefreshTokenFromCookies(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ((jwtProperties.getCookieName() + "_refresh").equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
