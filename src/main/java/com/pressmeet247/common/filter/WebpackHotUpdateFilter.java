package com.pressmeet247.common.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Filter to handle webpack hot-update requests.
 * This filter intercepts requests for webpack hot-update files and returns a 204 No Content response,
 * preventing these requests from reaching the exception handler and generating error logs.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class WebpackHotUpdateFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filter<PERSON>hain)
            throws ServletException, IOException {
        
        String path = request.getRequestURI();
        
        // Check if the request is for a webpack hot-update file
        if (path.contains(".hot-update.")) {
            // Return 204 No Content without any error
            response.setStatus(HttpStatus.NO_CONTENT.value());
            return;
        }
        
        // Continue with the filter chain for all other requests
        filterChain.doFilter(request, response);
    }
}
