package com.pressmeet247.common.jwt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Configuration properties for JWT.
 */
@Data
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.jwt")
public class JwtProperties {
    
    /**
     * Secret key for signing JWT tokens.
     */
    @NotBlank(message = "JWT secret must not be blank")
    private String secret;
    
    /**
     * Access token expiration time in milliseconds.
     */
    @NotNull(message = "JWT expiration must not be null")
    @Positive(message = "JWT expiration must be positive")
    private Long expiration;
    
    /**
     * Refresh token expiration time in milliseconds.
     */
    @NotNull(message = "JWT refresh expiration must not be null")
    @Positive(message = "JWT refresh expiration must be positive")
    private Long refreshExpiration;
    
    /**
     * Issuer claim for JWT tokens.
     */
    private String issuer = "auth-service";
    
    /**
     * Audience claim for JWT tokens.
     */
    private String audience = "web";

    /**
     * Cookie name for storing JWT token.
     */
    private String cookieName = "jwt_token";

    /**
     * Cookie path.
     */
    private String cookiePath = "/";

    /**
     * Whether the cookie is secure (HTTPS only).
     */
    private boolean cookieSecure = false;

    /**
     * Whether the cookie is HTTP only.
     */
    private boolean cookieHttpOnly = true;

    /**
     * Same site policy for the cookie.
     */
    private String cookieSameSite = "Lax";
}
