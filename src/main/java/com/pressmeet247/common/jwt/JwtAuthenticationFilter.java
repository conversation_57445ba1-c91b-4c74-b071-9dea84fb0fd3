package com.pressmeet247.common.jwt;

import com.pressmeet247.auth.service.CustomUserDetailsService;
import com.pressmeet247.common.util.CookieUtils;
import jakarta.annotation.Nonnull;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Filter for JWT authentication.
 * Extracts and validates JWT tokens from requests.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final CustomUserDetailsService userDetailsService;
    private final CookieUtils cookieUtils;

    @Override
    protected void doFilterInternal(@Nonnull HttpServletRequest request,
                                    @Nonnull HttpServletResponse response,
                                    @Nonnull FilterChain filterChain) throws ServletException, IOException {
        try {
            // First try to get token from cookie
            String jwt = cookieUtils.getJwtFromCookies(request);
            log.debug("JWT from cookie: {}", jwt != null ? "found" : "not found");

            // If not found in cookie, try to get from Authorization header as fallback
            if (jwt == null) {
                jwt = parseJwt(request);
                log.debug("JWT from Authorization header: {}", jwt != null ? "found" : "not found");
            }

            // Log request details for debugging
            log.debug("Request URI: {}", request.getRequestURI());
            log.debug("Request method: {}", request.getMethod());
            log.debug("Has cookies: {}", request.getCookies() != null);
            if (request.getCookies() != null) {
                log.debug("Cookie names: {}", Arrays.stream(request.getCookies())
                        .map(c -> c.getName() + "=" + (c.getValue() != null ? "[present]" : "[empty]"))
                        .collect(Collectors.joining(", ")));
            }

            if (jwt != null && jwtUtils.validateJwtToken(jwt)) {
                String email = jwtUtils.getEmailFromJwtToken(jwt);
                UserDetails userDetails = userDetailsService.loadUserByUsername(email);

                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());

                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);

                log.debug("Set authentication for user: {}", email);
            }
        } catch (Exception e) {
            log.error("Cannot set user authentication: {}", e.getMessage());
        }

        filterChain.doFilter(request, response);
    }

    /**
     * Extracts JWT token from the Authorization header.
     *
     * @param request HTTP request
     * @return JWT token if present, null otherwise
     */
    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader("Authorization");

        if (headerAuth != null && headerAuth.startsWith("Bearer ")) {
            return headerAuth.substring(7);
        }

        return null;
    }
}



