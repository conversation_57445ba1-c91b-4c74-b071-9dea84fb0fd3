package com.pressmeet247.common.oauth2;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;

/**
 * Configuration properties for OAuth2.
 */
@Data
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.oauth2")
public class OAuth2Properties {
    
    /**
     * Google OAuth2 redirect URI.
     */
    @NotBlank(message = "Google redirect URI must not be blank")
    private String googleRedirectUri;
    
    /**
     * Facebook OAuth2 redirect URI.
     */
    private String facebookRedirectUri;
    
    /**
     * Frontend registration URL.
     */
    @NotBlank(message = "Registration URL must not be blank")
    private String registrationUrl;
    
    /**
     * Frontend profile URL.
     */
    @NotBlank(message = "Profile URL must not be blank")
    private String profileUrl;
    
    /**
     * Frontend error URL.
     */
    @NotBlank(message = "Error URL must not be blank")
    private String errorUrl;
}

