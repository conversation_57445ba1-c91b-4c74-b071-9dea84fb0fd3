package com.pressmeet247.common.oauth2;

import com.pressmeet247.common.oauth2.OAuth2Properties;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.auth.token.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

/**
 * Handler for successful OAuth2 authentication.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OAuth2SuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    private final UserRepository userRepository;
    private final TokenService tokenService;
    private final OAuth2Properties oAuth2Properties;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request,
                                        HttpServletResponse response,
                                        Authentication authentication) throws IOException {
        OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();
        String email = oAuth2User.getAttribute("email");

        if (email == null) {
            log.error("Email not found in OAuth2 user attributes");
            response.sendRedirect(oAuth2Properties.getErrorUrl() + "?error=email-not-found");
            return;
        }

        User user = userRepository.findByEmail(email);

        if (user == null) {
            // Redirect to a page to complete registration
            String tempToken = UUID.randomUUID().toString();
            tokenService.storeTempToken(tempToken, email);
            
            // Store OAuth2 user attributes in session for later use
            request.getSession().setAttribute("oauth2_user_first_name", oAuth2User.getAttribute("given_name"));
            request.getSession().setAttribute("oauth2_user_last_name", oAuth2User.getAttribute("family_name"));
            
            log.info("Redirecting new OAuth2 user to registration page: {}", email);
            response.sendRedirect(oAuth2Properties.getRegistrationUrl() + "?token=" + tempToken);
            return;
        }

        // Generate tokens for existing user
        var authResponse = tokenService.generateTokenResponse(user);
        
        // Redirect to profile page with token
        log.info("OAuth2 login successful for user: {}", email);
        response.sendRedirect(oAuth2Properties.getProfileUrl() + 
                "?token=" + authResponse.getAccessToken() + 
                "&refreshToken=" + authResponse.getRefreshToken());
    }
}

