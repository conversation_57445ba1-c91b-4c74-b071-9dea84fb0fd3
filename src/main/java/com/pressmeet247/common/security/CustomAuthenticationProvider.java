package com.pressmeet247.common.security;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.service.CustomUserDetailsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;

/**
 * Custom authentication provider for username/password authentication.
 */
@Slf4j
@RequiredArgsConstructor
public class CustomAuthenticationProvider implements AuthenticationProvider {

    private final CustomUserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = (String) authentication.getCredentials();

        try {
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            
            // Check if account is locked
            if (userDetails instanceof User user && !user.isAccountNonLocked()) {
                log.warn("Account is locked for user: {}", username);
                throw new LockedException("Account is locked. Please try again later or reset your password.");
            }

            // Validate password
            if (!passwordEncoder.matches(password, userDetails.getPassword())) {
                handleFailedLogin(userDetails);
                log.warn("Authentication failed for user: {}", username);
                throw new BadCredentialsException("Invalid username or password");
            }

            // Reset failed login attempts on successful login
            if (userDetails instanceof User user) {
                user.setFailedLoginAttempts(0);
                user.setLastLoginAt(LocalDateTime.now());
                userDetailsService.saveUser(user);
            }

            return new UsernamePasswordAuthenticationToken(userDetails, password, userDetails.getAuthorities());
        } catch (UsernameNotFoundException ex) {
            log.warn("User not found: {}", username);
            throw new BadCredentialsException("Invalid username or password");
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    /**
     * Handles failed login attempts.
     *
     * @param userDetails User details
     */
    private void handleFailedLogin(UserDetails userDetails) {
        if (userDetails instanceof User user) {
            int failedAttempts = user.getFailedLoginAttempts() + 1;
            user.setFailedLoginAttempts(failedAttempts);
            
            // Lock account after 5 failed attempts
            if (failedAttempts >= 5) {
                user.setLockedUntil(LocalDateTime.now().plusMinutes(15));
                log.warn("Account locked for user: {} after {} failed attempts", user.getEmail(), failedAttempts);
            }
            
            userDetailsService.saveUser(user);
        }
    }
}

