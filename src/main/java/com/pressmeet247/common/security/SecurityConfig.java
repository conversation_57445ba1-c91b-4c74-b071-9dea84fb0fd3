package com.pressmeet247.common.security;

import com.pressmeet247.common.filter.WebpackHotUpdateFilter;
import com.pressmeet247.common.oauth2.OAuth2SuccessHandler;
import com.pressmeet247.common.security.SecurityProperties;
import com.pressmeet247.common.jwt.JwtAuthenticationFilter;
import com.pressmeet247.auth.service.CustomUserDetailsService;
import com.pressmeet247.auth.service.OAuth2UserServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;

import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Security configuration for the application.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final CustomUserDetailsService userDetailsService;
    private final OAuth2SuccessHandler oAuth2SuccessHandler;
    private final OAuth2UserServiceImpl oAuth2UserService;
    private final PasswordEncoder passwordEncoder;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final WebpackHotUpdateFilter webpackHotUpdateFilter;
    private final SecurityProperties securityProperties;

    /**
     * Configures the authentication manager.
     *
     * @return AuthenticationManager
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(Collections.singletonList(
                authenticationProvider()
        ));
    }

    /**
     * Configures the authentication provider.
     *
     * @return AuthenticationProvider
     */
    @Bean
    public AuthenticationProvider authenticationProvider() {
        return new CustomAuthenticationProvider(userDetailsService, passwordEncoder);
    }

    /**
     * Configures CORS.
     *
     * @return CorsConfigurationSource
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // For cookies to work, we need to specify allowed origins explicitly
        configuration.setAllowedOrigins(securityProperties.getAllowedOrigins());
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "Accept"));
        configuration.setExposedHeaders(Arrays.asList("X-Auth-Redirect", "X-Auth-Token", "X-Auth-Token-Type", "Authorization", "Set-Cookie"));
        // Enable credentials for cookies
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * Configures the security filter chain.
     *
     * @param http HttpSecurity
     * @return SecurityFilterChain
     * @throws Exception if an error occurs
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                // Enable CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                // Disable CSRF for stateless REST API
                .csrf(AbstractHttpConfigurer::disable)
                // Configure headers
                .headers(headers -> headers.frameOptions(frameOptions -> frameOptions.disable()))
                // Stateless session management
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // Authorization rules
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/api/auth/register", "/api/auth/login", "/api/auth/refresh",
                                "/api/auth/logout", "/api/auth/verify", "/api/auth/set-password",
                                "/api/auth/reset-password/**", "/api/auth/resend-verification-email",
                                "/api/v1/authentication/register", "/api/v1/authentication/login", "/api/v1/authentication/verify",
                                "/api/v1/authentication/get-verification-token", "/api/v1/authentication/validate-email-verification-token",
                                "/api/v1/authentication/send-email-verification-token", "/api/v1/authentication/verify-otp",
                                "/api/v1/authentication/resend-otp", "/api/v1/authentication/get-otp-code", "/api/v1/authentication/refresh-token",
                                "/api/admin/auth/login/initiate", "/api/admin/auth/login/verify", // Permit access to admin login endpoints
                                "/api/health",
                                "/api/news/published", "/api/news/featured", "/api/news/type/**",
                                "/swagger-ui/**", "/v3/api-docs/**", "/actuator/**").permitAll() // Permit access to auth, public news, and documentation endpoints
                        .requestMatchers("/api/news/generate").authenticated() // Require authentication for news generation
                        .requestMatchers("/api/admin/**").hasAuthority("ROLE_ADMIN") // Require ADMIN role for admin endpoints
                        .requestMatchers("/api/v1/admin/**").hasAuthority("ROLE_ADMIN") // Require ADMIN role for v1 admin endpoints
                        .anyRequest().authenticated() // Protect all other endpoints
                )
                // Completely disable OAuth2 login to prevent it from intercepting regular login
                .oauth2Login(AbstractHttpConfigurer::disable)
                // JWT-based resource server configuration - disabled for now
                .oauth2ResourceServer(AbstractHttpConfigurer::disable);

        // Add webpack hot-update filter at the very beginning of the filter chain
        http.addFilterBefore(webpackHotUpdateFilter, UsernamePasswordAuthenticationFilter.class);

        // Add JWT filter before UsernamePasswordAuthenticationFilter
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * Configures how JWT tokens are converted to authentication objects.
     *
     * @return JwtAuthenticationConverter
     */
    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtGrantedAuthoritiesConverter authoritiesConverter = new JwtGrantedAuthoritiesConverter();
        authoritiesConverter.setAuthorityPrefix("ROLE_");
        authoritiesConverter.setAuthoritiesClaimName("roles");

        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(authoritiesConverter);
        return converter;
    }


}



