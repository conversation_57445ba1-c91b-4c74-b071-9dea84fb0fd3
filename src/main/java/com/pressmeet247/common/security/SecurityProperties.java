package com.pressmeet247.common.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * Configuration properties for security.
 */
@Data
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.security")
public class SecurityProperties {
    
    /**
     * List of allowed origins for CORS.
     */
    private List<String> allowedOrigins = List.of("http://localhost:3000");
    
    /**
     * Maximum number of failed login attempts before account lockout.
     */
    private int maxFailedLoginAttempts = 5;
    
    /**
     * Account lockout duration in minutes.
     */
    private int accountLockoutMinutes = 15;
    
    /**
     * Password reset token expiration in hours.
     */
    private int passwordResetExpirationHours = 24;
    
    /**
     * Account verification token expiration in hours.
     */
    private int accountVerificationExpirationHours = 24;
}

