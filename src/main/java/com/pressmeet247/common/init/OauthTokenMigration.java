package com.pressmeet247.common.init;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Migration script to update the oauth_tokens table to use UUID primary keys.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OauthTokenMigration {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Executes the migration to update the oauth_tokens table structure.
     */
    @PostConstruct
    @Transactional
    public void migrate() {
        log.info("Running oauth_tokens table migration...");
        
        try {
            // Check if the table exists
            boolean tableExists = doesTableExist("oauth_tokens");
            
            if (tableExists) {
                // Drop the existing table
                log.info("Dropping existing oauth_tokens table...");
                jdbcTemplate.execute("DROP TABLE oauth_tokens");
            }
            
            // Create the table with UUID primary key
            log.info("Creating oauth_tokens table with UUID primary key...");
            jdbcTemplate.execute("""
                CREATE TABLE oauth_tokens (
                    id VARCHAR(36) NOT NULL PRIMARY KEY,
                    access_token TEXT NOT NULL,
                    refresh_token VARCHAR(100) NOT NULL UNIQUE,
                    user_id BIGINT NOT NULL,
                    expiration_time DATETIME NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent VARCHAR(255),
                    device_type VARCHAR(50),
                    location VARCHAR(255),
                    revoked BOOLEAN DEFAULT FALSE,
                    revoked_at DATETIME,
                    created_at DATETIME NOT NULL,
                    created_by VARCHAR(255),
                    updated_at DATETIME,
                    updated_by VARCHAR(255),
                    deleted BOOLEAN DEFAULT FALSE,
                    deleted_at DATETIME,
                    deleted_by VARCHAR(255),
                    version INT DEFAULT 0,
                    INDEX idx_oauth_token_refresh_token (refresh_token),
                    INDEX idx_oauth_token_user_id (user_id),
                    INDEX idx_oauth_token_expiration_time (expiration_time),
                    INDEX idx_oauth_token_deleted (deleted),
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """);
            
            log.info("OAuth tokens table migration completed successfully");
        } catch (Exception e) {
            log.error("Error during oauth_tokens table migration: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Checks if a table exists in the database.
     *
     * @param tableName The name of the table to check
     * @return true if the table exists, false otherwise
     */
    private boolean doesTableExist(String tableName) {
        try {
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ? AND table_schema = DATABASE()",
                Integer.class,
                tableName
            );
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking if table exists: {}", e.getMessage());
            return false;
        }
    }
}
