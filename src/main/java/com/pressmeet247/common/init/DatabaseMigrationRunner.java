package com.pressmeet247.common.init;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * Executes database migrations on application startup.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseMigrationRunner {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Executes database migrations after the application context is initialized.
     */
    @PostConstruct
    public void runMigrations() {
        try {
            log.info("Running database migrations...");

            // Check if otp_code column exists
            boolean otpColumnExists = checkIfColumnExists("account_verifications", "otp_code");

            if (!otpColumnExists) {
                log.info("Adding otp_code column to account_verifications table");
                jdbcTemplate.execute("ALTER TABLE account_verifications ADD COLUMN otp_code VARCHAR(6) NULL AFTER token");
                log.info("Migration completed successfully");
            } else {
                log.info("otp_code column already exists in account_verifications table");
            }

            // Update oauth_tokens table
            try {
                // Check if access_token is VARCHAR
                String query = "SELECT DATA_TYPE FROM information_schema.columns " +
                        "WHERE table_name = 'oauth_tokens' AND column_name = 'access_token'";
                String dataType = jdbcTemplate.queryForObject(query, String.class);

                if (dataType != null && !dataType.equalsIgnoreCase("text")) {
                    log.info("Updating access_token column in oauth_tokens table to TEXT");

                    // Drop the unique constraint if it exists
                    try {
                        jdbcTemplate.execute("ALTER TABLE oauth_tokens DROP INDEX idx_oauth_token_access_token");
                    } catch (Exception e) {
                        log.warn("Could not drop index idx_oauth_token_access_token: {}", e.getMessage());
                    }

                    // Change column type
                    jdbcTemplate.execute("ALTER TABLE oauth_tokens MODIFY COLUMN access_token TEXT NOT NULL");
                    log.info("Successfully updated access_token column to TEXT");
                } else {
                    log.info("access_token column is already TEXT type");
                }
            } catch (Exception e) {
                log.warn("Error updating oauth_tokens table: {}", e.getMessage());
            }

            // Fix null version values in all tables
            try {
                log.info("Fixing null version values in database tables");

                // List of tables to check and fix
                String[] tables = {
                    "users", "account_verifications", "reset_password_tokens", "oauth_tokens",
                    "roles", "user_roles", "security_events", "login_history", "user_preferences",
                    "admins", "admin_login_history", "admin_security_events"
                };

                for (String table : tables) {
                    try {
                        // Check if table exists
                        String checkTableQuery = "SELECT COUNT(*) FROM information_schema.tables " +
                                "WHERE table_schema = DATABASE() AND table_name = ?";
                        Integer tableCount = jdbcTemplate.queryForObject(checkTableQuery, Integer.class, table);

                        if (tableCount != null && tableCount > 0) {
                            // Check if version column exists
                            String checkColumnQuery = "SELECT COUNT(*) FROM information_schema.columns " +
                                    "WHERE table_schema = DATABASE() AND table_name = ? AND column_name = 'version'";
                            Integer columnCount = jdbcTemplate.queryForObject(checkColumnQuery, Integer.class, table);

                            if (columnCount != null && columnCount > 0) {
                                // Update null version values
                                String updateQuery = "UPDATE " + table + " SET version = 0 WHERE version IS NULL";
                                int updatedRows = jdbcTemplate.update(updateQuery);
                                log.info("Fixed {} null version values in {} table", updatedRows, table);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Error fixing version values in {} table: {}", table, e.getMessage());
                    }
                }

                log.info("Completed fixing null version values");
            } catch (Exception e) {
                log.warn("Error fixing null version values: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("Error running database migrations", e);
        }
    }

    /**
     * Checks if a column exists in a table.
     *
     * @param tableName Table name
     * @param columnName Column name
     * @return True if the column exists
     */
    private boolean checkIfColumnExists(String tableName, String columnName) {
        try {
            String query = "SELECT COUNT(*) FROM information_schema.columns " +
                    "WHERE table_name = ? AND column_name = ?";

            Integer count = jdbcTemplate.queryForObject(query, Integer.class, tableName, columnName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking if column exists", e);
            return false;
        }
    }
}


