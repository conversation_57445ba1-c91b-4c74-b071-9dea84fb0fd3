package com.pressmeet247.common.config;

import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC configuration.
 * This configuration ensures that API requests are not treated as static resources.
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * Configure formatters and converters.
     * This adds custom converters for enum types to handle case-insensitive conversion.
     *
     * @param registry FormatterRegistry
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        // Add custom converters for enums
        registry.addConverter(String.class, NewsStatus.class, source -> {
            try {
                return NewsStatus.valueOf(source.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid NewsStatus value: " + source);
            }
        });

        registry.addConverter(String.class, SubscriptionStatus.class, source -> {
            try {
                return SubscriptionStatus.valueOf(source.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid SubscriptionStatus value: " + source);
            }
        });
    }

    /**
     * Configure resource handlers.
     * This explicitly defines which paths should be treated as static resources,
     * ensuring that API paths are not mistakenly handled as static resources.
     *
     * @param registry ResourceHandlerRegistry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Define static resource locations explicitly
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        registry.addResourceHandler("/public/**")
                .addResourceLocations("classpath:/public/");

        registry.addResourceHandler("/resources/**")
                .addResourceLocations("classpath:/resources/");

        registry.addResourceHandler("/META-INF/resources/**")
                .addResourceLocations("classpath:/META-INF/resources/");

        // Add Swagger UI resources
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .resourceChain(false);
    }
}
