package com.pressmeet247.common.config.redis;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.support.PersistenceExceptionTranslator;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * In-memory implementation of Redis when Redis is disabled.
 */
@Configuration
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "false")
public class InMemoryRedisConfig {

    /**
     * In-memory Redis connection factory.
     *
     * @return RedisConnectionFactory
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        return new InMemoryRedisConnectionFactory();
    }

    /**
     * In-memory Redis template for string operations.
     *
     * @param connectionFactory Redis connection factory
     * @return RedisTemplate
     */
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new InMemoryRedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        return template;
    }

    /**
     * In-memory implementation of RedisConnectionFactory.
     */
    private static class InMemoryRedisConnectionFactory implements RedisConnectionFactory {
        @Override
        public org.springframework.data.redis.connection.RedisConnection getConnection() {
            return null; // Not used in our implementation
        }

        @Override
        public org.springframework.data.redis.connection.RedisClusterConnection getClusterConnection() {
            return null; // Not used in our implementation
        }

        @Override
        public org.springframework.data.redis.connection.RedisSentinelConnection getSentinelConnection() {
            return null; // Not used in our implementation
        }

        @Override
        public boolean getConvertPipelineAndTxResults() {
            return false; // Not used in our implementation
        }

        @Override
        public DataAccessException translateExceptionIfPossible(RuntimeException ex) {
            return null; // No translation needed for our in-memory implementation
        }
    }

    /**
     * In-memory implementation of RedisTemplate.
     */
    private static class InMemoryRedisTemplate<K, V> extends RedisTemplate<K, V> {
        private final Map<Object, Object> storage = new ConcurrentHashMap<>();
        private final Map<Object, Long> expirations = new ConcurrentHashMap<>();
        private final InMemoryValueOperations valueOps = new InMemoryValueOperations() {
            @Override
            public V getAndDelete(K key) {
                return null;
            }

            @Override
            public V getAndExpire(K key, long timeout, TimeUnit unit) {
                return null;
            }

            @Override
            public V getAndExpire(K key, Duration timeout) {
                return null;
            }

            @Override
            public V getAndPersist(K key) {
                return null;
            }

            @Override
            public V getAndSet(K key, V value) {
                return null;
            }

            @Override
            public List<Long> bitField(K key, BitFieldSubCommands subCommands) {
                return List.of();
            }
        };

        @Override
        public org.springframework.data.redis.core.ValueOperations<K, V> opsForValue() {
            return valueOps;
        }

        @Override
        public Boolean delete(Object key) {
            storage.remove(key);
            expirations.remove(key);
            return true;
        }

        /**
         * In-memory implementation of ValueOperations.
         * Only implements the methods we actually use in our application.
         */
        private abstract class InMemoryValueOperations implements org.springframework.data.redis.core.ValueOperations<K, V> {

            @Override
            public org.springframework.data.redis.core.RedisOperations<K, V> getOperations() {
                return InMemoryRedisTemplate.this;
            }
            @Override
            public void set(K key, V value) {
                storage.put(key, value);
            }

            @Override
            public void set(K key, V value, long timeout, TimeUnit unit) {
                storage.put(key, value);
                expirations.put(key, System.currentTimeMillis() + unit.toMillis(timeout));
            }

            @Override
            @SuppressWarnings("unchecked")
            public V get(Object key) {
                // Check if key has expired
                Long expiration = expirations.get(key);
                if (expiration != null && System.currentTimeMillis() > expiration) {
                    storage.remove(key);
                    expirations.remove(key);
                    return null;
                }
                return (V) storage.get(key);
            }

            // The following methods are not used in our application
            // but are required by the interface

            @Override
            public void set(K key, V value, long offset) { }

            @Override
            public Boolean setIfAbsent(K key, V value) { return false; }

            @Override
            public Boolean setIfAbsent(K key, V value, long timeout, TimeUnit unit) { return false; }

            @Override
            public Boolean setIfPresent(K key, V value) { return false; }

            @Override
            public Boolean setIfPresent(K key, V value, long timeout, TimeUnit unit) { return false; }

            @Override
            public void multiSet(Map<? extends K, ? extends V> map) { }

            @Override
            public Boolean multiSetIfAbsent(Map<? extends K, ? extends V> map) { return false; }

            @Override
            public Long increment(K key) { return 0L; }

            @Override
            public Long increment(K key, long delta) { return 0L; }

            @Override
            public Double increment(K key, double delta) { return 0.0; }

            @Override
            public Long decrement(K key) { return 0L; }

            @Override
            public Long decrement(K key, long delta) { return 0L; }

            @Override
            public Integer append(K key, String value) { return 0; }

            @Override
            public String get(K key, long start, long end) { return null; }

            @Override
            public List<V> multiGet(Collection<K> keys) { return null; }

            @Override
            public Long size(K key) { return 0L; }

            @Override
            public Boolean setBit(K key, long offset, boolean value) { return false; }

            @Override
            public Boolean getBit(K key, long offset) { return false; }
        }
    }
}

