package com.pressmeet247.common.config;

import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * Custom converter for string to enum conversion.
 * This converter handles case-insensitive conversion of strings to enum values.
 */
@Component
public class StringToEnumConverter {

    /**
     * Converter for NewsStatus enum.
     */
    @Component
    public static class StringToNewsStatusConverter implements Converter<String, NewsStatus> {
        @Override
        public NewsStatus convert(String source) {
            try {
                return NewsStatus.valueOf(source.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Log the error and return a default value or throw a custom exception
                throw new IllegalArgumentException("Invalid NewsStatus value: " + source);
            }
        }
    }

    /**
     * Converter for SubscriptionStatus enum.
     */
    @Component
    public static class StringToSubscriptionStatusConverter implements Converter<String, SubscriptionStatus> {
        @Override
        public SubscriptionStatus convert(String source) {
            try {
                return SubscriptionStatus.valueOf(source.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Log the error and return a default value or throw a custom exception
                throw new IllegalArgumentException("Invalid SubscriptionStatus value: " + source);
            }
        }
    }
}
