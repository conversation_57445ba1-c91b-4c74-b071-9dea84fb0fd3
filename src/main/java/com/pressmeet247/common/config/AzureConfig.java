package com.pressmeet247.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * Configuration properties for Azure services.
 */
@Data
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.azure")
public class AzureConfig {
    
    /**
     * Azure OpenAI API URL.
     */
    private String apiUrl = "https://api.openai.com/v1/chat/completions";
    
    /**
     * Azure OpenAI API key.
     */
    private String apiKey = "${AZURE_API_KEY:your-api-key}";
}

