package com.pressmeet247.admin.util;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Utility class to check admin user password.
 */
@Slf4j
//@Configuration
@RequiredArgsConstructor
public class AdminPasswordChecker {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * Creates a CommandLineRunner bean that checks the admin user password.
     *
     * @return CommandLineRunner
     */
    //@Bean
    public CommandLineRunner checkAdminPassword() {
        return args -> {
            String email = "<EMAIL>";
            String password = "admin@1234";

            User user = userRepository.findByEmail(email);
            if (user == null) {
                log.error("Admin user not found with email: {}", email);
                return;
            }

            log.info("Admin user found with email: {}", email);
            log.info("Admin user enabled: {}", user.isEnabled());
            log.info("Admin user email verified: {}", user.isEmailVerified());

            boolean passwordMatches = passwordEncoder.matches(password, user.getPassword());
            log.info("Password matches: {}", passwordMatches);

            if (!passwordMatches) {
                log.info("Current password hash: {}", user.getPassword());
                log.info("Expected password hash: {}", passwordEncoder.encode(password));

                // Update password if it doesn't match
                user.setPassword(passwordEncoder.encode(password));
                userRepository.save(user);
                log.info("Admin password updated to: {}", password);
            }
        };
    }
}
