package com.pressmeet247.admin.util;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

/**
 * Utility class to create an admin user programmatically.
 */
@Slf4j
//@Configuration
@RequiredArgsConstructor
public class AdminUserCreationTool {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * Creates a CommandLineRunner bean that creates an admin user if it doesn't exist.
     *
     * @return CommandLineRunner
     */
    //@Bean
    public CommandLineRunner createAdminUser() {
        return args -> {
            createAdminUserIfNotExists("<EMAIL>", "admin@1234");
        };
    }

    /**
     * Creates an admin user if it doesn't exist.
     *
     * @param email Admin email
     * @param password Admin password
     */
    @Transactional
    public void createAdminUserIfNotExists(String email, String password) {
        // Check if user already exists
        if (userRepository.existsByEmail(email)) {
            log.info("Admin user already exists with email: {}", email);
            return;
        }

        // Get admin role
        Optional<Role> adminRoleOpt = roleRepository.findByName("ADMIN");
        if (adminRoleOpt.isEmpty()) {
            log.error("ADMIN role not found. Cannot create admin user.");
            return;
        }

        Role adminRole = adminRoleOpt.get();

        // Create admin user
        User adminUser = new User();
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setEmail(email);
        adminUser.setPassword(passwordEncoder.encode(password));
        adminUser.setEmailVerified(true);
        adminUser.setEnabled(true);
        adminUser.setCreatedAt(LocalDateTime.now());
        adminUser.setRoles(Collections.singletonList(adminRole));

        userRepository.save(adminUser);
        log.info("Created admin user: {}", email);
    }
}
