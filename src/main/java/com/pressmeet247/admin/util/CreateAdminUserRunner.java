package com.pressmeet247.admin.util;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Standalone application to create an admin user.
 * Run this class directly to create the admin user without starting the full application.
 */
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.pressmeet247.admin.util",
    "com.pressmeet247.auth.repository",
    "com.pressmeet247.auth.entity",
    "com.pressmeet247.common.config"
})
public class CreateAdminUserRunner {

    public static void main(String[] args) {
        SpringApplication.run(CreateAdminUserRunner.class, args);
    }
}
