package com.pressmeet247.admin.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Utility class to manage admin users using direct JDBC operations.
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AdminUserJdbcManager {

    private final JdbcTemplate jdbcTemplate;
    private final PasswordEncoder passwordEncoder;

    /**
     * Creates a CommandLineRunner bean that manages the admin user.
     * 
     * @return CommandLineRunner
     */
    @Bean
    @Primary
    public CommandLineRunner manageAdminUserJdbc() {
        return args -> {
            String email = "<EMAIL>";
            String password = "admin@1234";
            
            // Check if admin user exists
            List<Map<String, Object>> users = jdbcTemplate.queryForList(
                    "SELECT id, email, password, enabled, email_verified FROM users WHERE email = ? AND deleted = 0",
                    email);
            
            if (users.isEmpty()) {
                log.info("Admin user not found with email: {}. Creating new admin user.", email);
                createAdminUser(email, password);
            } else {
                Map<String, Object> user = users.get(0);
                Long userId = ((Number) user.get("id")).longValue();
                boolean enabled = (Boolean) user.get("enabled");
                boolean emailVerified = (Boolean) user.get("email_verified");
                String storedPassword = (String) user.get("password");
                
                log.info("Admin user found with email: {}", email);
                log.info("Admin user enabled: {}", enabled);
                log.info("Admin user email verified: {}", emailVerified);
                
                // Check if user has ADMIN role
                List<Map<String, Object>> roles = jdbcTemplate.queryForList(
                        "SELECT r.id, r.name FROM roles r " +
                        "JOIN user_roles ur ON r.id = ur.role_id " +
                        "WHERE ur.user_id = ? AND r.name = 'ADMIN' AND r.deleted = 0",
                        userId);
                
                boolean hasAdminRole = !roles.isEmpty();
                
                if (!hasAdminRole) {
                    log.info("User does not have ADMIN role. Adding ADMIN role...");
                    addAdminRole(userId);
                }
                
                // Check if password matches
                boolean passwordMatches = passwordEncoder.matches(password, storedPassword);
                log.info("Password matches: {}", passwordMatches);
                
                if (!passwordMatches) {
                    log.info("Current password hash: {}", storedPassword);
                    log.info("Updating admin password...");
                    updateAdminPassword(userId, password);
                }
                
                // Ensure user is enabled and email is verified
                if (!enabled || !emailVerified) {
                    log.info("Ensuring user is enabled and email is verified...");
                    enableAndVerifyUser(userId);
                }
            }
        };
    }
    
    /**
     * Creates a new admin user.
     * 
     * @param email Admin email
     * @param password Admin password
     */
    @Transactional
    private void createAdminUser(String email, String password) {
        // Get admin role ID
        Long adminRoleId = getAdminRoleId();
        if (adminRoleId == null) {
            log.error("ADMIN role not found. Cannot create admin user.");
            return;
        }
        
        // Create user
        String encodedPassword = passwordEncoder.encode(password);
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(
                "INSERT INTO users (first_name, last_name, email, password, email_verified, enabled, created_at, deleted) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                "Admin", "User", email, encodedPassword, true, true, now, false);
        
        // Get the user ID
        Long userId = jdbcTemplate.queryForObject(
                "SELECT id FROM users WHERE email = ?",
                Long.class, email);
        
        // Add admin role
        jdbcTemplate.update(
                "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
                userId, adminRoleId);
        
        // Create user preferences
        jdbcTemplate.update(
                "INSERT INTO user_preferences (user_id, notifications_enabled, email_notifications, push_notifications, " +
                "sms_notifications, language, theme, timezone, date_format, time_format, created_at, deleted) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                userId, true, true, false, false, "en", "light", "UTC", "MM/dd/yyyy", "HH:mm", now, false);
        
        log.info("Created admin user: {}", email);
        log.info("Password hash: {}", encodedPassword);
    }
    
    /**
     * Adds the ADMIN role to a user.
     * 
     * @param userId User ID
     */
    private void addAdminRole(Long userId) {
        // Get admin role ID
        Long adminRoleId = getAdminRoleId();
        if (adminRoleId == null) {
            log.error("ADMIN role not found. Cannot add ADMIN role to user.");
            return;
        }
        
        // Check if the user already has the role
        int count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM user_roles WHERE user_id = ? AND role_id = ?",
                Integer.class, userId, adminRoleId);
        
        if (count > 0) {
            log.info("User already has ADMIN role");
            return;
        }
        
        // Add admin role
        jdbcTemplate.update(
                "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
                userId, adminRoleId);
        
        log.info("Added ADMIN role to user with ID: {}", userId);
    }
    
    /**
     * Updates an admin user's password.
     * 
     * @param userId User ID
     * @param password New password
     */
    private void updateAdminPassword(Long userId, String password) {
        String encodedPassword = passwordEncoder.encode(password);
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(
                "UPDATE users SET password = ?, last_password_change_at = ?, updated_at = ? WHERE id = ?",
                encodedPassword, now, now, userId);
        
        log.info("Updated password for user with ID: {}", userId);
    }
    
    /**
     * Ensures a user is enabled and email is verified.
     * 
     * @param userId User ID
     */
    private void enableAndVerifyUser(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(
                "UPDATE users SET enabled = ?, email_verified = ?, updated_at = ? WHERE id = ?",
                true, true, now, userId);
        
        log.info("Enabled and verified user with ID: {}", userId);
    }
    
    /**
     * Gets the admin role ID.
     * 
     * @return Admin role ID
     */
    private Long getAdminRoleId() {
        try {
            return jdbcTemplate.queryForObject(
                    "SELECT id FROM roles WHERE name = 'ADMIN' AND deleted = 0",
                    Long.class);
        } catch (Exception e) {
            log.error("Error getting ADMIN role ID: {}", e.getMessage());
            return null;
        }
    }
}
