package com.pressmeet247.admin.util;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

/**
 * Utility class to fix admin user password.
 */
@Slf4j
//@Configuration
@RequiredArgsConstructor
public class FixAdminPassword {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * Creates a CommandLineRunner bean that fixes the admin user password.
     *
     * @return CommandLineRunner
     */
    //@Bean
    public CommandLineRunner adminPasswordFixer() {
        return args -> {
            fixAdminUserPassword("<EMAIL>", "admin@1234");
        };
    }

    /**
     * Fixes an admin user's password.
     *
     * @param email Admin email
     * @param password New password
     */
    @Transactional
    public void fixAdminUserPassword(String email, String password) {
        User user = userRepository.findByEmail(email);

        if (user == null) {
            log.info("Admin user not found with email: {}. Creating new admin user.", email);

            // Get admin role
            Optional<Role> adminRoleOpt = roleRepository.findByName("ADMIN");
            if (adminRoleOpt.isEmpty()) {
                log.error("ADMIN role not found. Cannot create admin user.");
                return;
            }

            Role adminRole = adminRoleOpt.get();

            // Create admin user
            user = new User();
            user.setFirstName("Admin");
            user.setLastName("User");
            user.setEmail(email);
            user.setEmailVerified(true);
            user.setEnabled(true);
            user.setCreatedAt(LocalDateTime.now());
            user.setRoles(Collections.singletonList(adminRole));
        } else {
            log.info("Found existing admin user: {}", email);
            log.info("Current password hash: {}", user.getPassword());
        }

        // Update password with BCrypt encoding
        String encodedPassword = passwordEncoder.encode(password);
        user.setPassword(encodedPassword);

        // Ensure user is enabled and email is verified
        user.setEnabled(true);
        user.setEmailVerified(true);

        // Reset any failed login attempts and unlock the account
        user.setFailedLoginAttempts(0);
        user.setLockedUntil(null);

        userRepository.save(user);
        log.info("Admin password fixed successfully for: {}", email);
        log.info("New password hash: {}", encodedPassword);

        // Verify the password matches
        boolean passwordMatches = passwordEncoder.matches(password, encodedPassword);
        log.info("Password verification: {}", passwordMatches ? "SUCCESS" : "FAILED");
    }
}
