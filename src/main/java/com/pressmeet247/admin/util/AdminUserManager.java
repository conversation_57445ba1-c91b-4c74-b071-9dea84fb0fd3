package com.pressmeet247.admin.util;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Utility class to manage admin users.
 */
@Slf4j
//@Configuration
@RequiredArgsConstructor
public class AdminUserManager {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * Creates a CommandLineRunner bean that manages the admin user.
     *
     * @return CommandLineRunner
     */
    //@Bean
    //@Primary
    public CommandLineRunner manageAdminUser() {
        return args -> {
            String email = "<EMAIL>";
            String password = "admin@1234";

            // Check if admin user exists
            User user = userRepository.findByEmail(email);

            if (user == null) {
                log.info("Admin user not found with email: {}. Creating new admin user.", email);
                createAdminUser(email, password);
            } else {
                log.info("Admin user found with email: {}", email);
                log.info("Admin user enabled: {}", user.isEnabled());
                log.info("Admin user email verified: {}", user.isEmailVerified());

                // Check if user has ADMIN role
                boolean hasAdminRole = user.getRoles().stream()
                        .anyMatch(role -> "ADMIN".equals(role.getName()));

                if (!hasAdminRole) {
                    log.info("User does not have ADMIN role. Adding ADMIN role...");
                    addAdminRole(user);
                    user = userRepository.findByEmail(email); // Refresh user after role update
                }

                // Check if password matches
                boolean passwordMatches = passwordEncoder.matches(password, user.getPassword());
                log.info("Password matches: {}", passwordMatches);

                if (!passwordMatches) {
                    log.info("Current password hash: {}", user.getPassword());
                    log.info("Updating admin password...");
                    updateAdminUser(user, password);
                }
            }
        };
    }

    /**
     * Adds the ADMIN role to a user.
     *
     * @param user User to add ADMIN role to
     */
    @Transactional
    private void addAdminRole(User user) {
        // Get admin role
        Optional<Role> adminRoleOpt = roleRepository.findByName("ADMIN");
        if (adminRoleOpt.isEmpty()) {
            log.error("ADMIN role not found. Cannot add ADMIN role to user.");
            return;
        }

        // Get a fresh copy of the user
        User freshUser = userRepository.findById(user.getId()).orElse(null);
        if (freshUser == null) {
            log.error("Could not find user with ID: {}", user.getId());
            return;
        }

        // Get the role ID and insert directly into user_roles table using native query
        Role adminRole = adminRoleOpt.get();
        Long roleId = adminRole.getId();
        Long userId = freshUser.getId();

        // Check if the user already has the role
        boolean hasRole = freshUser.getRoles().stream()
                .anyMatch(role -> role.getId().equals(roleId));

        if (hasRole) {
            log.info("User already has ADMIN role: {}", freshUser.getEmail());
            return;
        }

        // Use the custom repository method to add the role
        try {
            userRepository.addRoleToUser(userId, roleId);
            log.info("Added ADMIN role to user: {}", freshUser.getEmail());
        } catch (Exception e) {
            log.error("Error adding ADMIN role to user: {}", e.getMessage(), e);
        }
    }

    /**
     * Creates a new admin user.
     *
     * @param email Admin email
     * @param password Admin password
     */
    @Transactional
    private User createAdminUser(String email, String password) {
        // Get admin role
        Optional<Role> adminRoleOpt = roleRepository.findByName("ADMIN");
        if (adminRoleOpt.isEmpty()) {
            log.error("ADMIN role not found. Cannot create admin user.");
            return null;
        }

        // Create admin user
        User adminUser = new User();
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setEmail(email);
        adminUser.setPassword(passwordEncoder.encode(password));
        adminUser.setEmailVerified(true);
        adminUser.setEnabled(true);
        adminUser.setCreatedAt(LocalDateTime.now());

        // Save the user first without roles
        adminUser = userRepository.save(adminUser);

        // Now add the role to the saved user
        Role adminRole = adminRoleOpt.get();
        adminUser.setRoles(Collections.singletonList(adminRole));

        // Save again with roles
        adminUser = userRepository.save(adminUser);

        log.info("Created admin user: {}", email);
        log.info("Password hash: {}", adminUser.getPassword());

        return adminUser;
    }

    /**
     * Updates an existing admin user.
     *
     * @param user Admin user
     * @param password New password
     */
    @Transactional
    private User updateAdminUser(User user, String password) {
        // Get a fresh copy of the user from the database to avoid detached entity issues
        User freshUser = userRepository.findById(user.getId()).orElse(null);
        if (freshUser == null) {
            log.error("Could not find user with ID: {}", user.getId());
            return null;
        }

        // Update password
        String encodedPassword = passwordEncoder.encode(password);
        freshUser.setPassword(encodedPassword);

        // Ensure user is enabled and email is verified
        freshUser.setEnabled(true);
        freshUser.setEmailVerified(true);

        // Reset any failed login attempts and unlock the account
        freshUser.setFailedLoginAttempts(0);
        freshUser.setLockedUntil(null);

        // Save the user
        freshUser = userRepository.save(freshUser);
        log.info("Updated admin user: {}", freshUser.getEmail());
        log.info("New password hash: {}", encodedPassword);

        return freshUser;
    }
}
