package com.pressmeet247.admin.service;

import com.pressmeet247.auth.entity.User;

import com.pressmeet247.auth.exception.AuthenticationException;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.IEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for admin OTP operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminOtpService {

    private final UserRepository userRepository;
    private final IEmailService emailService;
    
    // In-memory storage for OTPs (in production, consider using Redis or another distributed cache)
    private final Map<String, AdminOtpData> otpStore = new ConcurrentHashMap<>();
    
    // OTP expiration time in minutes
    private static final int OTP_EXPIRATION_MINUTES = 5;
    
    /**
     * Generates and sends an OTP for admin login.
     *
     * @param email Admin email
     * @return True if OTP sent successfully
     */
    @Transactional
    public boolean generateAndSendOtp(String email) {
        User user = userRepository.findByEmail(email);
        
        if (user == null) {
            log.warn("OTP generation attempted for non-existent user: {}", email);
            throw new AuthenticationException("User not found");
        }
        
        // Check if user has admin role
        boolean isAdmin = user.getRoles().stream()
                .anyMatch(role -> role.getName().equals("ADMIN"));
        
        if (!isAdmin) {
            log.warn("OTP generation attempted for non-admin user: {}", email);
            throw new AuthenticationException("User is not an admin");
        }
        
        // Generate 6-digit OTP
        String otp = generateOtp();
        
        // Store OTP with expiration time
        otpStore.put(email, new AdminOtpData(otp, LocalDateTime.now().plusMinutes(OTP_EXPIRATION_MINUTES)));
        
        // Send OTP via email
        sendOtpEmail(user, otp);
        
        log.info("Admin OTP generated and sent to: {}", email);
        return true;
    }
    
    /**
     * Verifies an admin OTP.
     *
     * @param email Admin email
     * @param otp OTP to verify
     * @return True if OTP is valid
     */
    public boolean verifyOtp(String email, String otp) {
        AdminOtpData otpData = otpStore.get(email);
        
        if (otpData == null) {
            log.warn("OTP verification attempted with no OTP generated: {}", email);
            return false;
        }
        
        if (LocalDateTime.now().isAfter(otpData.expiresAt)) {
            log.warn("OTP verification attempted with expired OTP: {}", email);
            otpStore.remove(email);
            return false;
        }
        
        if (!otpData.otp.equals(otp)) {
            log.warn("OTP verification failed for user: {}", email);
            return false;
        }
        
        // Remove OTP after successful verification
        otpStore.remove(email);
        log.info("Admin OTP verified successfully for: {}", email);
        return true;
    }
    
    /**
     * Generates a random 6-digit OTP.
     *
     * @return 6-digit OTP
     */
    private String generateOtp() {
        SecureRandom random = new SecureRandom();
        int otp = 100000 + random.nextInt(900000); // 6-digit number
        return String.valueOf(otp);
    }
    
    /**
     * Sends OTP via email.
     *
     * @param user User to send OTP to
     * @param otp OTP to send
     */
    private void sendOtpEmail(User user, String otp) {
        String subject = "Admin Login OTP Verification";
        String content = String.format(
                "Hello %s,\n\n" +
                "Your admin login OTP is: %s\n\n" +
                "This OTP will expire in %d minutes.\n\n" +
                "If you did not request this OTP, please contact support immediately.\n\n" +
                "Regards,\n" +
                "The Admin Team",
                user.getFirstName(), otp, OTP_EXPIRATION_MINUTES);
        
        emailService.sendSimpleEmail(user.getEmail(), subject, content);
    }
    
    /**
     * Inner class to store OTP data.
     */
    private static class AdminOtpData {
        private final String otp;
        private final LocalDateTime expiresAt;
        
        public AdminOtpData(String otp, LocalDateTime expiresAt) {
            this.otp = otp;
            this.expiresAt = expiresAt;
        }
    }
}



