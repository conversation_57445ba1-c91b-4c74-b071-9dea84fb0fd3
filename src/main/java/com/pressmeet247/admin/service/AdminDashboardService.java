package com.pressmeet247.admin.service;


import com.pressmeet247.admin.dto.DashboardStatsResponse;
import com.pressmeet247.ai_news.dto.NewsResponse;
import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.ai_news.repository.NewsRepository;
import com.pressmeet247.auth.dto.response.UserResponse;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import com.pressmeet247.auth.repository.*;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import com.pressmeet247.subscription.repository.SubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for admin dashboard operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminDashboardService {

    private final UserRepository userRepository;
    private final NewsRepository newsRepository;
    private final RoleRepository roleRepository;
    private final SubscriptionRepository subscriptionRepository;
    private final LoginHistoryRepository loginHistoryRepository;
    private final SecurityEventRepository securityEventRepository;

    /**
     * Gets dashboard statistics.
     *
     * @return Dashboard statistics
     */
    @Transactional(readOnly = true)
    public DashboardStatsResponse getDashboardStats() {
        long totalUsers = userRepository.count();
        long totalAdmins = userRepository.countByRoles_Name("ROLE_ADMIN");
        long totalNews = newsRepository.count();
        long pendingNews = newsRepository.countByStatus(NewsStatus.PENDING);
        long publishedNews = newsRepository.countByStatus(NewsStatus.PUBLISHED);
        long rejectedNews = newsRepository.countByStatus(NewsStatus.REJECTED);
        long totalSubscriptions = subscriptionRepository.count();
        long activeSubscriptions = subscriptionRepository.countByStatus(SubscriptionStatus.ACTIVE);

        return DashboardStatsResponse.builder()
                .totalUsers(totalUsers)
                .totalAdmins(totalAdmins)
                .totalNews(totalNews)
                .pendingNews(pendingNews)
                .publishedNews(publishedNews)
                .rejectedNews(rejectedNews)
                .totalSubscriptions(totalSubscriptions)
                .activeSubscriptions(activeSubscriptions)
                .build();
    }

    /**
     * Gets all users.
     *
     * @param pageable Pageable
     * @return Page of user responses
     */
    @Transactional(readOnly = true)
    public Page<UserResponse> getAllUsers(Pageable pageable) {
        Page<User> users = userRepository.findAll(pageable);
        return users.map(this::mapUserToResponse);
    }

    /**
     * Gets admin users.
     *
     * @param pageable Pageable
     * @return Page of user responses
     */
    @Transactional(readOnly = true)
    public Page<UserResponse> getAdminUsers(Pageable pageable) {
        Role adminRole = roleRepository.findByName("ROLE_ADMIN")
                .orElseThrow(() -> new ResourceNotFoundException("Admin role not found"));
        Page<User> admins = userRepository.findByRoles(adminRole, pageable);
        return admins.map(this::mapUserToResponse);
    }

    /**
     * Gets all news.
     *
     * @param pageable Pageable
     * @return Page of news responses
     */
    @Transactional(readOnly = true)
    public Page<NewsResponse> getAllNews(Pageable pageable) {
        Page<News> news = newsRepository.findAll(pageable);
        return news.map(NewsResponse::fromEntity);
    }

    /**
     * Gets news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Page of news responses
     */
    @Transactional(readOnly = true)
    public Page<NewsResponse> getNewsByStatus(NewsStatus status, Pageable pageable) {
        Page<News> news = newsRepository.findByStatus(status, pageable);
        return news.map(NewsResponse::fromEntity);
    }

    /**
     * Gets all news categories.
     *
     * @return List of news categories
     */
    @Transactional(readOnly = true)
    public List<String> getNewsCategories() {
        return newsRepository.findDistinctNewsTypes();
    }

    /**
     * Gets news by category.
     *
     * @param category News category
     * @param pageable Pageable
     * @return Page of news responses
     */
    @Transactional(readOnly = true)
    public Page<NewsResponse> getNewsByCategory(String category, Pageable pageable) {
        Page<News> news = newsRepository.findByNewsTypeIgnoreCase(category, pageable);
        return news.map(NewsResponse::fromEntity);
    }

    /**
     * Approves a news article.
     *
     * @param id News ID
     * @return News response
     */
    @Transactional
    public NewsResponse approveNews(Long id) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("News not found with ID: " + id));

        User currentUser = getCurrentUser();
        news.publish(currentUser);
        news.setPublishedAt(LocalDateTime.now());
        News savedNews = newsRepository.save(news);

        log.info("News approved: {}, by admin: {}", id, currentUser.getEmail());
        return NewsResponse.fromEntity(savedNews);
    }

    /**
     * Rejects a news article.
     *
     * @param id News ID
     * @return News response
     */
    @Transactional
    public NewsResponse rejectNews(Long id) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("News not found with ID: " + id));

        User currentUser = getCurrentUser();
        news.reject(currentUser);
        News savedNews = newsRepository.save(news);

        log.info("News rejected: {}, by admin: {}", id, currentUser.getEmail());
        return NewsResponse.fromEntity(savedNews);
    }

    /**
     * Sets a news article as featured.
     *
     * @param id News ID
     * @param featured Featured flag
     * @return News response
     */
    @Transactional
    public NewsResponse setNewsFeatured(Long id, boolean featured) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("News not found with ID: " + id));

        news.setFeatured(featured);
        News savedNews = newsRepository.save(news);

        log.info("News featured status updated: {}, featured: {}", id, featured);
        return NewsResponse.fromEntity(savedNews);
    }

    /**
     * Maps a user entity to a user response.
     *
     * @param user User entity
     * @return User response
     */
    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .emailVerified(user.isEmailVerified())
                .phoneNumber(user.getPhoneNumber())
                .phoneVerified(user.isPhoneVerified())
                .enabled(user.isEnabled())
                .roles(user.getRoles().stream().map(Role::getName).toList())
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();
    }

    /**
     * Gets the current authenticated user.
     *
     * @return Current user
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        return userRepository.findByEmail(email);
    }
}



