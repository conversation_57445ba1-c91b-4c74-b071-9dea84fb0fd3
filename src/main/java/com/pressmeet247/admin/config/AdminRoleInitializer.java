package com.pressmeet247.admin.config;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Initializes the admin role if it doesn't exist.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AdminRoleInitializer implements CommandLineRunner {

    private final RoleRepository roleRepository;

    @Override
    @Transactional
    public void run(String... args) {
        // Create ADMIN role if it doesn't exist
        if (!roleRepository.existsByName("ADMIN")) {
            Role adminRole = new Role();
            adminRole.setName("ADMIN");
            adminRole.setDescription("Administrator role with full access");
            adminRole.setDefault(false);
            adminRole.setPriority(100);
            roleRepository.save(adminRole);
            log.info("Created ADMIN role");
        }

        // Create USER role if it doesn't exist
        if (!roleRepository.existsByName("USER")) {
            Role userRole = new Role();
            userRole.setName("USER");
            userRole.setDescription("Regular user role with limited access");
            userRole.setDefault(true);
            userRole.setPriority(1);
            roleRepository.save(userRole);
            log.info("Created USER role");
        }
    }
}

