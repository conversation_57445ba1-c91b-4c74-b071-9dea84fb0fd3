package com.pressmeet247.admin.config;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

/**
 * Creates an admin user if specified in application properties.
 * Only runs when the "admin-setup" profile is active.
 */
@Slf4j
@Component
@Order(2)
@Profile("admin-setup")
@RequiredArgsConstructor
public class AdminUserCreator implements CommandLineRunner {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        // Get admin role
        Role adminRole = roleRepository.findByName("ADMIN")
                .orElseThrow(() -> new RuntimeException("Admin role not found"));

        // Create admin user if it doesn't exist
        String adminEmail = "<EMAIL>";
        if (!userRepository.existsByEmail(adminEmail)) {
            User adminUser = new User();
            adminUser.setFirstName("Admin");
            adminUser.setLastName("User");
            adminUser.setEmail(adminEmail);
            adminUser.setPassword(passwordEncoder.encode("Admin@123"));
            adminUser.setEmailVerified(true);
            adminUser.setEnabled(true);
            adminUser.setRoles(Collections.singletonList(adminRole));
            
            userRepository.save(adminUser);
            log.info("Created admin user: {}", adminEmail);
            log.info("Default admin password: Admin@123");
            log.warn("Please change the admin password after first login!");
        } else {
            log.info("Admin user already exists: {}", adminEmail);
        }
    }
}

