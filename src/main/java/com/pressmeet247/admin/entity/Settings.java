package com.pressmeet247.admin.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Entity representing system settings.
 * This is a singleton entity with only one row in the database.
 */
@Entity
@Table(name = "settings")
@SQLDelete(sql = "UPDATE settings SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing system settings")
public class Settings extends BaseEntity {

    @Column(name = "ai_enabled", nullable = false)
    @Schema(description = "Whether AI generation is enabled", example = "true")
    private boolean aiEnabled = true;

    @Column(name = "max_word_limit", nullable = false)
    @Schema(description = "Maximum word limit for news submissions", example = "1000")
    private int maxWordLimit = 1000;

    @Column(name = "language", length = 10, nullable = false)
    @Schema(description = "Default language for AI generation", example = "en")
    private String language = "en";

    @Column(name = "default_model", length = 50, nullable = false)
    @Schema(description = "Default AI model to use", example = "gpt-4")
    private String defaultModel = "gpt-4";

    @Column(name = "generation_delay_seconds", nullable = false)
    @Schema(description = "Delay in seconds before AI generation", example = "5")
    private int generationDelaySeconds = 5;

    @Column(name = "auto_approve_enabled", nullable = false)
    @Schema(description = "Whether auto-approval is enabled", example = "false")
    private boolean autoApproveEnabled = false;

    @Column(name = "max_submissions_per_day", nullable = false)
    @Schema(description = "Maximum submissions per user per day", example = "10")
    private int maxSubmissionsPerDay = 10;

    @Column(name = "maintenance_mode", nullable = false)
    @Schema(description = "Whether the system is in maintenance mode", example = "false")
    private boolean maintenanceMode = false;

    @Column(name = "maintenance_message", length = 500)
    @Schema(description = "Message to display during maintenance")
    private String maintenanceMessage;

    @Column(name = "email_notifications_enabled", nullable = false)
    @Schema(description = "Whether email notifications are enabled", example = "true")
    private boolean emailNotificationsEnabled = true;

    @Column(name = "whatsapp_notifications_enabled", nullable = false)
    @Schema(description = "Whether WhatsApp notifications are enabled", example = "false")
    private boolean whatsappNotificationsEnabled = false;

    /**
     * Validates the settings values.
     *
     * @throws IllegalArgumentException if any setting is invalid
     */
    public void validate() {
        if (maxWordLimit <= 0) {
            throw new IllegalArgumentException("Max word limit must be positive");
        }
        if (generationDelaySeconds < 0 || generationDelaySeconds > 120) {
            throw new IllegalArgumentException("Generation delay must be between 0 and 120 seconds");
        }
        if (maxSubmissionsPerDay <= 0) {
            throw new IllegalArgumentException("Max submissions per day must be positive");
        }
        if (language == null || language.trim().isEmpty()) {
            throw new IllegalArgumentException("Language cannot be empty");
        }
        if (defaultModel == null || defaultModel.trim().isEmpty()) {
            throw new IllegalArgumentException("Default model cannot be empty");
        }
    }
}
