package com.pressmeet247.admin.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing internal messages sent by admins.
 */
@Entity
@Table(
    name = "internal_messages",
    indexes = {
        @Index(name = "idx_internal_messages_from_admin", columnList = "from_admin_id"),
        @Index(name = "idx_internal_messages_to_user", columnList = "to_user_id"),
        @Index(name = "idx_internal_messages_type", columnList = "message_type"),
        @Index(name = "idx_internal_messages_created_at", columnList = "created_at"),
        @Index(name = "idx_internal_messages_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE internal_messages SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing internal messages")
public class InternalMessage extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "from_admin_id", nullable = false)
    @Schema(description = "Admin who sent the message")
    private User fromAdmin;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "to_user_id")
    @Schema(description = "User who received the message (null for broadcast)")
    private User toUser;

    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false, length = 20)
    @Schema(description = "Type of message", example = "EMAIL")
    private MessageType messageType;

    @Column(name = "subject", length = 255)
    @Schema(description = "Message subject", example = "Important Announcement")
    private String subject;

    @Column(name = "body", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "Message body")
    private String body;

    @Column(name = "read_at")
    @Schema(description = "Timestamp when message was read")
    private LocalDateTime readAt;

    @Column(name = "is_broadcast", nullable = false)
    @Schema(description = "Whether this is a broadcast message", example = "false")
    private boolean isBroadcast = false;

    @Column(name = "broadcast_id", length = 50)
    @Schema(description = "Broadcast identifier for grouping related messages")
    private String broadcastId;

    @Column(name = "priority", nullable = false)
    @Schema(description = "Message priority", example = "NORMAL")
    @Enumerated(EnumType.STRING)
    private MessagePriority priority = MessagePriority.NORMAL;

    @Column(name = "delivery_status", nullable = false)
    @Schema(description = "Delivery status", example = "SENT")
    @Enumerated(EnumType.STRING)
    private DeliveryStatus deliveryStatus = DeliveryStatus.PENDING;

    @Column(name = "delivery_error", length = 500)
    @Schema(description = "Error message if delivery failed")
    private String deliveryError;

    @Column(name = "sent_at")
    @Schema(description = "Timestamp when message was sent")
    private LocalDateTime sentAt;

    /**
     * Marks the message as read.
     */
    public void markAsRead() {
        this.readAt = LocalDateTime.now();
    }

    /**
     * Marks the message as sent.
     */
    public void markAsSent() {
        this.deliveryStatus = DeliveryStatus.SENT;
        this.sentAt = LocalDateTime.now();
    }

    /**
     * Marks the message as failed.
     *
     * @param errorMessage Error message
     */
    public void markAsFailed(String errorMessage) {
        this.deliveryStatus = DeliveryStatus.FAILED;
        this.deliveryError = errorMessage;
    }

    /**
     * Enum for message types.
     */
    public enum MessageType {
        EMAIL("Email"),
        WHATSAPP("WhatsApp"),
        INTERNAL("Internal"),
        SMS("SMS");

        private final String displayName;

        MessageType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Enum for message priority.
     */
    public enum MessagePriority {
        LOW("Low"),
        NORMAL("Normal"),
        HIGH("High"),
        URGENT("Urgent");

        private final String displayName;

        MessagePriority(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Enum for delivery status.
     */
    public enum DeliveryStatus {
        PENDING("Pending"),
        SENT("Sent"),
        DELIVERED("Delivered"),
        FAILED("Failed"),
        READ("Read");

        private final String displayName;

        DeliveryStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
