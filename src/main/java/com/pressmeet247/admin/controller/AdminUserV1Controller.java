package com.pressmeet247.admin.controller;

import com.pressmeet247.admin.service.AdminDashboardService;
import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.common.exception.BadRequestException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for admin user management endpoints with v1 API path.
 * This controller provides compatibility with the frontend that uses /api/v1/admin/users* paths.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin")
@RequiredArgsConstructor
@Tag(name = "Admin User Management V1", description = "Admin user management API V1")
@SecurityRequirement(name = "Bearer Authentication")
@PreAuthorize("hasRole('ADMIN')")
public class AdminUserV1Controller {

    private final AdminDashboardService adminDashboardService;

    // Map of frontend sort fields to entity fields
    private static final Map<String, String> SORT_FIELD_MAPPING = new HashMap<>();

    static {
        // Initialize the mapping
        SORT_FIELD_MAPPING.put("name", "firstName");
        SORT_FIELD_MAPPING.put("email", "email");
        SORT_FIELD_MAPPING.put("createdAt", "createdAt");
        SORT_FIELD_MAPPING.put("lastLogin", "lastLoginAt");
        SORT_FIELD_MAPPING.put("status", "enabled");
        // Map 'submissions' to a default sort field since it doesn't exist
        SORT_FIELD_MAPPING.put("submissions", "createdAt");
    }

    /**
     * Gets all users with custom pagination and sorting.
     *
     * @param page Page number (0-based)
     * @param limit Number of items per page
     * @param sort Sort field
     * @param direction Sort direction (asc or desc)
     * @return Response entity with paged users
     */
    @GetMapping("/users")
    @Operation(summary = "Get all users", description = "Returns all users (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String sort,
            @RequestParam(defaultValue = "desc") String direction) {

        log.debug("V1 API - Getting all users with page={}, limit={}, sort={}, direction={}", page, limit, sort, direction);

        // Create pageable with custom handling for sort field
        Pageable pageable;
        if (sort != null && !sort.isEmpty()) {
            // Map frontend sort field to entity field
            String entityField = SORT_FIELD_MAPPING.getOrDefault(sort, "createdAt");
            log.debug("Mapping sort field '{}' to entity field '{}'", sort, entityField);

            // Create sort object
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ?
                    Sort.Direction.ASC : Sort.Direction.DESC;
            Sort sortObj = Sort.by(sortDirection, entityField);

            pageable = PageRequest.of(page, limit, sortObj);
        } else {
            // Default sort by createdAt desc
            pageable = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        try {
            Page<UserResponse> users = adminDashboardService.getAllUsers(pageable);
            return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", users));
        } catch (Exception e) {
            log.error("Error getting users: {}", e.getMessage(), e);
            throw new BadRequestException("Error retrieving users: " + e.getMessage());
        }
    }

    /**
     * Gets admin users with custom pagination and sorting.
     *
     * @param page Page number (0-based)
     * @param limit Number of items per page
     * @param sort Sort field
     * @param direction Sort direction (asc or desc)
     * @return Response entity with paged admin users
     */
    @GetMapping("/users/admins")
    @Operation(summary = "Get admin users", description = "Returns admin users (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAdminUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String sort,
            @RequestParam(defaultValue = "desc") String direction) {

        log.debug("V1 API - Getting admin users with page={}, limit={}, sort={}, direction={}", page, limit, sort, direction);

        // Create pageable with custom handling for sort field
        Pageable pageable;
        if (sort != null && !sort.isEmpty()) {
            // Map frontend sort field to entity field
            String entityField = SORT_FIELD_MAPPING.getOrDefault(sort, "createdAt");
            log.debug("Mapping sort field '{}' to entity field '{}'", sort, entityField);

            // Create sort object
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ?
                    Sort.Direction.ASC : Sort.Direction.DESC;
            Sort sortObj = Sort.by(sortDirection, entityField);

            pageable = PageRequest.of(page, limit, sortObj);
        } else {
            // Default sort by createdAt desc
            pageable = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        try {
            Page<UserResponse> admins = adminDashboardService.getAdminUsers(pageable);
            return ResponseEntity.ok(ApiResponse.success("Admin users retrieved successfully", admins));
        } catch (Exception e) {
            log.error("Error getting admin users: {}", e.getMessage(), e);
            throw new BadRequestException("Error retrieving admin users: " + e.getMessage());
        }
    }
}
