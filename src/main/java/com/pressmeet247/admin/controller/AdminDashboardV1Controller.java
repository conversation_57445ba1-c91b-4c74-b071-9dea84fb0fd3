package com.pressmeet247.admin.controller;

import com.pressmeet247.admin.dto.DashboardStatsResponse;
import com.pressmeet247.admin.service.AdminDashboardService;
import com.pressmeet247.ai_news.dto.NewsResponse;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.auth.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for admin dashboard endpoints with v1 API path.
 * This controller provides compatibility with the frontend that uses /api/v1/admin/dashboard/* paths.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/dashboard")
@RequiredArgsConstructor
@Tag(name = "Admin Dashboard V1", description = "Admin dashboard API V1")
@SecurityRequirement(name = "Bearer Authentication")
@PreAuthorize("hasRole('ADMIN')")
public class AdminDashboardV1Controller {

    private final AdminDashboardService adminDashboardService;

    /**
     * Gets dashboard statistics.
     *
     * @return Response entity with dashboard statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get dashboard statistics", description = "Returns dashboard statistics (requires ADMIN role)")
    public ResponseEntity<ApiResponse<DashboardStatsResponse>> getDashboardStats() {
        log.debug("V1 API - Getting dashboard statistics");
        DashboardStatsResponse stats = adminDashboardService.getDashboardStats();
        return ResponseEntity.ok(ApiResponse.success("Dashboard statistics retrieved successfully", stats));
    }

    /**
     * Gets all news categories.
     *
     * @return Response entity with list of news categories
     */
    @GetMapping("/news/categories")
    @Operation(summary = "Get all news categories", description = "Returns all news categories (requires ADMIN role)")
    public ResponseEntity<ApiResponse<List<String>>> getNewsCategories() {
        log.debug("V1 API - Getting all news categories");
        List<String> categories = adminDashboardService.getNewsCategories();
        return ResponseEntity.ok(ApiResponse.success("News categories retrieved successfully", categories));
    }

    /**
     * Special endpoint to handle the specific URL pattern that's causing the error.
     * This endpoint is specifically for the URL /api/v1/admin/dashboard/news/status/categories
     *
     * @param pageable Pageable
     * @return Response entity with list of news categories
     */
    @GetMapping("/news/status/categories")
    @Operation(summary = "Get all news categories (alternative URL)", description = "Returns all news categories (requires ADMIN role)")
    public ResponseEntity<ApiResponse<List<String>>> getNewsCategoriesAlternative(Pageable pageable) {
        log.debug("V1 API - Getting all news categories (alternative URL)");
        List<String> categories = adminDashboardService.getNewsCategories();
        return ResponseEntity.ok(ApiResponse.success("News categories retrieved successfully", categories));
    }

    /**
     * Gets news by category.
     *
     * @param category News category
     * @param pageable Pageable
     * @return Response entity with paged news
     */
    @GetMapping("/news/category/{category}")
    @Operation(summary = "Get news by category", description = "Returns news by category (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByCategory(
            @PathVariable String category,
            Pageable pageable) {
        log.debug("V1 API - Getting news by category: {}", category);
        Page<NewsResponse> news = adminDashboardService.getNewsByCategory(category, pageable);
        return ResponseEntity.ok(ApiResponse.success("News retrieved successfully", news));
    }

    /**
     * Gets news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Response entity with paged news
     */
    @GetMapping("/news/status/{status}")
    @Operation(summary = "Get news by status", description = "Returns news by status (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByStatus(
            @PathVariable NewsStatus status,
            Pageable pageable) {
        log.debug("V1 API - Getting news by status: {}", status);
        Page<NewsResponse> news = adminDashboardService.getNewsByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success("News retrieved successfully", news));
    }
}
