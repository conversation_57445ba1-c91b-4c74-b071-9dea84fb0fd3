package com.pressmeet247.admin.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.admin.dto.DashboardStatsResponse;
import com.pressmeet247.ai_news.dto.NewsResponse;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.admin.service.AdminDashboardService;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for admin dashboard endpoints.
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/dashboard")
@RequiredArgsConstructor
@Tag(name = "Admin Dashboard", description = "Admin dashboard API")
@SecurityRequirement(name = "Bearer Authentication")
@PreAuthorize("hasRole('ADMIN')")
public class AdminDashboardController {

    private final AdminDashboardService adminDashboardService;

    /**
     * Welcome endpoint for admin dashboard.
     *
     * @return Response entity with welcome message
     */
    @GetMapping
    @Operation(summary = "Welcome to admin dashboard", description = "Returns a welcome message for the admin dashboard")
    public ResponseEntity<ApiResponse<String>> welcome() {
        return ResponseEntity.ok(ApiResponse.success(
                "Welcome to the admin dashboard",
                "You have successfully logged in as an admin."
        ));
    }

    /**
     * Gets dashboard statistics.
     *
     * @return Response entity with dashboard statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get dashboard statistics", description = "Returns dashboard statistics (requires ADMIN role)")
    public ResponseEntity<ApiResponse<DashboardStatsResponse>> getDashboardStats() {
        DashboardStatsResponse stats = adminDashboardService.getDashboardStats();
        return ResponseEntity.ok(ApiResponse.success("Dashboard statistics retrieved successfully", stats));
    }

    /**
     * Gets all users.
     *
     * @param pageable Pageable
     * @return Response entity with paged users
     */
    @GetMapping("/users")
    @Operation(summary = "Get all users", description = "Returns all users (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAllUsers(Pageable pageable) {
        Page<UserResponse> users = adminDashboardService.getAllUsers(pageable);
        return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", users));
    }

    /**
     * Gets admin users.
     *
     * @param pageable Pageable
     * @return Response entity with paged admin users
     */
    @GetMapping("/users/admins")
    @Operation(summary = "Get admin users", description = "Returns admin users (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAdminUsers(Pageable pageable) {
        Page<UserResponse> admins = adminDashboardService.getAdminUsers(pageable);
        return ResponseEntity.ok(ApiResponse.success("Admin users retrieved successfully", admins));
    }

    /**
     * Gets all news.
     *
     * @param pageable Pageable
     * @return Response entity with paged news
     */
    @GetMapping("/news")
    @Operation(summary = "Get all news", description = "Returns all news (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getAllNews(Pageable pageable) {
        Page<NewsResponse> news = adminDashboardService.getAllNews(pageable);
        return ResponseEntity.ok(ApiResponse.success("News retrieved successfully", news));
    }

    /**
     * Gets news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Response entity with paged news
     */
    @GetMapping("/news/status/{status}")
    @Operation(summary = "Get news by status", description = "Returns news by status (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByStatus(
            @PathVariable NewsStatus status,
            Pageable pageable) {
        Page<NewsResponse> news = adminDashboardService.getNewsByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success("News retrieved successfully", news));
    }

    /**
     * Gets all news categories.
     *
     * @return Response entity with list of news categories
     */
    @GetMapping("/news/categories")
    @Operation(summary = "Get all news categories", description = "Returns all news categories (requires ADMIN role)")
    public ResponseEntity<ApiResponse<List<String>>> getNewsCategories() {
        List<String> categories = adminDashboardService.getNewsCategories();
        return ResponseEntity.ok(ApiResponse.success("News categories retrieved successfully", categories));
    }

    /**
     * Gets news by category.
     *
     * @param category News category
     * @param pageable Pageable
     * @return Response entity with paged news
     */
    @GetMapping("/news/category/{category}")
    @Operation(summary = "Get news by category", description = "Returns news by category (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByCategory(
            @PathVariable String category,
            Pageable pageable) {
        Page<NewsResponse> news = adminDashboardService.getNewsByCategory(category, pageable);
        return ResponseEntity.ok(ApiResponse.success("News retrieved successfully", news));
    }

    /**
     * Approves a news article.
     *
     * @param id News ID
     * @return Response entity
     */
    @PostMapping("/news/{id}/approve")
    @Operation(summary = "Approve news", description = "Approves a news article (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> approveNews(@PathVariable Long id) {
        NewsResponse news = adminDashboardService.approveNews(id);
        return ResponseEntity.ok(ApiResponse.success("News approved successfully", news));
    }

    /**
     * Rejects a news article.
     *
     * @param id News ID
     * @return Response entity
     */
    @PostMapping("/news/{id}/reject")
    @Operation(summary = "Reject news", description = "Rejects a news article (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> rejectNews(@PathVariable Long id) {
        NewsResponse news = adminDashboardService.rejectNews(id);
        return ResponseEntity.ok(ApiResponse.success("News rejected successfully", news));
    }

    /**
     * Sets a news article as featured.
     *
     * @param id News ID
     * @param featured Featured flag
     * @return Response entity
     */
    @PutMapping("/news/{id}/featured")
    @Operation(summary = "Set news as featured", description = "Sets a news article as featured (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> setNewsFeatured(
            @PathVariable Long id,
            @RequestParam boolean featured) {
        NewsResponse news = adminDashboardService.setNewsFeatured(id, featured);
        return ResponseEntity.ok(ApiResponse.success("News featured status updated successfully", news));
    }
}



