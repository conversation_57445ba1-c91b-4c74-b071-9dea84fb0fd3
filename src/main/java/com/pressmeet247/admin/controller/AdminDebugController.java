package com.pressmeet247.admin.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.util.CookieUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for admin debugging endpoints.
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/debug")
@RequiredArgsConstructor
@Tag(name = "Admin Debug", description = "Admin debugging API")
public class AdminDebugController {

    private final CookieUtils cookieUtils;

    /**
     * Gets authentication information for debugging.
     *
     * @param request HTTP request
     * @return Response entity
     */
    @GetMapping("/auth-info")
    @Operation(summary = "Get authentication info", description = "Returns authentication information for debugging")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAuthInfo(HttpServletRequest request) {
        Map<String, Object> authInfo = new HashMap<>();
        
        // Get authentication from security context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null) {
            authInfo.put("authenticated", authentication.isAuthenticated());
            authInfo.put("principal", authentication.getPrincipal().toString());
            authInfo.put("name", authentication.getName());
            authInfo.put("authorities", authentication.getAuthorities().toString());
            
            if (authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                authInfo.put("userId", user.getId());
                authInfo.put("email", user.getEmail());
                authInfo.put("roles", user.getRoles().stream()
                        .map(role -> role.getName())
                        .collect(Collectors.toList()));
            }
        } else {
            authInfo.put("authenticated", false);
            authInfo.put("message", "No authentication found in security context");
        }
        
        // Get request headers
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        authInfo.put("headers", headers);
        
        // Get cookies
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            Map<String, String> cookieMap = Arrays.stream(cookies)
                    .collect(Collectors.toMap(
                            Cookie::getName,
                            cookie -> cookie.getValue() != null ? "[present]" : "[empty]"
                    ));
            authInfo.put("cookies", cookieMap);
            
            // Check for JWT token in cookies
            String jwtToken = cookieUtils.getJwtFromCookies(request);
            authInfo.put("jwtTokenFromCookie", jwtToken != null ? "present" : "not found");
            
            // Check for refresh token in cookies
            String refreshToken = cookieUtils.getRefreshTokenFromCookies(request);
            authInfo.put("refreshTokenFromCookie", refreshToken != null ? "present" : "not found");
        } else {
            authInfo.put("cookies", "No cookies found");
            authInfo.put("jwtTokenFromCookie", "not found");
            authInfo.put("refreshTokenFromCookie", "not found");
        }
        
        // Get request details
        authInfo.put("requestURI", request.getRequestURI());
        authInfo.put("requestMethod", request.getMethod());
        authInfo.put("remoteAddr", request.getRemoteAddr());
        authInfo.put("userAgent", request.getHeader("User-Agent"));
        
        return ResponseEntity.ok(ApiResponse.success(
                "Authentication information retrieved successfully",
                authInfo
        ));
    }
}
