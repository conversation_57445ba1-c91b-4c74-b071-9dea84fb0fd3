package com.pressmeet247.admin.controller;

import com.pressmeet247.admin.dto.AdminLoginRequest;
import com.pressmeet247.admin.dto.AdminOtpVerificationRequest;
import com.pressmeet247.admin.dto.AdminAuthResponse;
import com.pressmeet247.auth.dto.request.LoginRequest;
import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.dto.response.AuthResponse;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.exception.AuthenticationException;
import com.pressmeet247.admin.service.AdminOtpService;
import com.pressmeet247.auth.service.AuthService;
import com.pressmeet247.common.util.CookieUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for admin authentication endpoints.
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/auth")
@RequiredArgsConstructor
@Tag(name = "Admin Authentication", description = "Admin authentication API")
public class AdminAuthController {

    private final AuthService authService;
    private final AdminOtpService adminOtpService;
    private final CookieUtils cookieUtils;

    @Value("${app.admin.dashboard-url:/admin/dashboard}")
    private String adminDashboardUrl;

    /**
     * Gets the current admin user.
     *
     * @param user Authenticated user
     * @return Response entity
     */
    @GetMapping("/me")
    @Operation(summary = "Get current admin", description = "Returns the current authenticated admin user")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentAdmin(@AuthenticationPrincipal User user) {
        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Not authenticated"));
        }

        // Check if user has admin role
        boolean isAdmin = user.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN"));

        if (!isAdmin) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Not authorized as admin"));
        }

        UserResponse userResponse = UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .enabled(user.isEnabled())
                .emailVerified(user.isEmailVerified())
                .roles(user.getRoles().stream()
                        .map(role -> role.getName())
                        .toList())
                .createdAt(user.getCreatedAt())
                .build();

        return ResponseEntity.ok(ApiResponse.success(
                "Admin profile retrieved successfully",
                userResponse
        ));
    }

    /**
     * Initiates admin login by sending OTP.
     *
     * @param request Admin login request
     * @return Response entity
     */
    @PostMapping("/login/initiate")
    @Operation(summary = "Initiate admin login", description = "Initiates admin login by sending OTP")
    public ResponseEntity<ApiResponse<Boolean>> initiateLogin(@Valid @RequestBody AdminLoginRequest request) {
        try {
            // Validate credentials first (without generating tokens)
            // Wrap this in a try-catch to handle specific authentication errors
            try {
                authService.validateCredentials(request.getEmail(), request.getPassword());
            } catch (AuthenticationException e) {
                log.warn("Admin login initiation failed: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(e.getMessage()));
            }

            // Generate and send OTP
            adminOtpService.generateAndSendOtp(request.getEmail());

            return ResponseEntity.ok(ApiResponse.success(
                    "OTP sent successfully. Please verify to complete login.",
                    true
            ));
        } catch (AuthenticationException e) {
            log.warn("Admin login initiation failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during admin login initiation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Completes admin login by verifying OTP.
     *
     * @param request Admin OTP verification request
     * @param httpRequest HTTP request
     * @return Response entity
     */
    @PostMapping("/login/verify")
    @Operation(summary = "Verify admin login OTP", description = "Completes admin login by verifying OTP")
    public ResponseEntity<ApiResponse<AdminAuthResponse>> verifyLoginOtp(
            @Valid @RequestBody AdminOtpVerificationRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            // Verify OTP
            boolean isValid = adminOtpService.verifyOtp(request.getEmail(), request.getOtp());

            if (!isValid) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("Invalid or expired OTP"));
            }

            // Complete login and generate tokens
            LoginRequest loginRequest = new LoginRequest(request.getEmail(), request.getPassword());
            AuthResponse authResponse = authService.login(loginRequest, httpRequest);

            // Create admin auth response with redirect URL
            AdminAuthResponse adminAuthResponse = AdminAuthResponse.fromAuthResponse(
                    authResponse, adminDashboardUrl);

            // Add JWT token to cookie
            cookieUtils.addJwtCookie(httpResponse, authResponse.getAccessToken());

            // Add refresh token to cookie if present
            if (authResponse.getRefreshToken() != null) {
                cookieUtils.addRefreshTokenCookie(httpResponse, authResponse.getRefreshToken());
            }

            // Log the token for debugging
            log.info("Generated token for admin user: {}", request.getEmail());
            log.info("Token type: {}", authResponse.getTokenType());
            log.info("Token expiration: {}", authResponse.getExpirationTime());

            // Add additional headers to help frontend with redirection
            return ResponseEntity
                    .ok()
                    .header("X-Auth-Redirect", adminDashboardUrl)
                    .header("Access-Control-Expose-Headers", "X-Auth-Redirect")
                    .body(ApiResponse.success(
                            "Admin login successful. Authentication cookies have been set.",
                            adminAuthResponse
                    ));
        } catch (AuthenticationException e) {
            log.warn("Admin login verification failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during admin login verification: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Logs out an admin user.
     *
     * @param user Authenticated user
     * @param response HTTP response
     * @return Response entity
     */
    @PostMapping("/logout")
    @Operation(summary = "Logout", description = "Logs out an admin user by invalidating their tokens")
    public ResponseEntity<ApiResponse<Void>> logout(
            @AuthenticationPrincipal User user,
            HttpServletResponse response) {
        if (user != null) {
            authService.logout(user);
        }

        // Clear cookies
        cookieUtils.clearJwtCookie(response);
        cookieUtils.clearRefreshTokenCookie(response);

        return ResponseEntity.ok(ApiResponse.success("Logged out successfully"));
    }
}



