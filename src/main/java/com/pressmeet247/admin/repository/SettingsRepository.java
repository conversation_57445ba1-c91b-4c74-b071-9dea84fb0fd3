package com.pressmeet247.admin.repository;

import com.pressmeet247.admin.entity.Settings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for Settings entity operations.
 * Settings is a singleton entity with only one row.
 */
@Repository
public interface SettingsRepository extends JpaRepository<Settings, Long> {

    /**
     * Find the current settings (should only be one row).
     *
     * @return Optional settings
     */
    @Query("SELECT s FROM Settings s ORDER BY s.id ASC")
    Optional<Settings> findCurrentSettings();

    /**
     * Check if settings exist.
     *
     * @return true if settings exist
     */
    @Query("SELECT COUNT(s) > 0 FROM Settings s")
    boolean existsSettings();
}
