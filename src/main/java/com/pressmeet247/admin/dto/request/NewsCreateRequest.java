package com.pressmeet247.admin.dto.request;

import com.pressmeet247.ai_news.entity.NewsStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for creating or updating news.
 */
@Data
@Schema(description = "Request for creating or updating news")
public class NewsCreateRequest {

    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    @Schema(description = "News title", example = "Breaking: New AI Technology Announced")
    private String title;

    @NotBlank(message = "Content is required")
    @Schema(description = "News content", example = "A revolutionary AI technology has been announced...")
    private String content;

    @Schema(description = "News summary", example = "Brief summary of the news article")
    private String summary;

    @Schema(description = "News type", example = "Technology")
    private String newsType;

    @Schema(description = "Category ID", example = "1")
    private Long categoryId;

    @Schema(description = "List of tag names", example = "[\"AI\", \"Technology\", \"Innovation\"]")
    private List<String> tagNames;

    @Schema(description = "News status", example = "PENDING")
    private NewsStatus status = NewsStatus.PENDING;

    @Schema(description = "Whether the news is featured", example = "false")
    private boolean featured = false;

    @Schema(description = "Author user ID (if different from current user)", example = "123")
    private Long authorUserId;

    @Schema(description = "Image URL", example = "https://example.com/image.jpg")
    private String imageUrl;

    @Schema(description = "Source URL", example = "https://example.com/source")
    private String sourceUrl;

    @Schema(description = "Source name", example = "Tech News Daily")
    private String sourceName;

    @Schema(description = "Meta description for SEO", example = "Latest AI technology news and updates")
    private String metaDescription;

    @Schema(description = "Meta keywords for SEO", example = "AI, technology, innovation")
    private String metaKeywords;
}
