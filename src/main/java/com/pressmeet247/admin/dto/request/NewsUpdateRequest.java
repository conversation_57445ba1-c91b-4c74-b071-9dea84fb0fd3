package com.pressmeet247.admin.dto.request;

import com.pressmeet247.ai_news.entity.NewsStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for updating news.
 */
@Data
@Schema(description = "Request for updating news")
public class NewsUpdateRequest {

    @Size(max = 255, message = "Title must not exceed 255 characters")
    @Schema(description = "News title", example = "Updated: New AI Technology Announced")
    private String title;

    @Schema(description = "News content", example = "Updated content of the news article...")
    private String content;

    @Schema(description = "News summary", example = "Updated brief summary of the news article")
    private String summary;

    @Schema(description = "News type", example = "Technology")
    private String newsType;

    @Schema(description = "Category ID", example = "1")
    private Long categoryId;

    @Schema(description = "List of tag names", example = "[\"AI\", \"Technology\", \"Innovation\"]")
    private List<String> tagNames;

    @Schema(description = "News status", example = "APPROVED")
    private NewsStatus status;

    @Schema(description = "Whether the news is featured", example = "true")
    private Boolean featured;

    @Schema(description = "Image URL", example = "https://example.com/updated-image.jpg")
    private String imageUrl;

    @Schema(description = "Source URL", example = "https://example.com/updated-source")
    private String sourceUrl;

    @Schema(description = "Source name", example = "Updated Tech News Daily")
    private String sourceName;

    @Schema(description = "Meta description for SEO", example = "Updated AI technology news and updates")
    private String metaDescription;

    @Schema(description = "Meta keywords for SEO", example = "AI, technology, innovation, updated")
    private String metaKeywords;
}
