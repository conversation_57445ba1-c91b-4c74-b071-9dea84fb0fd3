package com.pressmeet247.admin.dto;

import com.pressmeet247.auth.dto.response.AuthResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for admin authentication response.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Admin authentication response")
public class AdminAuthResponse {
    
    @Schema(description = "Authentication response with tokens")
    private AuthResponse authResponse;
    
    @Schema(description = "Redirect URL to admin dashboard", example = "/admin/dashboard")
    private String redirectUrl;
    
    /**
     * Creates an AdminAuthResponse from an AuthResponse.
     *
     * @param authResponse Authentication response
     * @param redirectUrl Redirect URL
     * @return AdminAuthResponse
     */
    public static AdminAuthResponse fromAuthResponse(AuthResponse authResponse, String redirectUrl) {
        return new AdminAuthResponse(authResponse, redirectUrl);
    }
}
