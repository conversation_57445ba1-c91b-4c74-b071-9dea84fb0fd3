package com.pressmeet247.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * DTO for dashboard statistics response.
 */
@Data
@Builder
@Schema(description = "Dashboard statistics response")
public class DashboardStatsResponse {
    
    @Schema(description = "Total number of users", example = "100")
    private long totalUsers;
    
    @Schema(description = "Total number of admin users", example = "5")
    private long totalAdmins;
    
    @Schema(description = "Total number of news articles", example = "50")
    private long totalNews;
    
    @Schema(description = "Number of pending news articles", example = "10")
    private long pendingNews;
    
    @Schema(description = "Number of published news articles", example = "35")
    private long publishedNews;
    
    @Schema(description = "Number of rejected news articles", example = "5")
    private long rejectedNews;
    
    @Schema(description = "Total number of subscriptions", example = "30")
    private long totalSubscriptions;
    
    @Schema(description = "Number of active subscriptions", example = "25")
    private long activeSubscriptions;
}



