package com.pressmeet247.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for admin OTP verification request.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Admin OTP verification request")
public class AdminOtpVerificationRequest {
    
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    @Schema(description = "Admin email", example = "<EMAIL>")
    private String email;
    
    @NotBlank(message = "OTP is required")
    @Pattern(regexp = "^\\d{6}$", message = "OTP must be a 6-digit number")
    @Schema(description = "OTP code", example = "123456")
    private String otp;
    
    @NotBlank(message = "Password is required")
    @Schema(description = "Admin password", example = "Admin@123")
    private String password;
}

