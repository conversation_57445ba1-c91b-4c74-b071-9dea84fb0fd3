package com.pressmeet247.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for admin login request.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Admin login request")
public class AdminLoginRequest {
    
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    @Schema(description = "Admin email", example = "<EMAIL>")
    private String email;
    
    @NotBlank(message = "Password is required")
    @Schema(description = "Admin password", example = "Admin@123")
    private String password;
}

