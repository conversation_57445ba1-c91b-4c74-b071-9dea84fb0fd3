package com.pressmeet247.news.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a news category.
 */
@Entity
@Table(
    name = "categories",
    indexes = {
        @Index(name = "idx_category_name", columnList = "name", unique = true),
        @Index(name = "idx_category_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE categories SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a news category")
public class Category extends BaseEntity {

    @Column(name = "name", length = 100, nullable = false, unique = true)
    @Schema(description = "Category name", example = "Technology")
    private String name;

    @Column(name = "description", length = 500)
    @Schema(description = "Category description", example = "Technology and innovation news")
    private String description;

    @Column(name = "color", length = 7)
    @Schema(description = "Category color in hex format", example = "#FF5733")
    private String color;

    @Column(name = "icon", length = 50)
    @Schema(description = "Category icon name", example = "tech-icon")
    private String icon;

    @Column(name = "is_active", nullable = false)
    @Schema(description = "Whether the category is active", example = "true")
    private boolean isActive = true;

    @Column(name = "sort_order", nullable = false)
    @Schema(description = "Sort order for display", example = "1")
    private int sortOrder = 0;

    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Schema(description = "News articles in this category")
    private List<News> newsArticles = new ArrayList<>();

    /**
     * Gets the count of news articles in this category.
     *
     * @return Number of news articles
     */
    public int getNewsCount() {
        return newsArticles != null ? newsArticles.size() : 0;
    }

    /**
     * Gets the count of approved news articles in this category.
     *
     * @return Number of approved news articles
     */
    public long getApprovedNewsCount() {
        return newsArticles != null ? 
            newsArticles.stream()
                .filter(news -> news.getStatus() == NewsStatus.APPROVED)
                .count() : 0;
    }
}
