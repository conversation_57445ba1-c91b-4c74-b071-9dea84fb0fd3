package com.pressmeet247.ai_news.entity;

/**
 * Enum representing the status of a news article.
 */
public enum NewsStatus {
    /**
     * News is pending approval.
     */
    PENDING("Pending"),

    /**
     * News has been approved.
     */
    APPROVED("Approved"),

    /**
     * News has been rejected.
     */
    REJECTED("Rejected"),

    /**
     * News is in draft state.
     */
    DRAFT("Draft"),

    /**
     * News has been published (approved + published).
     */
    PUBLISHED("Published");

    private final String displayName;

    NewsStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}

