package com.pressmeet247.ai_news.entity;

/**
 * Enum representing the status of a news article.
 */
public enum NewsStatus {
    PENDING("Pending Review"),
    APPROVED("Approved"),
    REJECTED("Rejected"),
    PUBLISHED("Published"),
    DRAFT("Draft"),
    ARCHIVED("Archived");

    private final String displayName;

    NewsStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
