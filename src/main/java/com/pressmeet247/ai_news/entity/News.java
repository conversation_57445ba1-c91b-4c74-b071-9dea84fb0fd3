package com.pressmeet247.ai_news.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Entity representing a news article in the system.
 */
@Entity
@Table(
    name = "news",
    indexes = {
        @Index(name = "idx_news_user_id", columnList = "user_id"),
        @Index(name = "idx_news_status", columnList = "status"),
        @Index(name = "idx_news_created_at", columnList = "created_at"),
        @Index(name = "idx_news_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE news SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a news article")
public class News extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @Schema(description = "User who submitted the news")
    private User user;

    @Column(name = "title", length = 255, nullable = false)
    @Schema(description = "News title", example = "Local Community Event")
    private String title;

    @Column(name = "content", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "News content")
    private String content;

    @Column(name = "date", length = 50)
    @Schema(description = "Date of the event", example = "2023-04-15")
    private String date;

    @Column(name = "place", length = 255)
    @Schema(description = "Place of the event", example = "City Hall")
    private String place;

    @Column(name = "what_happened", columnDefinition = "TEXT")
    @Schema(description = "Description of what happened")
    private String whatHappened;

    @Column(name = "where_happened", length = 255)
    @Schema(description = "Location details of the event")
    private String whereHappened;

    @Column(name = "who_involved", length = 255)
    @Schema(description = "People or entities involved")
    private String whoInvolved;

    @Column(name = "when_happened", length = 100)
    @Schema(description = "Time of the event")
    private String whenHappened;

    @Column(name = "who_told", length = 255)
    @Schema(description = "Source of the information")
    private String whoTold;

    @Column(name = "how_happened", columnDefinition = "TEXT")
    @Schema(description = "Explanation of how the event occurred")
    private String howHappened;

    @Column(name = "why_happened", columnDefinition = "TEXT")
    @Schema(description = "Reason behind the event")
    private String whyHappened;

    @Column(name = "news_type", length = 50)
    @Schema(description = "Type of news", example = "Politics")
    private String newsType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Schema(description = "Status of the news article", example = "PENDING")
    private NewsStatus status = NewsStatus.PENDING;

    @Column(name = "published_at")
    @Schema(description = "Date and time when the news was published")
    private java.time.LocalDateTime publishedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    @Schema(description = "Admin who approved the news")
    private User approvedBy;

    @Column(name = "approved_at")
    @Schema(description = "Date and time when the news was approved")
    private java.time.LocalDateTime approvedAt;

    @Column(name = "featured", nullable = false)
    @Schema(description = "Whether the news is featured", example = "false")
    private boolean featured = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id")
    @Schema(description = "News category")
    private Category category;

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "news_tags",
        joinColumns = @JoinColumn(name = "news_id"),
        inverseJoinColumns = @JoinColumn(name = "tag_id"),
        indexes = {
            @Index(name = "idx_news_tags_news_id", columnList = "news_id"),
            @Index(name = "idx_news_tags_tag_id", columnList = "tag_id")
        }
    )
    @Schema(description = "News tags")
    private List<Tag> tags = new java.util.ArrayList<>();

    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    @Schema(description = "Reason for rejection if status is REJECTED")
    private String rejectionReason;

    /**
     * Approves the news article.
     *
     * @param approver The user who approved the news
     */
    public void approve(User approver) {
        this.status = NewsStatus.APPROVED;
        this.approvedBy = approver;
        this.approvedAt = java.time.LocalDateTime.now();
        this.rejectionReason = null; // Clear any previous rejection reason
    }

    /**
     * Publishes the news article.
     *
     * @param approver The user who approved the news
     */
    public void publish(User approver) {
        this.status = NewsStatus.PUBLISHED;
        this.approvedBy = approver;
        this.approvedAt = java.time.LocalDateTime.now();
        this.publishedAt = java.time.LocalDateTime.now();
        this.rejectionReason = null; // Clear any previous rejection reason
    }

    /**
     * Rejects the news article.
     *
     * @param approver The user who rejected the news
     * @param reason The reason for rejection
     */
    public void reject(User approver, String reason) {
        this.status = NewsStatus.REJECTED;
        this.approvedBy = approver;
        this.approvedAt = java.time.LocalDateTime.now();
        this.rejectionReason = reason;
    }

    /**
     * Adds a tag to the news article.
     *
     * @param tag The tag to add
     */
    public void addTag(Tag tag) {
        if (this.tags == null) {
            this.tags = new java.util.ArrayList<>();
        }
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
            tag.incrementUsageCount();
        }
    }

    /**
     * Removes a tag from the news article.
     *
     * @param tag The tag to remove
     */
    public void removeTag(Tag tag) {
        if (this.tags != null && this.tags.contains(tag)) {
            this.tags.remove(tag);
            tag.decrementUsageCount();
        }
    }
}




