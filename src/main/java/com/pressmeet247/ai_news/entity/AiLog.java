package com.pressmeet247.ai_news.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing AI processing logs.
 */
@Entity
@Table(
    name = "ai_logs",
    indexes = {
        @Index(name = "idx_ai_logs_user_id", columnList = "user_id"),
        @Index(name = "idx_ai_logs_status", columnList = "status"),
        @Index(name = "idx_ai_logs_timestamp", columnList = "timestamp"),
        @Index(name = "idx_ai_logs_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE ai_logs SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing AI processing logs")
public class AiLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @Schema(description = "User who submitted the original text")
    private User user;

    @Column(name = "original_text", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "Original text submitted by user")
    private String originalText;

    @Column(name = "generated_text", columnDefinition = "TEXT")
    @Schema(description = "AI-generated text")
    private String generatedText;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Schema(description = "Processing status", example = "SUCCESS")
    private AiProcessingStatus status = AiProcessingStatus.PENDING;

    @Column(name = "timestamp", nullable = false)
    @Schema(description = "Processing timestamp")
    private LocalDateTime timestamp = LocalDateTime.now();

    @Column(name = "remarks", length = 500)
    @Schema(description = "Additional remarks or error messages")
    private String remarks;

    @Column(name = "model_used", length = 50)
    @Schema(description = "AI model used for generation", example = "gpt-4")
    private String modelUsed;

    @Column(name = "processing_time_ms")
    @Schema(description = "Processing time in milliseconds")
    private Long processingTimeMs;

    @Column(name = "token_count")
    @Schema(description = "Number of tokens processed")
    private Integer tokenCount;

    @Column(name = "cost_usd")
    @Schema(description = "Cost in USD for the AI processing")
    private Double costUsd;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "news_id")
    @Schema(description = "Associated news article if created")
    private News news;

    /**
     * Marks the AI processing as successful.
     *
     * @param generatedText The generated text
     * @param processingTime Processing time in milliseconds
     */
    public void markAsSuccess(String generatedText, Long processingTime) {
        this.status = AiProcessingStatus.SUCCESS;
        this.generatedText = generatedText;
        this.processingTimeMs = processingTime;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * Marks the AI processing as failed.
     *
     * @param errorMessage Error message
     */
    public void markAsFailed(String errorMessage) {
        this.status = AiProcessingStatus.FAILURE;
        this.remarks = errorMessage;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * Enum for AI processing status.
     */
    public enum AiProcessingStatus {
        PENDING("Pending"),
        PROCESSING("Processing"),
        SUCCESS("Success"),
        FAILURE("Failure"),
        TIMEOUT("Timeout"),
        CANCELLED("Cancelled");

        private final String displayName;

        AiProcessingStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
