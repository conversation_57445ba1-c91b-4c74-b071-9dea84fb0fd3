package com.pressmeet247.ai_news.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a news tag.
 */
@Entity
@Table(
    name = "tags",
    indexes = {
        @Index(name = "idx_tag_name", columnList = "name", unique = true),
        @Index(name = "idx_tag_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE tags SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a news tag")
public class Tag extends BaseEntity {

    @Column(name = "name", length = 50, nullable = false, unique = true)
    @Schema(description = "Tag name", example = "AI")
    private String name;

    @Column(name = "description", length = 255)
    @Schema(description = "Tag description", example = "Artificial Intelligence related content")
    private String description;

    @Column(name = "color", length = 7)
    @Schema(description = "Tag color in hex format", example = "#007BFF")
    private String color;

    @Column(name = "usage_count", nullable = false)
    @Schema(description = "Number of times this tag has been used", example = "25")
    private int usageCount = 0;

    @Column(name = "is_trending", nullable = false)
    @Schema(description = "Whether this tag is trending", example = "false")
    private boolean isTrending = false;

    @ManyToMany(mappedBy = "tags", fetch = FetchType.LAZY)
    @Schema(description = "News articles with this tag")
    private List<News> newsArticles = new ArrayList<>();

    /**
     * Increments the usage count when tag is used.
     */
    public void incrementUsageCount() {
        this.usageCount++;
    }

    /**
     * Decrements the usage count when tag is removed.
     */
    public void decrementUsageCount() {
        if (this.usageCount > 0) {
            this.usageCount--;
        }
    }

    /**
     * Gets the actual count of news articles using this tag.
     *
     * @return Number of news articles
     */
    public int getActualNewsCount() {
        return newsArticles != null ? newsArticles.size() : 0;
    }
}
