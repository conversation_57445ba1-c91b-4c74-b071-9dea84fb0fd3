package com.pressmeet247.ai_news.service;

import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import com.pressmeet247.ai_news.repository.NewsRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for news operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NewsService {

    private final NewsRepository newsRepository;
    private final UserRepository userRepository;
    private final AIService aiService;

    /**
     * Generates and saves news.
     *
     * @param userId User ID
     * @param date Date of the event
     * @param place Place of the event
     * @param whatHappened Description of what happened
     * @param whereHappened Location details of the event
     * @param whoInvolved People or entities involved
     * @param whenHappened Time of the event
     * @param whoTold Source of the information
     * @param howHappened Explanation of how the event occurred
     * @param whyHappened Reason behind the event
     * @param newsType Type of news
     * @return Generated and saved news
     */
    @Transactional
    public News generateAndSaveNews(
            Long userId,
            String date,
            String place,
            String whatHappened,
            String whereHappened,
            String whoInvolved,
            String whenHappened,
            String whoTold,
            String howHappened,
            String whyHappened,
            String newsType) {
        
        // Find user
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        
        // Generate news content using AI service
        String generatedContent = aiService.generateNews(
                date,
                place,
                whatHappened,
                whereHappened,
                whoInvolved,
                whenHappened,
                whoTold,
                howHappened,
                whyHappened,
                newsType
        );
        
        // Create news entity
        News news = new News();
        news.setUser(user);
        news.setTitle(generateTitle(whatHappened, whereHappened));
        news.setContent(generatedContent);
        news.setDate(date);
        news.setPlace(place);
        news.setWhatHappened(whatHappened);
        news.setWhereHappened(whereHappened);
        news.setWhoInvolved(whoInvolved);
        news.setWhenHappened(whenHappened);
        news.setWhoTold(whoTold);
        news.setHowHappened(howHappened);
        news.setWhyHappened(whyHappened);
        news.setNewsType(newsType);
        news.setStatus(NewsStatus.PENDING);
        
        // Save news
        return newsRepository.save(news);
    }
    
    /**
     * Generates a title from what happened and where.
     *
     * @param whatHappened What happened
     * @param whereHappened Where it happened
     * @return Generated title
     */
    private String generateTitle(String whatHappened, String whereHappened) {
        String title = whatHappened;
        if (title.length() > 100) {
            title = title.substring(0, 97) + "...";
        }
        return title + " - " + whereHappened;
    }
    
    /**
     * Gets news by ID.
     *
     * @param id News ID
     * @return News
     */
    public News getNewsById(Long id) {
        return newsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("News not found with ID: " + id));
    }
    
    /**
     * Gets all news.
     *
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getAllNews(Pageable pageable) {
        return newsRepository.findAll(pageable);
    }
    
    /**
     * Gets news by user.
     *
     * @param userId User ID
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getNewsByUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        return newsRepository.findByUser(user, pageable);
    }
    
    /**
     * Gets news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getNewsByStatus(NewsStatus status, Pageable pageable) {
        return newsRepository.findByStatus(status, pageable);
    }
    
    /**
     * Gets news by user and status.
     *
     * @param userId User ID
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getNewsByUserAndStatus(Long userId, NewsStatus status, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        return newsRepository.findByUserAndStatus(user, status, pageable);
    }
    
    /**
     * Approves and publishes news.
     *
     * @param newsId News ID
     * @param approverId Approver ID
     * @return Published news
     */
    @Transactional
    public News approveAndPublishNews(Long newsId, Long approverId) {
        News news = getNewsById(newsId);
        User approver = userRepository.findById(approverId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + approverId));
        
        news.publish(approver);
        return newsRepository.save(news);
    }
    
    /**
     * Rejects news.
     *
     * @param newsId News ID
     * @param approverId Approver ID
     * @return Rejected news
     */
    @Transactional
    public News rejectNews(Long newsId, Long approverId) {
        News news = getNewsById(newsId);
        User approver = userRepository.findById(approverId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + approverId));
        
        news.reject(approver);
        return newsRepository.save(news);
    }
    
    /**
     * Updates news content.
     *
     * @param newsId News ID
     * @param content New content
     * @return Updated news
     */
    @Transactional
    public News updateNewsContent(Long newsId, String content) {
        News news = getNewsById(newsId);
        news.setContent(content);
        return newsRepository.save(news);
    }
    
    /**
     * Sets news as featured or not.
     *
     * @param newsId News ID
     * @param featured Featured flag
     * @return Updated news
     */
    @Transactional
    public News setNewsFeatured(Long newsId, boolean featured) {
        News news = getNewsById(newsId);
        news.setFeatured(featured);
        return newsRepository.save(news);
    }
    
    /**
     * Gets featured news.
     *
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getFeaturedNews(Pageable pageable) {
        return newsRepository.findByFeatured(true, pageable);
    }
    
    /**
     * Gets published featured news.
     *
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getPublishedFeaturedNews(Pageable pageable) {
        return newsRepository.findByFeaturedAndStatus(true, NewsStatus.PUBLISHED, pageable);
    }
    
    /**
     * Gets news by type.
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getNewsByType(String newsType, Pageable pageable) {
        return newsRepository.findByNewsType(newsType, pageable);
    }
    
    /**
     * Gets published news by type.
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    public Page<News> getPublishedNewsByType(String newsType, Pageable pageable) {
        return newsRepository.findByNewsTypeAndStatus(newsType, NewsStatus.PUBLISHED, pageable);
    }
}



