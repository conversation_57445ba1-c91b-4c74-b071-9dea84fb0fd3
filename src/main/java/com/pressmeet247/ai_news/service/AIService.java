package com.pressmeet247.ai_news.service;

/**
 * Service for AI operations.
 */
public interface AIService {
    
    /**
     * Generates news using AI.
     *
     * @param date Date of the event
     * @param place Place of the event
     * @param whatHappened Description of what happened
     * @param whereHappened Location details of the event
     * @param whoInvolved People or entities involved
     * @param whenHappened Time of the event
     * @param whoTold Source of the information
     * @param howHappened Explanation of how the event occurred
     * @param whyHappened Reason behind the event
     * @param newsType Type of news
     * @return Generated news content
     */
    String generateNews(
            String date,
            String place,
            String whatHappened,
            String whereHappened,
            String whoInvolved,
            String whenHappened,
            String whoTold,
            String howHappened,
            String whyHappened,
            String newsType
    );
}

