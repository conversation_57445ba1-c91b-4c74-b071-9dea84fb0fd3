package com.pressmeet247.ai_news.dto;

import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for news response.
 */
@Data
@Builder
@Schema(description = "News response")
public class NewsResponse {
    
    @Schema(description = "News ID", example = "1")
    private Long id;
    
    @Schema(description = "User ID who submitted the news", example = "1")
    private Long userId;
    
    @Schema(description = "User's name who submitted the news", example = "John <PERSON>")
    private String userName;
    
    @Schema(description = "News title", example = "Local Community Event")
    private String title;
    
    @Schema(description = "News content")
    private String content;
    
    @Schema(description = "Date of the event", example = "2023-04-15")
    private String date;
    
    @Schema(description = "Place of the event", example = "City Hall")
    private String place;
    
    @Schema(description = "Type of news", example = "Local")
    private String newsType;
    
    @Schema(description = "Status of the news article", example = "PENDING")
    private NewsStatus status;
    
    @Schema(description = "Whether the news is featured", example = "false")
    private boolean featured;
    
    @Schema(description = "Date and time when the news was created")
    private LocalDateTime createdAt;
    
    @Schema(description = "Date and time when the news was published")
    private LocalDateTime publishedAt;
    
    /**
     * Converts a News entity to NewsResponse DTO.
     *
     * @param news News entity
     * @return NewsResponse DTO
     */
    public static NewsResponse fromEntity(News news) {
        String userName = news.getUser().getFirstName() + " " + news.getUser().getLastName();
        
        return NewsResponse.builder()
                .id(news.getId())
                .userId(news.getUser().getId())
                .userName(userName)
                .title(news.getTitle())
                .content(news.getContent())
                .date(news.getDate())
                .place(news.getPlace())
                .newsType(news.getNewsType())
                .status(news.getStatus())
                .featured(news.isFeatured())
                .createdAt(news.getCreatedAt())
                .publishedAt(news.getPublishedAt())
                .build();
    }
}

