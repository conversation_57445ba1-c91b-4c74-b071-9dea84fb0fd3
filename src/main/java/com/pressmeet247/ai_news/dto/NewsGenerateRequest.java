package com.pressmeet247.ai_news.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * DTO for news generation request.
 */
@Data
@Schema(description = "News generation request")
public class NewsGenerateRequest {
    
    @NotBlank(message = "Date is required")
    @Schema(description = "Date of the event", example = "2023-04-15")
    private String date;
    
    @NotBlank(message = "Place is required")
    @Schema(description = "Place of the event", example = "City Hall")
    private String place;
    
    @NotBlank(message = "What happened is required")
    @Schema(description = "Description of what happened", example = "A community meeting was held to discuss the new park project")
    private String whatHappened;
    
    @NotBlank(message = "Where happened is required")
    @Schema(description = "Location details of the event", example = "Main Conference Room, City Hall")
    private String whereHappened;
    
    @NotBlank(message = "Who involved is required")
    @Schema(description = "People or entities involved", example = "Mayor <PERSON>, City Council members, and local residents")
    private String whoInvolved;
    
    @NotBlank(message = "When happened is required")
    @Schema(description = "Time of the event", example = "7:00 PM")
    private String whenHappened;
    
    @NotBlank(message = "Who told is required")
    @Schema(description = "Source of the information", example = "City Press Office")
    private String whoTold;
    
    @Schema(description = "Explanation of how the event occurred", example = "The meeting was conducted in a town hall format with presentations followed by Q&A")
    private String howHappened;
    
    @Schema(description = "Reason behind the event", example = "To gather community feedback on the proposed park design")
    private String whyHappened;
    
    @NotBlank(message = "News type is required")
    @Schema(description = "Type of news", example = "Local")
    private String newsType;
}

