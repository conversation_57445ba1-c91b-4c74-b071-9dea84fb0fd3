package com.pressmeet247.ai_news.impl;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pressmeet247.ai_news.service.AIService;
import com.pressmeet247.common.config.AzureConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of AIService.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIServiceImpl implements AIService {

    private final AzureConfig azureConfig;
    private final RestTemplate restTemplate;

    /**
     * Generates news using Azure OpenAI API based on the input fields.
     *
     * @param date Date of the event
     * @param place Place where the event occurred
     * @param whatHappened Description of what happened
     * @param whereHappened Location details of the event
     * @param whoInvolved People or entities involved
     * @param whenHappened Time of the event
     * @param whoTold Source of the information
     * @param howHappened Explanation of how the event occurred
     * @param whyHappened Reason behind the event
     * @param newsType Type of news (e.g., political, sports, etc.)
     * @return Generated news article as a string
     */
    @Override
    public String generateNews(
            String date,
            String place,
            String whatHappened,
            String whereHappened,
            String whoInvolved,
            String whenHappened,
            String whoTold,
            String howHappened,
            String whyHappened,
            String newsType) {

        // Fetches API URL and key from configuration
        String apiUrl = azureConfig.getApiUrl();
        String apiKey = azureConfig.getApiKey();

        // Constructs a detailed prompt for generating news
        String prompt = "Generate a very very professional news article as an expert news writer and professional journalist in Marathi language, formatted like a newspaper article " + newsType + " news article about the topic: " + ".\n" +
                "Include the following details:\n" +
                "- Date: " + date + "\n" +
                "- Place: " + place + "\n" +
                "- What happened: " + whatHappened + "\n" +
                "- Where it happened: " + whereHappened + "\n" +
                "- Who was involved: " + whoInvolved + "\n" +
                "- When it happened: " + whenHappened + "\n" +
                "- Who reported it: " + whoTold + "\n" +
                "- How it happened: " + howHappened + "\n" +
                "- Why it happened: " + whyHappened + "\n" +
                "Ensure the article is informative, accurate, and engaging.";

        // Builds the request body for Azure OpenAI API
        Map<String, Object> body = new HashMap<>();
        body.put("messages", new Object[]{
                Map.of("role", "system", "content", "You are a helpful news generator."),
                Map.of("role", "user", "content", prompt)
        });
        body.put("temperature", 1.0);
        body.put("top_p", 1.0);
        body.put("max_tokens", 1000);
        body.put("model", "gpt-4o");

        // Sets up the headers for the HTTP request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        // Creates the HTTP request entity
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);

        try {
            // Sends the request to Azure OpenAI API and handles the response
            ResponseEntity<String> response = restTemplate.exchange(apiUrl, HttpMethod.POST, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                try {
                    // Parses the response using Jackson ObjectMapper
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode root = mapper.readTree(response.getBody());
                    // Extracts the generated news content
                    return root.get("choices").get(0).get("message").get("content").asText();
                } catch (Exception e) {
                    log.error("Error parsing AI response: {}", e.getMessage(), e);
                }
            } else {
                log.error("AI service returned non-success status: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error calling AI service: {}", e.getMessage(), e);
        }
        
        return "Failed to generate news.";
    }
}

