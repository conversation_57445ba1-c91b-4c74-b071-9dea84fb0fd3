package com.pressmeet247.ai_news.repository;

import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import org.springframework.data.jpa.repository.Query;
import java.util.List;

/**
 * Repository for News entity operations.
 */
@Repository
public interface NewsRepository extends JpaRepository<News, Long> {

    /**
     * Find news by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByUser(User user, Pageable pageable);

    /**
     * Find news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByStatus(NewsStatus status, Pageable pageable);

    /**
     * Find news by user and status.
     *
     * @param user User
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByUserAndStatus(User user, NewsStatus status, Pageable pageable);

    /**
     * Find featured news.
     *
     * @param featured Featured flag
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByFeatured(boolean featured, Pageable pageable);

    /**
     * Find published featured news.
     *
     * @param featured Featured flag
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByFeaturedAndStatus(boolean featured, NewsStatus status, Pageable pageable);

    /**
     * Find news by news type.
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByNewsType(String newsType, Pageable pageable);

    /**
     * Find published news by news type.
     *
     * @param newsType News type
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByNewsTypeAndStatus(String newsType, NewsStatus status, Pageable pageable);

    /**
     * Count news by status.
     *
     * @param status News status
     * @return Count of news with the specified status
     */
    long countByStatus(NewsStatus status);

    /**
     * Find all distinct news types.
     *
     * @return List of distinct news types
     */
    @Query("SELECT DISTINCT n.newsType FROM News n WHERE n.newsType IS NOT NULL")
    List<String> findDistinctNewsTypes();

    /**
     * Find news by news type (case insensitive).
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    @Query("SELECT n FROM News n WHERE LOWER(n.newsType) = LOWER(:newsType)")
    Page<News> findByNewsTypeIgnoreCase(String newsType, Pageable pageable);
}



