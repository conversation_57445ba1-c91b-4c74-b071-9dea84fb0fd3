package com.pressmeet247.ai_news.repository;

import com.pressmeet247.ai_news.entity.Category;
import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.ai_news.entity.Tag;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for News entity operations.
 */
@Repository
public interface NewsRepository extends JpaRepository<News, Long> {

    /**
     * Find news by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByUser(User user, Pageable pageable);

    /**
     * Find news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByStatus(NewsStatus status, Pageable pageable);

    /**
     * Find news by user and status.
     *
     * @param user User
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByUserAndStatus(User user, NewsStatus status, Pageable pageable);

    /**
     * Find featured news.
     *
     * @param featured Featured flag
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByFeatured(boolean featured, Pageable pageable);

    /**
     * Find published featured news.
     *
     * @param featured Featured flag
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByFeaturedAndStatus(boolean featured, NewsStatus status, Pageable pageable);

    /**
     * Find news by news type.
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByNewsType(String newsType, Pageable pageable);

    /**
     * Find published news by news type.
     *
     * @param newsType News type
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByNewsTypeAndStatus(String newsType, NewsStatus status, Pageable pageable);

    /**
     * Count news by status.
     *
     * @param status News status
     * @return Count of news with the specified status
     */
    long countByStatus(NewsStatus status);

    /**
     * Find all distinct news types.
     *
     * @return List of distinct news types
     */
    @Query("SELECT DISTINCT n.newsType FROM News n WHERE n.newsType IS NOT NULL")
    List<String> findDistinctNewsTypes();

    /**
     * Find news by news type (case insensitive).
     *
     * @param newsType News type
     * @param pageable Pageable
     * @return Page of News
     */
    @Query("SELECT n FROM News n WHERE LOWER(n.newsType) = LOWER(:newsType)")
    Page<News> findByNewsTypeIgnoreCase(String newsType, Pageable pageable);

    /**
     * Find news by category.
     *
     * @param category Category
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByCategory(Category category, Pageable pageable);

    /**
     * Find news by category and status.
     *
     * @param category Category
     * @param status News status
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByCategoryAndStatus(Category category, NewsStatus status, Pageable pageable);

    /**
     * Find news by tags containing.
     *
     * @param tag Tag
     * @param pageable Pageable
     * @return Page of News
     */
    @Query("SELECT n FROM News n JOIN n.tags t WHERE t = :tag")
    Page<News> findByTagsContaining(@Param("tag") Tag tag, Pageable pageable);

    /**
     * Find news by multiple statuses.
     *
     * @param statuses List of statuses
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByStatusIn(List<NewsStatus> statuses, Pageable pageable);

    /**
     * Find news by date range.
     *
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find news by status and date range.
     *
     * @param status News status
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pageable
     * @return Page of News
     */
    Page<News> findByStatusAndCreatedAtBetween(NewsStatus status, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find news by title containing (case insensitive).
     *
     * @param title Title to search for
     * @param pageable Pageable
     * @return Page of News
     */
    @Query("SELECT n FROM News n WHERE LOWER(n.title) LIKE LOWER(CONCAT('%', :title, '%'))")
    Page<News> findByTitleContainingIgnoreCase(@Param("title") String title, Pageable pageable);

    /**
     * Count news by category.
     *
     * @param category Category
     * @return Count of news in the category
     */
    long countByCategory(Category category);

    /**
     * Count news by user.
     *
     * @param user User
     * @return Count of news by the user
     */
    long countByUser(User user);

    /**
     * Find news statistics by category.
     *
     * @return List of category statistics
     */
    @Query("SELECT c.name, COUNT(n) FROM News n RIGHT JOIN n.category c GROUP BY c.id, c.name ORDER BY COUNT(n) DESC")
    List<Object[]> findNewsStatsByCategory();

    /**
     * Find news statistics by status.
     *
     * @return List of status statistics
     */
    @Query("SELECT n.status, COUNT(n) FROM News n GROUP BY n.status")
    List<Object[]> findNewsStatsByStatus();

    /**
     * Find news statistics by date range.
     *
     * @param startDate Start date
     * @param endDate End date
     * @return List of date-based statistics
     */
    @Query("SELECT DATE(n.createdAt), COUNT(n) FROM News n WHERE n.createdAt BETWEEN :startDate AND :endDate GROUP BY DATE(n.createdAt) ORDER BY DATE(n.createdAt)")
    List<Object[]> findNewsStatsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find recently approved news.
     *
     * @param days Number of days to look back
     * @param pageable Pageable
     * @return Page of recently approved news
     */
    @Query("SELECT n FROM News n WHERE n.status IN ('APPROVED', 'PUBLISHED') AND n.approvedAt >= :cutoffDate ORDER BY n.approvedAt DESC")
    Page<News> findRecentlyApprovedNews(@Param("cutoffDate") LocalDateTime cutoffDate, Pageable pageable);

    /**
     * Find pending news older than specified days.
     *
     * @param cutoffDate Cutoff date
     * @param pageable Pageable
     * @return Page of old pending news
     */
    @Query("SELECT n FROM News n WHERE n.status = 'PENDING' AND n.createdAt < :cutoffDate ORDER BY n.createdAt ASC")
    Page<News> findOldPendingNews(@Param("cutoffDate") LocalDateTime cutoffDate, Pageable pageable);
}



