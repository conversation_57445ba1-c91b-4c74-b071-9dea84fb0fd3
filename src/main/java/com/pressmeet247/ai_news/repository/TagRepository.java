package com.pressmeet247.ai_news.repository;

import com.pressmeet247.ai_news.entity.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for Tag entity operations.
 */
@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {

    /**
     * Find tag by name.
     *
     * @param name Tag name
     * @return Optional containing the tag if found
     */
    Optional<Tag> findByName(String name);

    /**
     * Check if tag exists by name.
     *
     * @param name Tag name
     * @return true if exists, false otherwise
     */
    boolean existsByName(String name);

    /**
     * Find tags by name containing (case insensitive).
     *
     * @param name Name to search for
     * @param pageable Pagination parameters
     * @return Page of matching tags
     */
    @Query("SELECT t FROM Tag t WHERE LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Page<Tag> findByNameContainingIgnoreCase(@Param("name") String name, Pageable pageable);

    /**
     * Find trending tags.
     *
     * @return List of trending tags
     */
    List<Tag> findByIsTrendingTrue();

    /**
     * Find trending tags with pagination.
     *
     * @param pageable Pagination parameters
     * @return Page of trending tags
     */
    Page<Tag> findByIsTrendingTrue(Pageable pageable);

    /**
     * Find tags ordered by usage count descending.
     *
     * @param pageable Pagination parameters
     * @return Page of tags ordered by usage count
     */
    Page<Tag> findAllByOrderByUsageCountDesc(Pageable pageable);

    /**
     * Find most popular tags.
     *
     * @param limit Number of tags to return
     * @return List of most popular tags
     */
    @Query("SELECT t FROM Tag t ORDER BY t.usageCount DESC")
    List<Tag> findMostPopularTags(Pageable pageable);

    /**
     * Find tags with usage count greater than specified value.
     *
     * @param minUsageCount Minimum usage count
     * @param pageable Pagination parameters
     * @return Page of tags
     */
    Page<Tag> findByUsageCountGreaterThan(int minUsageCount, Pageable pageable);

    /**
     * Find tags by multiple names.
     *
     * @param names List of tag names
     * @return List of tags
     */
    List<Tag> findByNameIn(List<String> names);

    /**
     * Count tags by trending status.
     *
     * @param isTrending Trending status
     * @return Count of tags
     */
    long countByIsTrending(boolean isTrending);

    /**
     * Find unused tags (usage count = 0).
     *
     * @param pageable Pagination parameters
     * @return Page of unused tags
     */
    Page<Tag> findByUsageCount(int usageCount, Pageable pageable);
}
