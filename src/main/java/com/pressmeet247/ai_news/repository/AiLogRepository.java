package com.pressmeet247.ai_news.repository;

import com.pressmeet247.ai_news.entity.AiLog;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for AiLog entity operations.
 */
@Repository
public interface AiLogRepository extends JpaRepository<AiLog, Long> {

    /**
     * Find AI logs by user.
     *
     * @param user User
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByUser(User user, Pageable pageable);

    /**
     * Find AI logs by user ID.
     *
     * @param userId User ID
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByUserId(Long userId, Pageable pageable);

    /**
     * Find AI logs by status.
     *
     * @param status Processing status
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByStatus(AiLog.AiProcessingStatus status, Pageable pageable);

    /**
     * Find AI logs by timestamp range.
     *
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByTimestampBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find AI logs by user and status.
     *
     * @param userId User ID
     * @param status Processing status
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByUserIdAndStatus(Long userId, AiLog.AiProcessingStatus status, Pageable pageable);

    /**
     * Find AI logs by user and timestamp range.
     *
     * @param userId User ID
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByUserIdAndTimestampBetween(Long userId, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find AI logs by status and timestamp range.
     *
     * @param status Processing status
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByStatusAndTimestampBetween(AiLog.AiProcessingStatus status, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Count AI logs by status.
     *
     * @param status Processing status
     * @return Count of AI logs
     */
    long countByStatus(AiLog.AiProcessingStatus status);

    /**
     * Count AI logs by user.
     *
     * @param user User
     * @return Count of AI logs
     */
    long countByUser(User user);

    /**
     * Count AI logs by user and status.
     *
     * @param userId User ID
     * @param status Processing status
     * @return Count of AI logs
     */
    long countByUserIdAndStatus(Long userId, AiLog.AiProcessingStatus status);

    /**
     * Find recent AI logs.
     *
     * @param pageable Pagination parameters
     * @return Page of recent AI logs
     */
    @Query("SELECT a FROM AiLog a ORDER BY a.timestamp DESC")
    Page<AiLog> findRecentLogs(Pageable pageable);

    /**
     * Find AI logs with processing time statistics.
     *
     * @return List of AI logs with processing time data
     */
    @Query("SELECT a FROM AiLog a WHERE a.processingTimeMs IS NOT NULL ORDER BY a.processingTimeMs DESC")
    List<AiLog> findLogsWithProcessingTime();

    /**
     * Calculate average processing time.
     *
     * @return Average processing time in milliseconds
     */
    @Query("SELECT AVG(a.processingTimeMs) FROM AiLog a WHERE a.processingTimeMs IS NOT NULL")
    Double calculateAverageProcessingTime();

    /**
     * Calculate total cost.
     *
     * @return Total cost in USD
     */
    @Query("SELECT SUM(a.costUsd) FROM AiLog a WHERE a.costUsd IS NOT NULL")
    Double calculateTotalCost();

    /**
     * Find logs by model used.
     *
     * @param modelUsed Model name
     * @param pageable Pagination parameters
     * @return Page of AI logs
     */
    Page<AiLog> findByModelUsed(String modelUsed, Pageable pageable);

    /**
     * Count logs by model used.
     *
     * @param modelUsed Model name
     * @return Count of logs
     */
    long countByModelUsed(String modelUsed);
}
