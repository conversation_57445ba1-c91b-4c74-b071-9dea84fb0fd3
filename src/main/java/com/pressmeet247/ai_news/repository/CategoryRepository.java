package com.pressmeet247.ai_news.repository;

import com.pressmeet247.ai_news.entity.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for Category entity operations.
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    /**
     * Find category by name.
     *
     * @param name Category name
     * @return Optional containing the category if found
     */
    Optional<Category> findByName(String name);

    /**
     * Check if category exists by name.
     *
     * @param name Category name
     * @return true if exists, false otherwise
     */
    boolean existsByName(String name);

    /**
     * Find all active categories.
     *
     * @return List of active categories
     */
    List<Category> findByIsActiveTrue();

    /**
     * Find all active categories with pagination.
     *
     * @param pageable Pagination parameters
     * @return Page of active categories
     */
    Page<Category> findByIsActiveTrue(Pageable pageable);

    /**
     * Find categories by active status.
     *
     * @param isActive Active status
     * @param pageable Pagination parameters
     * @return Page of categories
     */
    Page<Category> findByIsActive(boolean isActive, Pageable pageable);

    /**
     * Find categories ordered by sort order.
     *
     * @return List of categories ordered by sort order
     */
    List<Category> findAllByOrderBySortOrderAsc();

    /**
     * Find categories with news count.
     *
     * @return List of categories with their news count
     */
    @Query("SELECT c FROM Category c LEFT JOIN c.newsArticles n GROUP BY c ORDER BY COUNT(n) DESC")
    List<Category> findCategoriesWithNewsCount();

    /**
     * Find categories by name containing (case insensitive).
     *
     * @param name Name to search for
     * @param pageable Pagination parameters
     * @return Page of matching categories
     */
    @Query("SELECT c FROM Category c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Page<Category> findByNameContainingIgnoreCase(@Param("name") String name, Pageable pageable);

    /**
     * Count categories by active status.
     *
     * @param isActive Active status
     * @return Count of categories
     */
    long countByIsActive(boolean isActive);

    /**
     * Find the maximum sort order.
     *
     * @return Maximum sort order
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM Category c")
    int findMaxSortOrder();
}
