package com.pressmeet247.ai_news.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.ai_news.dto.NewsGenerateRequest;
import com.pressmeet247.ai_news.dto.NewsResponse;
import com.pressmeet247.ai_news.entity.News;
import com.pressmeet247.ai_news.entity.NewsStatus;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.ai_news.service.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for news operations.
 */
@RestController
@RequestMapping("/api/news")
@RequiredArgsConstructor
@Tag(name = "News", description = "News API")
public class NewsController {

    private final NewsService newsService;
    private final UserRepository userRepository;

    /**
     * Generates news.
     *
     * @param request News generation request
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/generate")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Generate news", description = "Generates news using AI (requires authentication)")
    public ResponseEntity<ApiResponse<NewsResponse>> generateNews(
            @RequestBody NewsGenerateRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        // Get user by email (username)
        String email = userDetails.getUsername();
        User user = userRepository.findByEmail(email);

        if (user == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("User not found with email: " + email));
        }

        Long userId = user.getId();

        News news = newsService.generateAndSaveNews(
                userId,
                request.getDate(),
                request.getPlace(),
                request.getWhatHappened(),
                request.getWhereHappened(),
                request.getWhoInvolved(),
                request.getWhenHappened(),
                request.getWhoTold(),
                request.getHowHappened(),
                request.getWhyHappened(),
                request.getNewsType()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "News generated successfully",
                NewsResponse.fromEntity(news)
        ));
    }

    /**
     * Gets news by ID.
     *
     * @param id News ID
     * @return Response entity
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get news by ID", description = "Returns a news article by ID")
    public ResponseEntity<ApiResponse<NewsResponse>> getNewsById(@PathVariable Long id) {
        try {
            News news = newsService.getNewsById(id);
            return ResponseEntity.ok(ApiResponse.success(
                    "News retrieved successfully",
                    NewsResponse.fromEntity(news)
            ));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Gets all news.
     *
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all news", description = "Returns all news articles (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getAllNews(Pageable pageable) {
        Page<News> newsPage = newsService.getAllNews(pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "News retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets news by user.
     *
     * @param userDetails Authenticated user details
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/my-news")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get my news", description = "Returns news articles submitted by the authenticated user")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getMyNews(
            @AuthenticationPrincipal UserDetails userDetails,
            Pageable pageable) {

        // Get user by email (username)
        String email = userDetails.getUsername();
        User user = userRepository.findByEmail(email);

        if (user == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("User not found with email: " + email));
        }

        Long userId = user.getId();
        Page<News> newsPage = newsService.getNewsByUser(userId, pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "News retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets news by status.
     *
     * @param status News status
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get news by status", description = "Returns news articles by status (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByStatus(
            @PathVariable NewsStatus status,
            Pageable pageable) {

        Page<News> newsPage = newsService.getNewsByStatus(status, pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "News retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets published news.
     *
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/published")
    @Operation(summary = "Get published news", description = "Returns published news articles")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getPublishedNews(Pageable pageable) {
        Page<News> newsPage = newsService.getNewsByStatus(NewsStatus.PUBLISHED, pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "News retrieved successfully",
                responsePage
        ));
    }

    /**
     * Approves and publishes news.
     *
     * @param id News ID
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/{id}/approve")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Approve news", description = "Approves and publishes a news article (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> approveNews(
            @PathVariable Long id,
            @AuthenticationPrincipal UserDetails userDetails) {

        // Get user by email (username)
        String email = userDetails.getUsername();
        User user = userRepository.findByEmail(email);

        if (user == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("User not found with email: " + email));
        }

        Long userId = user.getId();
        News news = newsService.approveAndPublishNews(id, userId);

        return ResponseEntity.ok(ApiResponse.success(
                "News approved and published successfully",
                NewsResponse.fromEntity(news)
        ));
    }

    /**
     * Rejects news.
     *
     * @param id News ID
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/{id}/reject")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Reject news", description = "Rejects a news article (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> rejectNews(
            @PathVariable Long id,
            @AuthenticationPrincipal UserDetails userDetails) {

        // Get user by email (username)
        String email = userDetails.getUsername();
        User user = userRepository.findByEmail(email);

        if (user == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("User not found with email: " + email));
        }

        Long userId = user.getId();
        News news = newsService.rejectNews(id, userId);

        return ResponseEntity.ok(ApiResponse.success(
                "News rejected successfully",
                NewsResponse.fromEntity(news)
        ));
    }

    /**
     * Updates news content.
     *
     * @param id News ID
     * @param content News content
     * @return Response entity
     */
    @PutMapping("/{id}/content")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Update news content", description = "Updates the content of a news article (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> updateNewsContent(
            @PathVariable Long id,
            @RequestBody String content) {

        News news = newsService.updateNewsContent(id, content);

        return ResponseEntity.ok(ApiResponse.success(
                "News content updated successfully",
                NewsResponse.fromEntity(news)
        ));
    }

    /**
     * Sets news as featured.
     *
     * @param id News ID
     * @param featured Featured flag
     * @return Response entity
     */
    @PutMapping("/{id}/featured")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Set news as featured", description = "Sets a news article as featured or not (requires ADMIN role)")
    public ResponseEntity<ApiResponse<NewsResponse>> setNewsFeatured(
            @PathVariable Long id,
            @RequestParam boolean featured) {

        News news = newsService.setNewsFeatured(id, featured);

        return ResponseEntity.ok(ApiResponse.success(
                featured ? "News set as featured successfully" : "News removed from featured successfully",
                NewsResponse.fromEntity(news)
        ));
    }

    /**
     * Gets featured news.
     *
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/featured")
    @Operation(summary = "Get featured news", description = "Returns featured news articles")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getFeaturedNews(Pageable pageable) {
        Page<News> newsPage = newsService.getPublishedFeaturedNews(pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Featured news retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets news by type.
     *
     * @param type News type
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/type/{type}")
    @Operation(summary = "Get news by type", description = "Returns news articles by type")
    public ResponseEntity<ApiResponse<Page<NewsResponse>>> getNewsByType(
            @PathVariable String type,
            Pageable pageable) {

        Page<News> newsPage = newsService.getPublishedNewsByType(type, pageable);
        Page<NewsResponse> responsePage = newsPage.map(NewsResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "News retrieved successfully",
                responsePage
        ));
    }
}



