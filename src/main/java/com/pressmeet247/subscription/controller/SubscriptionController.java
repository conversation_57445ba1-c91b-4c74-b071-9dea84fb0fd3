package com.pressmeet247.subscription.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.subscription.dto.*;
import com.pressmeet247.subscription.entity.Subscription;
import com.pressmeet247.subscription.entity.SubscriptionPlan;
import com.pressmeet247.subscription.service.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Controller for subscription operations.
 */
@RestController
@RequestMapping("/api/subscriptions")
@RequiredArgsConstructor
@Tag(name = "Subscriptions", description = "Subscriptions API")
public class SubscriptionController {

    private final SubscriptionService subscriptionService;

    /**
     * Creates a subscription plan.
     *
     * @param request Subscription plan request
     * @return Response entity
     */
    @PostMapping("/plans")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create subscription plan", description = "Creates a subscription plan (requires ADMIN role)")
    public ResponseEntity<ApiResponse<SubscriptionPlanResponse>> createSubscriptionPlan(
            @RequestBody SubscriptionPlanRequest request) {

        SubscriptionPlan plan = subscriptionService.createSubscriptionPlan(
                request.getName(),
                request.getDescription(),
                request.getPrice(),
                request.getCurrency(),
                request.getDurationMonths(),
                request.getFeatures()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription plan created successfully",
                SubscriptionPlanResponse.fromEntity(plan)
        ));
    }

    /**
     * Gets all subscription plans.
     *
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/plans")
    @Operation(summary = "Get all subscription plans", description = "Returns all subscription plans")
    public ResponseEntity<ApiResponse<Page<SubscriptionPlanResponse>>> getAllSubscriptionPlans(Pageable pageable) {
        Page<SubscriptionPlan> planPage = subscriptionService.getAllSubscriptionPlans(pageable);
        Page<SubscriptionPlanResponse> responsePage = planPage.map(SubscriptionPlanResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription plans retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets active subscription plans.
     *
     * @return Response entity
     */
    @GetMapping("/plans/active")
    @Operation(summary = "Get active subscription plans", description = "Returns active subscription plans")
    public ResponseEntity<ApiResponse<List<SubscriptionPlanResponse>>> getActiveSubscriptionPlans() {
        List<SubscriptionPlan> plans = subscriptionService.getActiveSubscriptionPlans();
        List<SubscriptionPlanResponse> responseList = plans.stream()
                .map(SubscriptionPlanResponse::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(
                "Active subscription plans retrieved successfully",
                responseList
        ));
    }

    /**
     * Gets a subscription plan by ID.
     *
     * @param id Plan ID
     * @return Response entity
     */
    @GetMapping("/plans/{id}")
    @Operation(summary = "Get subscription plan", description = "Returns a subscription plan by ID")
    public ResponseEntity<ApiResponse<SubscriptionPlanResponse>> getSubscriptionPlan(@PathVariable Long id) {
        SubscriptionPlan plan = subscriptionService.getSubscriptionPlanById(id);

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription plan retrieved successfully",
                SubscriptionPlanResponse.fromEntity(plan)
        ));
    }

    /**
     * Updates a subscription plan.
     *
     * @param id Plan ID
     * @param request Subscription plan update request
     * @return Response entity
     */
    @PutMapping("/plans/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Update subscription plan", description = "Updates a subscription plan (requires ADMIN role)")
    public ResponseEntity<ApiResponse<SubscriptionPlanResponse>> updateSubscriptionPlan(
            @PathVariable Long id,
            @RequestBody SubscriptionPlanUpdateRequest request) {

        SubscriptionPlan plan = subscriptionService.updateSubscriptionPlan(
                id,
                request.getName(),
                request.getDescription(),
                request.getPrice(),
                request.getCurrency(),
                request.getDurationMonths(),
                request.getFeatures(),
                request.isActive()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription plan updated successfully",
                SubscriptionPlanResponse.fromEntity(plan)
        ));
    }

    /**
     * Creates a subscription.
     *
     * @param request Subscription request
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Create subscription", description = "Creates a subscription for the authenticated user")
    public ResponseEntity<ApiResponse<SubscriptionResponse>> createSubscription(
            @RequestBody SubscriptionRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();

        Subscription subscription = subscriptionService.createSubscriptionByEmail(
                email,
                request.getPlanId(),
                request.getPaymentMethod(),
                request.getPaymentReference()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription created successfully",
                SubscriptionResponse.fromEntity(subscription)
        ));
    }

    /**
     * Gets the active subscription for the authenticated user.
     *
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @GetMapping("/active")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get active subscription", description = "Returns the active subscription for the authenticated user")
    public ResponseEntity<ApiResponse<SubscriptionResponse>> getActiveSubscription(
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();
        Optional<Subscription> subscription = subscriptionService.getActiveSubscriptionForUserByEmail(email);

        if (subscription.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success(
                    "Active subscription retrieved successfully",
                    SubscriptionResponse.fromEntity(subscription.get())
            ));
        } else {
            return ResponseEntity.ok(ApiResponse.success(
                    "No active subscription found",
                    null
            ));
        }
    }

    /**
     * Gets all subscriptions for the authenticated user.
     *
     * @param userDetails Authenticated user details
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/my-subscriptions")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get my subscriptions", description = "Returns all subscriptions for the authenticated user")
    public ResponseEntity<ApiResponse<Page<SubscriptionResponse>>> getMySubscriptions(
            @AuthenticationPrincipal UserDetails userDetails,
            Pageable pageable) {

        String email = userDetails.getUsername();
        Page<Subscription> subscriptionPage = subscriptionService.getSubscriptionsForUserByEmail(email, pageable);
        Page<SubscriptionResponse> responsePage = subscriptionPage.map(SubscriptionResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Subscriptions retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets subscription history for the authenticated user.
     * This is an alias for /my-subscriptions for compatibility.
     *
     * @param userDetails Authenticated user details
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/history")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get subscription history", description = "Returns subscription history for the authenticated user")
    public ResponseEntity<ApiResponse<Page<SubscriptionResponse>>> getSubscriptionHistory(
            @AuthenticationPrincipal UserDetails userDetails,
            Pageable pageable) {

        String email = userDetails.getUsername();
        Page<Subscription> subscriptionPage = subscriptionService.getSubscriptionsForUserByEmail(email, pageable);
        Page<SubscriptionResponse> responsePage = subscriptionPage.map(SubscriptionResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription history retrieved successfully",
                responsePage
        ));
    }

    /**
     * Cancels a subscription.
     *
     * @param id Subscription ID
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/{id}/cancel")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Cancel subscription", description = "Cancels a subscription")
    public ResponseEntity<ApiResponse<SubscriptionResponse>> cancelSubscription(
            @PathVariable Long id,
            @AuthenticationPrincipal UserDetails userDetails) {

        Subscription subscription = subscriptionService.cancelSubscription(id);

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription cancelled successfully",
                SubscriptionResponse.fromEntity(subscription)
        ));
    }

    /**
     * Renews a subscription.
     *
     * @param id Subscription ID
     * @param request Subscription renewal request
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/{id}/renew")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Renew subscription", description = "Renews a subscription")
    public ResponseEntity<ApiResponse<SubscriptionResponse>> renewSubscription(
            @PathVariable Long id,
            @RequestBody SubscriptionRenewalRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        Subscription subscription = subscriptionService.renewSubscription(
                id,
                request.getPaymentMethod(),
                request.getPaymentReference()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "Subscription renewed successfully",
                SubscriptionResponse.fromEntity(subscription)
        ));
    }

    /**
     * Checks if the authenticated user has an active subscription.
     *
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @GetMapping("/check-active")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Check active subscription", description = "Checks if the authenticated user has an active subscription")
    public ResponseEntity<ApiResponse<Boolean>> checkActiveSubscription(
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();
        boolean hasActiveSubscription = subscriptionService.hasActiveSubscriptionByEmail(email);

        return ResponseEntity.ok(ApiResponse.success(
                "Active subscription check completed",
                hasActiveSubscription
        ));
    }

    /**
     * Gets subscriptions expiring soon.
     *
     * @param days Days until expiry
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/expiring")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get expiring subscriptions", description = "Returns subscriptions expiring within the specified number of days (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<SubscriptionResponse>>> getExpiringSubscriptions(
            @RequestParam(defaultValue = "7") int days,
            Pageable pageable) {

        Page<Subscription> subscriptionPage = subscriptionService.getSubscriptionsExpiringSoon(days, pageable);
        Page<SubscriptionResponse> responsePage = subscriptionPage.map(SubscriptionResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Expiring subscriptions retrieved successfully",
                responsePage
        ));
    }

    /**
     * Notifies users about expiring subscriptions.
     *
     * @param days Days until expiry
     * @return Response entity
     */
    @PostMapping("/notify-expiring")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Notify expiring subscriptions", description = "Notifies users about subscriptions expiring within the specified number of days (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Void>> notifyExpiringSubscriptions(
            @RequestParam(defaultValue = "7") int days) {

        subscriptionService.notifyExpiringSubscriptions(days);

        return ResponseEntity.ok(ApiResponse.success(
                "Expiring subscription notifications sent successfully",
                null
        ));
    }
}
