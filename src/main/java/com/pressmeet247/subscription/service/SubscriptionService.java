package com.pressmeet247.subscription.service;


import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.exception.ResourceNotFoundException;

import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.EmailService;
import com.pressmeet247.subscription.entity.Subscription;
import com.pressmeet247.subscription.entity.SubscriptionPlan;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import com.pressmeet247.subscription.repository.SubscriptionPlanRepository;
import com.pressmeet247.subscription.repository.SubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for subscription operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SubscriptionService {

    private final SubscriptionRepository subscriptionRepository;
    private final SubscriptionPlanRepository subscriptionPlanRepository;
    private final UserRepository userRepository;
    private final EmailService emailService;

    /**
     * Creates a subscription plan.
     *
     * @param name Plan name
     * @param description Plan description
     * @param price Plan price
     * @param currency Currency code
     * @param durationMonths Duration in months
     * @param features Plan features as JSON
     * @return Created subscription plan
     */
    @Transactional
    public SubscriptionPlan createSubscriptionPlan(
            String name,
            String description,
            java.math.BigDecimal price,
            String currency,
            int durationMonths,
            String features) {

        if (subscriptionPlanRepository.existsByName(name)) {
            throw new IllegalArgumentException("Subscription plan with name '" + name + "' already exists");
        }

        SubscriptionPlan plan = new SubscriptionPlan();
        plan.setName(name);
        plan.setDescription(description);
        plan.setPrice(price);
        plan.setCurrency(currency);
        plan.setDurationMonths(durationMonths);
        plan.setFeatures(features);
        plan.setActive(true);

        return subscriptionPlanRepository.save(plan);
    }

    /**
     * Gets a subscription plan by ID.
     *
     * @param id Plan ID
     * @return Subscription plan
     */
    public SubscriptionPlan getSubscriptionPlanById(Long id) {
        return subscriptionPlanRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Subscription plan not found with ID: " + id));
    }

    /**
     * Gets all subscription plans.
     *
     * @param pageable Pageable
     * @return Page of subscription plans
     */
    public Page<SubscriptionPlan> getAllSubscriptionPlans(Pageable pageable) {
        return subscriptionPlanRepository.findAll(pageable);
    }

    /**
     * Gets active subscription plans.
     *
     * @return List of active subscription plans
     */
    public List<SubscriptionPlan> getActiveSubscriptionPlans() {
        return subscriptionPlanRepository.findByActive(true);
    }

    /**
     * Updates a subscription plan.
     *
     * @param id Plan ID
     * @param name Plan name
     * @param description Plan description
     * @param price Plan price
     * @param currency Currency code
     * @param durationMonths Duration in months
     * @param features Plan features as JSON
     * @param active Active flag
     * @return Updated subscription plan
     */
    @Transactional
    public SubscriptionPlan updateSubscriptionPlan(
            Long id,
            String name,
            String description,
            java.math.BigDecimal price,
            String currency,
            int durationMonths,
            String features,
            boolean active) {

        SubscriptionPlan plan = getSubscriptionPlanById(id);

        if (name != null && !name.equals(plan.getName())) {
            if (subscriptionPlanRepository.existsByName(name)) {
                throw new IllegalArgumentException("Subscription plan with name '" + name + "' already exists");
            }
            plan.setName(name);
        }

        if (description != null) {
            plan.setDescription(description);
        }

        if (price != null) {
            plan.setPrice(price);
        }

        if (currency != null) {
            plan.setCurrency(currency);
        }

        if (durationMonths > 0) {
            plan.setDurationMonths(durationMonths);
        }

        if (features != null) {
            plan.setFeatures(features);
        }

        plan.setActive(active);

        return subscriptionPlanRepository.save(plan);
    }

    /**
     * Creates a subscription.
     *
     * @param userId User ID
     * @param planId Plan ID
     * @param paymentMethod Payment method
     * @param paymentReference Payment reference
     * @return Created subscription
     */
    @Transactional
    public Subscription createSubscription(
            Long userId,
            Long planId,
            String paymentMethod,
            String paymentReference) {

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        SubscriptionPlan plan = getSubscriptionPlanById(planId);

        // Check if user already has an active subscription
        Optional<Subscription> existingSubscription = subscriptionRepository.findActiveSubscriptionByUser(user);
        if (existingSubscription.isPresent()) {
            throw new IllegalStateException("User already has an active subscription");
        }

        return createSubscriptionInternal(user, plan, paymentMethod, paymentReference);
    }

    /**
     * Creates a subscription by email.
     *
     * @param email User email
     * @param planId Plan ID
     * @param paymentMethod Payment method
     * @param paymentReference Payment reference
     * @return Created subscription
     */
    @Transactional
    public Subscription createSubscriptionByEmail(
            String email,
            Long planId,
            String paymentMethod,
            String paymentReference) {

        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        SubscriptionPlan plan = getSubscriptionPlanById(planId);

        // Check if user already has an active subscription
        Optional<Subscription> existingSubscription = subscriptionRepository.findActiveSubscriptionByUser(user);
        if (existingSubscription.isPresent()) {
            throw new IllegalStateException("User already has an active subscription");
        }

        return createSubscriptionInternal(user, plan, paymentMethod, paymentReference);
    }

    /**
     * Internal method to create a subscription.
     *
     * @param user User
     * @param plan Subscription plan
     * @param paymentMethod Payment method
     * @param paymentReference Payment reference
     * @return Created subscription
     */
    private Subscription createSubscriptionInternal(
            User user,
            SubscriptionPlan plan,
            String paymentMethod,
            String paymentReference) {

        // Create new subscription
        Subscription subscription = new Subscription();
        subscription.setUser(user);
        subscription.setPlan(plan);
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        subscription.setStartDate(LocalDateTime.now());
        subscription.setEndDate(LocalDateTime.now().plusMonths(plan.getDurationMonths()));
        subscription.setAutoRenew(true);
        subscription.setLastPaymentDate(LocalDateTime.now());
        subscription.setNextPaymentDate(subscription.getEndDate().minusDays(7)); // Notify 7 days before expiry
        subscription.setPaymentMethod(paymentMethod);
        subscription.setPaymentReference(paymentReference);

        Subscription savedSubscription = subscriptionRepository.save(subscription);

        // Send confirmation email
        emailService.sendSimpleEmail(
                user.getEmail(),
                "Subscription Confirmation",
                "Thank you for subscribing to our " + plan.getName() + " plan. Your subscription is now active and will expire on " + subscription.getEndDate() + "."
        );

        return savedSubscription;
    }

    /**
     * Gets a subscription by ID.
     *
     * @param id Subscription ID
     * @return Subscription
     */
    public Subscription getSubscriptionById(Long id) {
        return subscriptionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Subscription not found with ID: " + id));
    }

    /**
     * Gets active subscription for a user.
     *
     * @param userId User ID
     * @return Optional of Subscription
     */
    public Optional<Subscription> getActiveSubscriptionForUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        return subscriptionRepository.findActiveSubscriptionByUser(user);
    }

    /**
     * Gets active subscription for a user by email.
     *
     * @param email User email
     * @return Optional of Subscription
     */
    public Optional<Subscription> getActiveSubscriptionForUserByEmail(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        return subscriptionRepository.findActiveSubscriptionByUser(user);
    }

    /**
     * Gets all subscriptions for a user.
     *
     * @param userId User ID
     * @param pageable Pageable
     * @return Page of subscriptions
     */
    public Page<Subscription> getSubscriptionsForUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        return subscriptionRepository.findByUser(user, pageable);
    }

    /**
     * Gets all subscriptions for a user by email.
     *
     * @param email User email
     * @param pageable Pageable
     * @return Page of subscriptions
     */
    public Page<Subscription> getSubscriptionsForUserByEmail(String email, Pageable pageable) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        return subscriptionRepository.findByUser(user, pageable);
    }

    /**
     * Cancels a subscription.
     *
     * @param id Subscription ID
     * @return Cancelled subscription
     */
    @Transactional
    public Subscription cancelSubscription(Long id) {
        Subscription subscription = getSubscriptionById(id);
        subscription.cancel();

        Subscription savedSubscription = subscriptionRepository.save(subscription);

        // Send cancellation email
        emailService.sendSimpleEmail(
                subscription.getUser().getEmail(),
                "Subscription Cancelled",
                "Your subscription has been cancelled. You will continue to have access until " + subscription.getEndDate() + "."
        );

        return savedSubscription;
    }

    /**
     * Renews a subscription.
     *
     * @param id Subscription ID
     * @param paymentMethod Payment method
     * @param paymentReference Payment reference
     * @return Renewed subscription
     */
    @Transactional
    public Subscription renewSubscription(
            Long id,
            String paymentMethod,
            String paymentReference) {

        Subscription subscription = getSubscriptionById(id);

        // Renew subscription
        subscription.renew(subscription.getPlan().getDurationMonths());
        subscription.setPaymentMethod(paymentMethod);
        subscription.setPaymentReference(paymentReference);

        Subscription savedSubscription = subscriptionRepository.save(subscription);

        // Send renewal email
        emailService.sendSimpleEmail(
                subscription.getUser().getEmail(),
                "Subscription Renewed",
                "Your subscription has been renewed and will now expire on " + subscription.getEndDate() + "."
        );

        return savedSubscription;
    }

    /**
     * Checks if a user has an active subscription.
     *
     * @param userId User ID
     * @return true if user has an active subscription, false otherwise
     */
    public boolean hasActiveSubscription(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        return subscriptionRepository.hasActiveSubscription(user);
    }

    /**
     * Checks if a user has an active subscription by email.
     *
     * @param email User email
     * @return true if user has an active subscription, false otherwise
     */
    public boolean hasActiveSubscriptionByEmail(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        return subscriptionRepository.hasActiveSubscription(user);
    }

    /**
     * Gets subscriptions expiring soon.
     *
     * @param days Days until expiry
     * @param pageable Pageable
     * @return Page of subscriptions
     */
    public Page<Subscription> getSubscriptionsExpiringSoon(int days, Pageable pageable) {
        LocalDateTime expiryDate = LocalDateTime.now().plusDays(days);
        return subscriptionRepository.findByEndDateBeforeAndStatus(expiryDate, SubscriptionStatus.ACTIVE, pageable);
    }

    /**
     * Notifies users about expiring subscriptions.
     *
     * @param days Days until expiry
     */
    @Transactional
    public void notifyExpiringSubscriptions(int days) {
        LocalDateTime expiryDate = LocalDateTime.now().plusDays(days);
        Page<Subscription> expiringSubscriptions = subscriptionRepository.findByEndDateBeforeAndStatus(
                expiryDate, SubscriptionStatus.ACTIVE, Pageable.unpaged());

        for (Subscription subscription : expiringSubscriptions) {
            emailService.sendSimpleEmail(
                    subscription.getUser().getEmail(),
                    "Subscription Expiring Soon",
                    "Your subscription will expire on " + subscription.getEndDate() + ". Please renew to continue enjoying premium features."
            );
        }
    }
}



