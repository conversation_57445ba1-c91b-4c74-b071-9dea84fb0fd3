package com.pressmeet247.subscription.repository;


import com.pressmeet247.auth.entity.User;
import com.pressmeet247.subscription.entity.Subscription;
import com.pressmeet247.subscription.entity.SubscriptionPlan;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for Subscription entity operations.
 */
@Repository
public interface SubscriptionRepository extends JpaRepository<Subscription, Long> {

    /**
     * Find active subscription by user.
     *
     * @param user User
     * @return Optional of Subscription
     */
    @Query("SELECT s FROM Subscription s WHERE s.user = :user AND s.status = 'ACTIVE' AND s.startDate <= CURRENT_TIMESTAMP AND s.endDate > CURRENT_TIMESTAMP")
    Optional<Subscription> findActiveSubscriptionByUser(@Param("user") User user);

    /**
     * Find subscriptions by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByUser(User user, Pageable pageable);

    /**
     * Find subscriptions by plan.
     *
     * @param plan Plan
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByPlan(SubscriptionPlan plan, Pageable pageable);

    /**
     * Find subscriptions by status.
     *
     * @param status Status
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByStatus(SubscriptionStatus status, Pageable pageable);

    /**
     * Find subscriptions by user and status.
     *
     * @param user User
     * @param status Status
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByUserAndStatus(User user, SubscriptionStatus status, Pageable pageable);

    /**
     * Find subscriptions expiring soon.
     *
     * @param endDate End date
     * @param status Status
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByEndDateBeforeAndStatus(LocalDateTime endDate, SubscriptionStatus status, Pageable pageable);

    /**
     * Find subscriptions with auto-renew enabled.
     *
     * @param autoRenew Auto-renew flag
     * @param pageable Pageable
     * @return Page of Subscription
     */
    Page<Subscription> findByAutoRenew(boolean autoRenew, Pageable pageable);

    /**
     * Check if user has an active subscription.
     *
     * @param user User
     * @return true if user has an active subscription, false otherwise
     */
    @Query("SELECT COUNT(s) > 0 FROM Subscription s WHERE s.user = :user AND s.status = 'ACTIVE' AND s.startDate <= CURRENT_TIMESTAMP AND s.endDate > CURRENT_TIMESTAMP")
    boolean hasActiveSubscription(@Param("user") User user);

    /**
     * Count subscriptions by status.
     *
     * @param status Status
     * @return Count of subscriptions with the specified status
     */
    long countByStatus(SubscriptionStatus status);
}



