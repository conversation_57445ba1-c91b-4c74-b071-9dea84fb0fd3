package com.pressmeet247.subscription.repository;


import com.pressmeet247.subscription.entity.SubscriptionPlan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for SubscriptionPlan entity operations.
 */
@Repository
public interface SubscriptionPlanRepository extends JpaRepository<SubscriptionPlan, Long> {
    
    /**
     * Find plan by name.
     *
     * @param name Plan name
     * @return Optional of SubscriptionPlan
     */
    Optional<SubscriptionPlan> findByName(String name);
    
    /**
     * Find active plans.
     *
     * @param active Active flag
     * @return List of SubscriptionPlan
     */
    List<SubscriptionPlan> findByActive(boolean active);
    
    /**
     * Find active plans with pagination.
     *
     * @param active Active flag
     * @param pageable Pageable
     * @return Page of SubscriptionPlan
     */
    Page<SubscriptionPlan> findByActive(boolean active, Pageable pageable);
    
    /**
     * Check if a plan exists with the given name.
     *
     * @param name Plan name
     * @return true if plan exists, false otherwise
     */
    boolean existsByName(String name);
}

