package com.pressmeet247.subscription.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a subscription plan in the system.
 */
@Entity
@Table(
    name = "subscription_plans",
    indexes = {
        @Index(name = "idx_subscription_plans_name", columnList = "name"),
        @Index(name = "idx_subscription_plans_active", columnList = "active"),
        @Index(name = "idx_subscription_plans_created_at", columnList = "created_at"),
        @Index(name = "idx_subscription_plans_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE subscription_plans SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a subscription plan")
public class SubscriptionPlan extends BaseEntity {

    @Column(name = "name", length = 100, nullable = false, unique = true)
    @Schema(description = "Plan name", example = "Premium")
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    @Schema(description = "Plan description")
    private String description;

    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    @Schema(description = "Plan price", example = "9.99")
    private BigDecimal price;

    @Column(name = "currency", length = 3, nullable = false)
    @Schema(description = "Currency code", example = "USD")
    private String currency = "USD";

    @Column(name = "duration_months", nullable = false)
    @Schema(description = "Duration in months", example = "1")
    private int durationMonths;

    @Column(name = "active", nullable = false)
    @Schema(description = "Whether the plan is active", example = "true")
    private boolean active = true;

    @Column(name = "features", columnDefinition = "TEXT")
    @Schema(description = "Plan features as JSON", example = "{\"feature1\": true, \"feature2\": false}")
    private String features;

    @OneToMany(mappedBy = "plan", cascade = CascadeType.ALL, orphanRemoval = true)
    @Schema(description = "Subscriptions using this plan")
    private List<Subscription> subscriptions = new ArrayList<>();
}

