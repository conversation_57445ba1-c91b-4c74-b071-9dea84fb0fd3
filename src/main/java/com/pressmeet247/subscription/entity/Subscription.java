package com.pressmeet247.subscription.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing a user subscription in the system.
 */
@Entity
@Table(
    name = "subscriptions",
    indexes = {
        @Index(name = "idx_subscriptions_user_id", columnList = "user_id"),
        @Index(name = "idx_subscriptions_plan_id", columnList = "plan_id"),
        @Index(name = "idx_subscriptions_status", columnList = "status"),
        @Index(name = "idx_subscriptions_created_at", columnList = "created_at"),
        @Index(name = "idx_subscriptions_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE subscriptions SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a user subscription")
public class Subscription extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @Schema(description = "User who owns the subscription")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id", nullable = false)
    @Schema(description = "Subscription plan")
    private SubscriptionPlan plan;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Schema(description = "Subscription status", example = "ACTIVE")
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;

    @Column(name = "start_date", nullable = false)
    @Schema(description = "Subscription start date")
    private LocalDateTime startDate;

    @Column(name = "end_date", nullable = false)
    @Schema(description = "Subscription end date")
    private LocalDateTime endDate;

    @Column(name = "auto_renew", nullable = false)
    @Schema(description = "Whether the subscription auto-renews", example = "true")
    private boolean autoRenew = true;

    @Column(name = "last_payment_date")
    @Schema(description = "Date of the last payment")
    private LocalDateTime lastPaymentDate;

    @Column(name = "next_payment_date")
    @Schema(description = "Date of the next payment")
    private LocalDateTime nextPaymentDate;

    @Column(name = "payment_method", length = 50)
    @Schema(description = "Payment method", example = "CREDIT_CARD")
    private String paymentMethod;

    @Column(name = "payment_reference", length = 100)
    @Schema(description = "Payment reference", example = "txn_1234567890")
    private String paymentReference;

    /**
     * Checks if the subscription is active.
     *
     * @return true if active, false otherwise
     */
    public boolean isActive() {
        return status == SubscriptionStatus.ACTIVE && 
               LocalDateTime.now().isAfter(startDate) && 
               LocalDateTime.now().isBefore(endDate);
    }

    /**
     * Cancels the subscription.
     */
    public void cancel() {
        this.status = SubscriptionStatus.CANCELLED;
        this.autoRenew = false;
    }

    /**
     * Renews the subscription for the specified period.
     *
     * @param months Number of months to renew for
     */
    public void renew(int months) {
        this.startDate = this.endDate;
        this.endDate = this.endDate.plusMonths(months);
        this.lastPaymentDate = LocalDateTime.now();
        this.nextPaymentDate = this.endDate.minusDays(7); // Notify 7 days before expiry
        this.status = SubscriptionStatus.ACTIVE;
    }
}



