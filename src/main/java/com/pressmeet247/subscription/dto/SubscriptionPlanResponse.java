package com.pressmeet247.subscription.dto;


import com.pressmeet247.subscription.entity.SubscriptionPlan;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for subscription plan response.
 */
@Data
@Builder
@Schema(description = "Subscription plan response")
public class SubscriptionPlanResponse {
    
    @Schema(description = "Plan ID", example = "1")
    private Long id;
    
    @Schema(description = "Plan name", example = "Premium")
    private String name;
    
    @Schema(description = "Plan description", example = "Premium plan with all features")
    private String description;
    
    @Schema(description = "Plan price", example = "9.99")
    private BigDecimal price;
    
    @Schema(description = "Currency code", example = "USD")
    private String currency;
    
    @Schema(description = "Duration in months", example = "1")
    private int durationMonths;
    
    @Schema(description = "Whether the plan is active", example = "true")
    private boolean active;
    
    @Schema(description = "Plan features as JSON", example = "{\"feature1\": true, \"feature2\": false}")
    private String features;
    
    @Schema(description = "Date and time when the plan was created")
    private LocalDateTime createdAt;
    
    /**
     * Converts a SubscriptionPlan entity to SubscriptionPlanResponse DTO.
     *
     * @param plan SubscriptionPlan entity
     * @return SubscriptionPlanResponse DTO
     */
    public static SubscriptionPlanResponse fromEntity(SubscriptionPlan plan) {
        return SubscriptionPlanResponse.builder()
                .id(plan.getId())
                .name(plan.getName())
                .description(plan.getDescription())
                .price(plan.getPrice())
                .currency(plan.getCurrency())
                .durationMonths(plan.getDurationMonths())
                .active(plan.isActive())
                .features(plan.getFeatures())
                .createdAt(plan.getCreatedAt())
                .build();
    }
}

