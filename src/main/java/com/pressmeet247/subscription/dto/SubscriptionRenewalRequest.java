package com.pressmeet247.subscription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * DTO for subscription renewal request.
 */
@Data
@Schema(description = "Subscription renewal request")
public class SubscriptionRenewalRequest {
    
    @NotBlank(message = "Payment method is required")
    @Schema(description = "Payment method", example = "CREDIT_CARD")
    private String paymentMethod;
    
    @NotBlank(message = "Payment reference is required")
    @Schema(description = "Payment reference", example = "txn_1234567890")
    private String paymentReference;
}

