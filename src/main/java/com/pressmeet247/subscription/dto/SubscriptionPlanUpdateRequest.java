package com.pressmeet247.subscription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DTO for subscription plan update request.
 */
@Data
@Schema(description = "Subscription plan update request")
public class SubscriptionPlanUpdateRequest {
    
    @Schema(description = "Plan name", example = "Premium")
    private String name;
    
    @Schema(description = "Plan description", example = "Premium plan with all features")
    private String description;
    
    @Schema(description = "Plan price", example = "9.99")
    private BigDecimal price;
    
    @Schema(description = "Currency code", example = "USD")
    private String currency;
    
    @Schema(description = "Duration in months", example = "1")
    private int durationMonths;
    
    @Schema(description = "Plan features as JSON", example = "{\"feature1\": true, \"feature2\": false}")
    private String features;
    
    @Schema(description = "Whether the plan is active", example = "true")
    private boolean active = true;
}

