package com.pressmeet247.subscription.dto;


import com.pressmeet247.subscription.entity.Subscription;
import com.pressmeet247.subscription.entity.SubscriptionStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for subscription response.
 */
@Data
@Builder
@Schema(description = "Subscription response")
public class SubscriptionResponse {
    
    @Schema(description = "Subscription ID", example = "1")
    private Long id;
    
    @Schema(description = "User ID", example = "1")
    private Long userId;
    
    @Schema(description = "User name", example = "<PERSON>")
    private String userName;
    
    @Schema(description = "Plan ID", example = "1")
    private Long planId;
    
    @Schema(description = "Plan name", example = "Premium")
    private String planName;
    
    @Schema(description = "Subscription status", example = "ACTIVE")
    private SubscriptionStatus status;
    
    @Schema(description = "Start date")
    private LocalDateTime startDate;
    
    @Schema(description = "End date")
    private LocalDateTime endDate;
    
    @Schema(description = "Whether the subscription auto-renews", example = "true")
    private boolean autoRenew;
    
    @Schema(description = "Last payment date")
    private LocalDateTime lastPaymentDate;
    
    @Schema(description = "Next payment date")
    private LocalDateTime nextPaymentDate;
    
    @Schema(description = "Payment method", example = "CREDIT_CARD")
    private String paymentMethod;
    
    @Schema(description = "Date and time when the subscription was created")
    private LocalDateTime createdAt;
    
    /**
     * Converts a Subscription entity to SubscriptionResponse DTO.
     *
     * @param subscription Subscription entity
     * @return SubscriptionResponse DTO
     */
    public static SubscriptionResponse fromEntity(Subscription subscription) {
        String userName = subscription.getUser().getFirstName() + " " + subscription.getUser().getLastName();
        
        return SubscriptionResponse.builder()
                .id(subscription.getId())
                .userId(subscription.getUser().getId())
                .userName(userName)
                .planId(subscription.getPlan().getId())
                .planName(subscription.getPlan().getName())
                .status(subscription.getStatus())
                .startDate(subscription.getStartDate())
                .endDate(subscription.getEndDate())
                .autoRenew(subscription.isAutoRenew())
                .lastPaymentDate(subscription.getLastPaymentDate())
                .nextPaymentDate(subscription.getNextPaymentDate())
                .paymentMethod(subscription.getPaymentMethod())
                .createdAt(subscription.getCreatedAt())
                .build();
    }
}

