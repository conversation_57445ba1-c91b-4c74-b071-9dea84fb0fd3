package com.pressmeet247.subscription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DTO for subscription plan request.
 */
@Data
@Schema(description = "Subscription plan request")
public class SubscriptionPlanRequest {
    
    @NotBlank(message = "Name is required")
    @Schema(description = "Plan name", example = "Premium")
    private String name;
    
    @Schema(description = "Plan description", example = "Premium plan with all features")
    private String description;
    
    @NotNull(message = "Price is required")
    @Positive(message = "Price must be positive")
    @Schema(description = "Plan price", example = "9.99")
    private BigDecimal price;
    
    @Schema(description = "Currency code", example = "USD")
    private String currency = "USD";
    
    @NotNull(message = "Duration is required")
    @Positive(message = "Duration must be positive")
    @Schema(description = "Duration in months", example = "1")
    private int durationMonths;
    
    @Schema(description = "Plan features as JSON", example = "{\"feature1\": true, \"feature2\": false}")
    private String features;
}

