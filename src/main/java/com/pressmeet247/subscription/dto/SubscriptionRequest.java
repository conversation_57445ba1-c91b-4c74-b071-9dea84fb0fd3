package com.pressmeet247.subscription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for subscription request.
 */
@Data
@Schema(description = "Subscription request")
public class SubscriptionRequest {
    
    @NotNull(message = "Plan ID is required")
    @Schema(description = "Plan ID", example = "1")
    private Long planId;
    
    @NotBlank(message = "Payment method is required")
    @Schema(description = "Payment method", example = "CREDIT_CARD")
    private String paymentMethod;
    
    @NotBlank(message = "Payment reference is required")
    @Schema(description = "Payment reference", example = "txn_1234567890")
    private String paymentReference;
}

