package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.entity.UserPreferences;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository for UserPreferences entity.
 */
@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Long> {
    
    /**
     * Finds user preferences by user.
     *
     * @param user User
     * @return UserPreferences
     */
    UserPreferences findByUser(User user);
}

