package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for Role entity operations.
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * Find a role by name.
     *
     * @param name Role name
     * @return Optional containing the role if found
     */
    Optional<Role> findByName(String name);
    
    /**
     * Check if a role exists with the given name.
     *
     * @param name Role name
     * @return true if role exists, false otherwise
     */
    boolean existsByName(String name);
}



