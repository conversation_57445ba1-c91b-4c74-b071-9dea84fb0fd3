package com.pressmeet247.auth.repository;

import jakarta.persistence.EntityManager;

/**
 * Custom repository interface for User entity operations.
 */
public interface CustomUserRepository {
    
    /**
     * Gets the EntityManager.
     * 
     * @return EntityManager
     */
    EntityManager getEntityManager();
    
    /**
     * Adds a role to a user using a native SQL query.
     * 
     * @param userId User ID
     * @param roleId Role ID
     */
    void addRoleToUser(Long userId, Long roleId);
}
