package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.LoginHistory;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for LoginHistory entity.
 */
@Repository
public interface LoginHistoryRepository extends JpaRepository<LoginHistory, Long> {
    
    /**
     * Finds login history by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of LoginHistory
     */
    Page<LoginHistory> findByUser(User user, Pageable pageable);
    
    /**
     * Finds login history by user and successful flag.
     *
     * @param user User
     * @param successful Successful flag
     * @param pageable Pageable
     * @return Page of LoginHistory
     */
    Page<LoginHistory> findByUserAndSuccessful(User user, boolean successful, Pageable pageable);
    
    /**
     * Finds login history by user and attempt time between.
     *
     * @param user User
     * @param start Start time
     * @param end End time
     * @param pageable Pageable
     * @return Page of LoginHistory
     */
    Page<LoginHistory> findByUserAndAttemptTimeBetween(User user, LocalDateTime start, LocalDateTime end, Pageable pageable);
    
    /**
     * Counts failed login attempts for a user within a time period.
     *
     * @param user User
     * @param start Start time
     * @return Number of failed login attempts
     */
    @Query("SELECT COUNT(l) FROM LoginHistory l WHERE l.user = :user AND l.successful = false AND l.attemptTime > :start")
    long countFailedLoginAttemptsSince(@Param("user") User user, @Param("start") LocalDateTime start);
    
    /**
     * Finds recent login history by user.
     *
     * @param user User
     * @param limit Limit
     * @return List of LoginHistory
     */
    @Query("SELECT l FROM LoginHistory l WHERE l.user = :user ORDER BY l.attemptTime DESC")
    List<LoginHistory> findRecentByUser(@Param("user") User user, Pageable pageable);
}

