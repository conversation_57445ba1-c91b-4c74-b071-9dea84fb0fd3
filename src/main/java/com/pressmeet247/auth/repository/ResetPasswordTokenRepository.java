package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.ResetPasswordToken;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for ResetPasswordToken entity operations.
 */
@Repository
public interface ResetPasswordTokenRepository extends JpaRepository<ResetPasswordToken, String> {
    
    /**
     * Find all reset tokens for a user.
     *
     * @param user User entity
     * @return List of reset tokens
     */
    List<ResetPasswordToken> findByUser(User user);
    
    /**
     * Find reset token by token string.
     *
     * @param token Token string
     * @return Optional containing the token if found
     */
    Optional<ResetPasswordToken> findByToken(String token);
    
    /**
     * Delete all tokens for a user.
     *
     * @param user User entity
     */
    void deleteByUser(User user);
    
    /**
     * Find expired reset tokens.
     *
     * @param now Current timestamp
     * @return List of expired reset tokens
     */
    @Query("SELECT rt FROM ResetPasswordToken rt WHERE rt.expirationTime < :now")
    List<ResetPasswordToken> findExpiredTokens(LocalDateTime now);
}

