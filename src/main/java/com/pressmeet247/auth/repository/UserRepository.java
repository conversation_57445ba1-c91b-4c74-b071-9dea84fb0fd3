package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for User entity operations.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, CustomUserRepository {

    /**
     * Find a user by email.
     *
     * @param email User's email
     * @return User if found, null otherwise
     */
    User findByEmail(String email);

    /**
     * Find a user by email as Optional.
     *
     * @param email User's email
     * @return Optional containing the user if found
     */
    Optional<User> findOptionalByEmail(String email);

    /**
     * Check if a user exists with the given email.
     *
     * @param email User's email
     * @return true if user exists, false otherwise
     */
    boolean existsByEmail(String email);

    /**
     * Find users that have not verified their email and whose verification token has not expired.
     *
     * @param cutoffDate Date before which to consider accounts stale
     * @return List of users with unverified emails
     */
    @Query("SELECT u FROM User u WHERE u.emailVerified = false AND u.createdAt < :cutoffDate")
    List<User> findUnverifiedUsersBefore(LocalDateTime cutoffDate);

    /**
     * Find users by enabled status.
     *
     * @param enabled Enabled status
     * @return List of users with the specified enabled status
     */
    List<User> findByEnabled(boolean enabled);

    /**
     * Find users by enabled status with pagination.
     *
     * @param enabled Enabled status
     * @param pageable Pagination information
     * @return Page of users with the specified enabled status
     */
    Page<User> findByEnabled(boolean enabled, Pageable pageable);

    /**
     * Find users by email verification status.
     *
     * @param emailVerified Email verification status
     * @param pageable Pagination information
     * @return Page of users with the specified email verification status
     */
    Page<User> findByEmailVerified(boolean emailVerified, Pageable pageable);

    /**
     * Find users by phone verification status.
     *
     * @param phoneVerified Phone verification status
     * @param pageable Pagination information
     * @return Page of users with the specified phone verification status
     */
    Page<User> findByPhoneVerified(boolean phoneVerified, Pageable pageable);

    /**
     * Find users by last login date before a specific date.
     *
     * @param date Date before which to find users
     * @param pageable Pagination information
     * @return Page of users who haven't logged in since the specified date
     */
    Page<User> findByLastLoginAtBefore(LocalDateTime date, Pageable pageable);

    /**
     * Find users by last login date after a specific date.
     *
     * @param date Date after which to find users
     * @param pageable Pagination information
     * @return Page of users who have logged in since the specified date
     */
    Page<User> findByLastLoginAtAfter(LocalDateTime date, Pageable pageable);

    /**
     * Find users by creation date between two dates.
     *
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pagination information
     * @return Page of users created between the specified dates
     */
    Page<User> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Search users by first name, last name, or email containing the search term.
     *
     * @param searchTerm Search term
     * @param pageable Pagination information
     * @return Page of users matching the search term
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find users with locked accounts.
     *
     * @param now Current date/time
     * @param pageable Pagination information
     * @return Page of users with locked accounts
     */
    @Query("SELECT u FROM User u WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil > :now")
    Page<User> findLockedUsers(@Param("now") LocalDateTime now, Pageable pageable);

    /**
     * Find users with two-factor authentication enabled.
     *
     * @param pageable Pagination information
     * @return Page of users with two-factor authentication enabled
     */
    Page<User> findByTwoFactorEnabled(boolean twoFactorEnabled, Pageable pageable);

    /**
     * Find users by role.
     *
     * @param role Role
     * @param pageable Pagination information
     * @return Page of users with the specified role
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r = :role")
    Page<User> findByRoles(@Param("role") Role role, Pageable pageable);

    /**
     * Count users by role name.
     *
     * @param roleName Role name
     * @return Count of users with the specified role
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.name = :roleName")
    long countByRoles_Name(@Param("roleName") String roleName);

    /**
     * Find users by user type.
     *
     * @param userType User type
     * @return List of users with the specified user type
     */
    List<User> findByUserType(UserType userType);

    /**
     * Find users by user type with pagination.
     *
     * @param userType User type
     * @param pageable Pagination information
     * @return Page of users with the specified user type
     */
    Page<User> findByUserType(UserType userType, Pageable pageable);

    /**
     * Count users by user type.
     *
     * @param userType User type
     * @return Count of users with the specified user type
     */
    long countByUserType(UserType userType);
}


