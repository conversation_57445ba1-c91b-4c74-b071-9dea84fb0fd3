package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.SecurityEvent;
import com.pressmeet247.auth.entity.SecurityEventType;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for SecurityEvent entity.
 */
@Repository
public interface SecurityEventRepository extends JpaRepository<SecurityEvent, Long> {
    
    /**
     * Finds security events by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of SecurityEvent
     */
    Page<SecurityEvent> findByUser(User user, Pageable pageable);
    
    /**
     * Finds security events by user and event type.
     *
     * @param user User
     * @param eventType Event type
     * @param pageable Pageable
     * @return Page of SecurityEvent
     */
    Page<SecurityEvent> findByUserAndEventType(User user, SecurityEventType eventType, Pageable pageable);
    
    /**
     * Finds security events by user and created at between.
     *
     * @param user User
     * @param start Start time
     * @param end End time
     * @param pageable Pageable
     * @return Page of SecurityEvent
     */
    @Query("SELECT e FROM SecurityEvent e WHERE e.user = :user AND e.createdAt BETWEEN :start AND :end")
    Page<SecurityEvent> findByUserAndCreatedAtBetween(
            @Param("user") User user, 
            @Param("start") LocalDateTime start, 
            @Param("end") LocalDateTime end, 
            Pageable pageable);
    
    /**
     * Finds recent security events by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return List of SecurityEvent
     */
    @Query("SELECT e FROM SecurityEvent e WHERE e.user = :user ORDER BY e.createdAt DESC")
    List<SecurityEvent> findRecentByUser(@Param("user") User user, Pageable pageable);
    
    /**
     * Finds suspicious activity events by user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of SecurityEvent
     */
    Page<SecurityEvent> findByUserAndEventTypeIn(
            User user, 
            List<SecurityEventType> eventTypes, 
            Pageable pageable);
}

