package com.pressmeet247.auth.repository;

import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for AccountVerification entity operations.
 */
@Repository
public interface AccountVerificationRepository extends JpaRepository<AccountVerification, Long> {

    /**
     * Find verification by user.
     *
     * @param user User entity
     * @return AccountVerification if found
     */
    AccountVerification findByUser(User user);

    /**
     * Find verification by token.
     *
     * @param token Verification token
     * @return AccountVerification if found
     */
    AccountVerification findByToken(String token);

    /**
     * Find verification by token as Optional.
     *
     * @param token Verification token
     * @return Optional containing the verification if found
     */
    Optional<AccountVerification> findOptionalByToken(String token);

    /**
     * Find expired verification tokens.
     *
     * @param now Current timestamp
     * @return List of expired verification tokens
     */
    @Query("SELECT av FROM AccountVerification av WHERE av.expiresAt < :now")
    List<AccountVerification> findExpiredTokens(LocalDateTime now);

    /**
     * Find the most recent verification token for a user by email.
     *
     * @param email User's email
     * @return Optional containing the most recent verification if found
     */
    Optional<AccountVerification> findTopByUserEmailOrderByCreatedAtDesc(String email);
}

