package com.pressmeet247.auth.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of CustomUserRepository.
 */
public class CustomUserRepositoryImpl implements CustomUserRepository {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }
    
    @Override
    @Transactional
    public void addRoleToUser(Long userId, Long roleId) {
        entityManager.createNativeQuery("INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)")
                .setParameter(1, userId)
                .setParameter(2, roleId)
                .executeUpdate();
    }
}
