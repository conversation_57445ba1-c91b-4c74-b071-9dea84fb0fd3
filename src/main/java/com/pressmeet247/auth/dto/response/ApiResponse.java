package com.pressmeet247.auth.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Generic API response object.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Generic API response object")
public class ApiResponse<T> {
    
    @Schema(description = "Response status", example = "success")
    private String status;

    @Schema(description = "Response message", example = "Operation completed successfully")
    private String message;

    @Schema(description = "Response data")
    private T data;

    @Schema(description = "Timestamp of the response", example = "2023-12-31T23:59:59")
    private LocalDateTime timestamp;

    /**
     * Creates a success response.
     *
     * @param message Success message
     * @param data    Response data
     * @return ApiResponse object
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .status("success")
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Creates a success response with only a message.
     *
     * @param message Success message
     * @return ApiResponse object
     */
    public static <T> ApiResponse<T> success(String message) {
        return success(message, null);
    }

    /**
     * Creates an error response.
     *
     * @param message Error message
     * @param data    Error data
     * @return ApiResponse object
     */
    public static <T> ApiResponse<T> error(String message, T data) {
        return ApiResponse.<T>builder()
                .status("error")
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Creates an error response with only a message.
     *
     * @param message Error message
     * @return ApiResponse object
     */
    public static <T> ApiResponse<T> error(String message) {
        return error(message, null);
    }
}



