package com.pressmeet247.auth.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Data transfer object for authentication responses.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for authentication responses")
public class AuthResponse {
    
    @Schema(description = "JWT access token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "Refresh token", example = "7c9e6679-7425-40de-944b-e07fc1f90ae7")
    private String refreshToken;

    @Schema(description = "Access token expiration time", example = "2023-12-31T23:59:59")
    private LocalDateTime expirationTime;

    @Schema(description = "Token type", example = "Bearer")
    private String tokenType;

    @Schema(description = "User ID", example = "1")
    private Long userId;

    @Schema(description = "User email", example = "<EMAIL>")
    private String email;

    @Schema(description = "User roles", example = "USER")
    private String roles;
}



