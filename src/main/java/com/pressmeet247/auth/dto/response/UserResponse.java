package com.pressmeet247.auth.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data transfer object for user responses.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for user responses")
public class UserResponse {
    
    @Schema(description = "User ID", example = "1")
    private Long id;

    @Schema(description = "User's first name", example = "<PERSON>")
    private String firstName;

    @Schema(description = "User's last name", example = "Doe")
    private String lastName;

    @Schema(description = "User's email address", example = "<EMAIL>")
    private String email;

    @Schema(description = "User roles", example = "[\"USER\", \"ADMIN\"]")
    private List<String> roles;

    @Schema(description = "Indicates if the user account is enabled", example = "true")
    private boolean enabled;

    @Schema(description = "Indicates if the user's email is verified", example = "true")
    private boolean emailVerified;

    @Schema(description = "User's phone number", example = "+**********")
    private String phoneNumber;

    @Schema(description = "Indicates if the user's phone is verified", example = "true")
    private boolean phoneVerified;

    @Schema(description = "Account creation timestamp", example = "2023-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "Last login timestamp", example = "2023-01-02T14:30:00")
    private LocalDateTime lastLoginAt;
}
