package com.pressmeet247.auth.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Data transfer object for refresh token requests.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for refresh token requests")
public class RefreshTokenRequest {
    
    @NotBlank(message = "Refresh token is mandatory")
    @Schema(description = "Refresh token", example = "7c9e6679-7425-40de-944b-e07fc1f90ae7")
    private String refreshToken;
}
