package com.pressmeet247.auth.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Data transfer object for setting a new password.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for setting a new password")
public class SetPasswordRequest {

    @NotBlank(message = "Token is mandatory")
    @Schema(description = "Temporary token for password setting", example = "95ceec70-e63e-4f00-a96c-8604134cb291")
    private String token;

    @NotBlank(message = "New password is mandatory")
    @Size(min = 8, message = "Password must be at least 8 characters long")
    @Pattern(
        regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=!])(?=\\S+$).{8,}$",
        message = "Password must contain at least one digit, one lowercase letter, one uppercase letter, one special character, and no whitespace"
    )
    @Schema(description = "New password to set", example = "NewStrongPassword123!")
    private String newPassword;
}
