package com.pressmeet247.auth.service;

import com.pressmeet247.auth.dto.request.LoginRequest;
import com.pressmeet247.auth.dto.request.RefreshTokenRequest;
import com.pressmeet247.auth.dto.request.SignUpRequest;
import com.pressmeet247.auth.dto.response.AuthResponse;
import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;

import com.pressmeet247.auth.exception.AuthenticationException;
import com.pressmeet247.auth.exception.TokenException;
import com.pressmeet247.auth.exception.UserAlreadyExistsException;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.IEmailService;
import com.pressmeet247.auth.token.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

/**
 * Service for authentication operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final TokenService tokenService;
    private final IEmailService emailService;

    /**
     * Registers a new user.
     *
     * @param request SignUp request
     * @return Registered user
     * @throws UserAlreadyExistsException if user already exists
     */
    @Transactional
    public User registerUser(SignUpRequest request) {
        // Check if user already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            User existingUser = userRepository.findByEmail(request.getEmail());

            if (!existingUser.isEnabled()) {
                // Resend verification email for disabled users
                emailService.sendVerificationEmail(existingUser);
                return existingUser;
            }

            throw new UserAlreadyExistsException("User already exists with email: " + request.getEmail());
        }

        // Create new user
        User user = new User();
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEnabled(false);
        user.setEmailVerified(false);
        user.setCreatedAt(LocalDateTime.now());

        // Assign USER role
        Optional<Role> userRole = roleRepository.findByName("USER");
        if (userRole.isPresent()) {
            user.setRoles(Collections.singletonList(userRole.get()));
        } else {
            log.warn("USER role not found when registering user");
        }

        // Save user
        user = userRepository.save(user);

        // Send verification email
        emailService.sendVerificationEmail(user);

        log.info("User registered successfully: {}", user.getEmail());
        return user;
    }

    /**
     * Validates user credentials without generating tokens.
     *
     * @param email User's email
     * @param password User's password
     * @return User if credentials are valid
     * @throws AuthenticationException if authentication fails
     */
    public User validateCredentials(String email, String password) {
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(email, password)
            );

            User user = (User) authentication.getPrincipal();
            log.info("Credentials validated successfully for user: {}", user.getEmail());
            return user;
        } catch (BadCredentialsException e) {
            log.warn("Credential validation failed for user: {}", email);
            throw new AuthenticationException("Invalid email or password");
        }
    }

    /**
     * Authenticates a user.
     *
     * @param request Login request
     * @param httpRequest HTTP request
     * @return Authentication response
     * @throws AuthenticationException if authentication fails
     */
    @Transactional
    public AuthResponse login(LoginRequest request, HttpServletRequest httpRequest) {
        try {
            // Validate credentials
            User user = validateCredentials(request.getEmail(), request.getPassword());

            // Set authentication in security context
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                    user, null, user.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // Update last login time
            user.setLastLoginAt(LocalDateTime.now());
            userRepository.save(user);

            // Generate tokens
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");

            AuthResponse response = tokenService.generateTokenResponse(user, ipAddress, userAgent);

            log.info("User logged in successfully: {}", user.getEmail());
            return response;
        } catch (AuthenticationException e) {
            log.warn("Authentication failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during login: {}", e.getMessage(), e);
            throw new AuthenticationException("An unexpected error occurred during login");
        }
    }

    /**
     * Refreshes an access token.
     *
     * @param request Refresh token request
     * @param httpRequest HTTP request
     * @return Authentication response
     * @throws TokenException if token is invalid
     */
    @Transactional
    public AuthResponse refreshToken(RefreshTokenRequest request, HttpServletRequest httpRequest) {
        try {
            String refreshToken = request.getRefreshToken();
            if (refreshToken == null || refreshToken.isEmpty()) {
                log.warn("Refresh token is null or empty");
                throw new TokenException("Invalid refresh token");
            }

            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");

            AuthResponse response = tokenService.refreshAccessToken(refreshToken, ipAddress, userAgent);

            log.info("Access token refreshed successfully for token: {}", refreshToken);
            return response;
        } catch (TokenException e) {
            log.warn("Token refresh failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during token refresh: {}", e.getMessage(), e);
            throw new TokenException("Failed to refresh token: " + e.getMessage());
        }
    }

    /**
     * Logs out a user.
     *
     * @param user User to log out
     */
    @Transactional
    public void logout(User user) {
        tokenService.revokeAllUserTokens(user);
        log.info("User logged out successfully: {}", user.getEmail());
    }

    /**
     * Verifies a user's account using a token.
     *
     * @param token Verification token
     * @return True if verification successful
     * @throws TokenException if token is invalid
     */
    @Transactional
    public boolean verifyAccount(String token) {
        AccountVerification verification = tokenService.validateVerificationToken(token);
        User user = verification.getUser();

        // Make sure the user is enabled and email is verified
        user.setEnabled(true);
        user.setEmailVerified(true);

        // Reset any failed login attempts and unlock the account
        user.setFailedLoginAttempts(0);
        user.setLockedUntil(null);

        userRepository.save(user);

        // Delete the used verification token
        tokenService.deleteVerificationToken(verification);

        log.info("Account verified successfully using token: {}", user.getEmail());
        return true;
    }

    /**
     * Verifies a user's account using an OTP code.
     *
     * @param email User's email
     * @param otpCode OTP code
     * @return True if verification successful
     * @throws TokenException if OTP is invalid
     */
    @Transactional
    public boolean verifyAccountWithOtp(String email, String otpCode) {
        AccountVerification verification = tokenService.validateOtpCode(email, otpCode);
        User user = verification.getUser();

        // Make sure the user is enabled and email is verified
        user.setEnabled(true);
        user.setEmailVerified(true);

        // Reset any failed login attempts and unlock the account
        user.setFailedLoginAttempts(0);
        user.setLockedUntil(null);

        userRepository.save(user);

        // Delete the used verification token
        tokenService.deleteVerificationToken(verification);

        log.info("Account verified successfully using OTP: {}", user.getEmail());
        return true;
    }



    /**
     * Resends a verification email.
     *
     * @param email User's email
     * @return True if email sent
     */
    @Transactional
    public boolean resendVerificationEmail(String email) {
        User user = userRepository.findByEmail(email);

        if (user == null) {
            log.warn("User not found for resend verification: {}", email);
            return false;
        }

        if (user.isEmailVerified()) {
            log.info("User already verified: {}", email);
            return false;
        }

        emailService.sendVerificationEmail(user);

        log.info("Verification email resent to: {}", email);
        return true;
    }

    /**
     * Gets the client IP address from the request.
     *
     * @param request HTTP request
     * @return IP address
     */
    private String getClientIp(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0];
    }
}



