package com.pressmeet247.auth.service;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for loading user details for Spring Security.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;

    /**
     * Loads a user by username (email).
     *
     * @param email User's email
     * @return UserDetails
     * @throws UsernameNotFoundException if user not found or not enabled
     */
    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            log.warn("User not found with email: {}", email);
            throw new UsernameNotFoundException("User not found with email: " + email);
        }

        if (!user.isEnabled()) {
            log.warn("User account is not enabled: {}", email);

            // Check if email is verified
            if (!user.isEmailVerified()) {
                log.warn("User email is not verified: {}", email);
                throw new UsernameNotFoundException("Please verify your email before logging in. Check your inbox for a verification link.");
            }

            throw new UsernameNotFoundException("Your account is not enabled. Please contact support.");
        }

        return user;
    }

    /**
     * Saves a user.
     *
     * @param user User to save
     * @return Saved user
     */
    @Transactional
    public User saveUser(User user) {
        // Ensure version is not null
        if (user.getVersion() == null) {
            user.setVersion(0);
        }
        return userRepository.save(user);
    }
}
