package com.pressmeet247.auth.service;

import com.pressmeet247.auth.dto.request.PasswordResetConfirmRequest;
import com.pressmeet247.auth.dto.request.PasswordResetRequest;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.auth.entity.ResetPasswordToken;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.exception.TokenException;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.IEmailService;
import com.pressmeet247.auth.token.TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * Service for user management.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final TokenService tokenService;
    private final IEmailService emailService;

    /**
     * Gets a user by ID.
     *
     * @param id User ID
     * @return User response
     * @throws ResourceNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public UserResponse getUserById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));

        return mapUserToResponse(user);
    }

    /**
     * Gets a user by email.
     *
     * @param email User email
     * @return User response
     * @throws ResourceNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public UserResponse getUserByEmail(String email) {
        User user = userRepository.findByEmail(email);

        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        return mapUserToResponse(user);
    }

    /**
     * Initiates a password reset.
     *
     * @param request Password reset request
     * @return True if reset initiated
     */
    @Transactional
    public boolean initiatePasswordReset(PasswordResetRequest request) {
        User user = userRepository.findByEmail(request.getEmail());

        if (user == null) {
            log.warn("Password reset requested for non-existent user: {}", request.getEmail());
            return false;
        }

        // Create reset token
        ResetPasswordToken resetToken = tokenService.createPasswordResetToken(user);

        // Send reset email
        emailService.sendPasswordResetEmail(user, resetToken.getToken());

        log.info("Password reset initiated for user: {}", user.getEmail());
        return true;
    }

    /**
     * Confirms a password reset.
     *
     * @param request Password reset confirmation request
     * @return True if reset successful
     * @throws TokenException if token is invalid
     */
    @Transactional
    public boolean confirmPasswordReset(PasswordResetConfirmRequest request) {
        ResetPasswordToken resetToken = tokenService.validatePasswordResetToken(request.getToken());
        User user = resetToken.getUser();

        // Update password
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setLastPasswordChangeAt(LocalDateTime.now());
        userRepository.save(user);

        // Delete used token
        tokenService.deletePasswordResetToken(resetToken);

        log.info("Password reset confirmed for user: {}", user.getEmail());
        return true;
    }

    /**
     * Sets a password for a user with a temporary token.
     *
     * @param token Token
     * @param newPassword New password
     * @return True if password set
     * @throws TokenException if token is invalid
     */
    @Transactional
    public boolean setPassword(String token, String newPassword) {
        String email = tokenService.getEmailByTempToken(token);

        if (email == null) {
            log.warn("Invalid or expired temporary token: {}", token);
            throw new TokenException("Invalid or expired token");
        }

        User user = userRepository.findByEmail(email);
        if (user == null) {
            log.warn("User not found for temporary token: {}", token);
            throw new ResourceNotFoundException("User not found");
        }

        // Update user record
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setEnabled(true);
        user.setEmailVerified(true);
        user.setLastPasswordChangeAt(LocalDateTime.now());
        userRepository.save(user);

        // Remove the temporary token
        tokenService.removeTempToken(token);

        log.info("Password set successfully for user: {}", user.getEmail());
        return true;
    }

    /**
     * Maps a User entity to a UserResponse DTO.
     *
     * @param user User entity
     * @return User response
     */
    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .roles(user.getRoles().stream()
                        .map(role -> role.getName())
                        .collect(Collectors.toList()))
                .enabled(user.isEnabled())
                .emailVerified(user.isEmailVerified())
                .phoneNumber(user.getPhoneNumber())
                .phoneVerified(user.isPhoneVerified())
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();
    }
}

