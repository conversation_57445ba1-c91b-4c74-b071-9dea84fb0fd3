package com.pressmeet247.auth.service;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

/**
 * Service for handling OAuth2 user authentication.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OAuth2UserServiceImpl implements OAuth2UserService<OAuth2UserRequest, OAuth2User> {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        OAuth2UserService<OAuth2UserRequest, OAuth2User> delegate = new DefaultOAuth2UserService();
        OAuth2User oAuth2User = delegate.loadUser(userRequest);

        String email = oAuth2User.getAttribute("email");
        if (email == null) {
            throw new OAuth2AuthenticationException("Email not found from OAuth2 provider");
        }

        User user = userRepository.findByEmail(email);
        if (user == null) {
            // Register new OAuth2 user
            user = new User();
            user.setFirstName(oAuth2User.getAttribute("given_name"));
            user.setLastName(oAuth2User.getAttribute("family_name"));
            user.setEmail(email);
            user.setPassword(""); // Empty password for OAuth2 users
            user.setEnabled(true);
            user.setEmailVerified(true);
            user.setCreatedAt(LocalDateTime.now());
            
            // Assign USER role
            Optional<Role> userRole = roleRepository.findByName("USER");
            if (userRole.isPresent()) {
                user.setRoles(Collections.singletonList(userRole.get()));
            } else {
                log.warn("USER role not found when registering OAuth2 user");
            }
            
            userRepository.save(user);
            log.info("Registered new OAuth2 user: {}", email);
        } else if (!user.isEnabled()) {
            // Enable existing user if not already enabled
            user.setEnabled(true);
            user.setEmailVerified(true);
            userRepository.save(user);
            log.info("Enabled existing OAuth2 user: {}", email);
        }

        return oAuth2User;
    }
}

