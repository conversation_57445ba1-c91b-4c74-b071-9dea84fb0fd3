package com.pressmeet247.auth.entity;

/**
 * Enum representing types of security events.
 */
public enum SecurityEventType {
    /**
     * User account was created.
     */
    ACCOUNT_CREATED,
    
    /**
     * User account was verified.
     */
    ACCOUNT_VERIFIED,
    
    /**
     * User account was locked due to too many failed login attempts.
     */
    ACCOUNT_LOCKED,
    
    /**
     * User account was unlocked.
     */
    ACCOUNT_UNLOCKED,
    
    /**
     * User password was changed.
     */
    PASSWORD_CHANGED,
    
    /**
     * User password was reset.
     */
    PASSWORD_RESET,
    
    /**
     * User email was changed.
     */
    EMAIL_CHANGED,
    
    /**
     * User phone number was changed.
     */
    PHONE_CHANGED,
    
    /**
     * User two-factor authentication was enabled.
     */
    TWO_FACTOR_ENABLED,
    
    /**
     * User two-factor authentication was disabled.
     */
    TWO_FACTOR_DISABLED,
    
    /**
     * User role was changed.
     */
    ROLE_CHANGED,
    
    /**
     * User account was deleted.
     */
    ACCOUNT_DELETED,
    
    /**
     * Suspicious activity was detected on the user account.
     */
    SUSPICIOUS_ACTIVITY,
    
    /**
     * User logged in from a new device or location.
     */
    NEW_DEVICE_LOGIN,
    
    /**
     * User profile was updated.
     */
    PROFILE_UPDATED,
    
    /**
     * User account was disabled.
     */
    ACCOUNT_DISABLED,
    
    /**
     * User account was enabled.
     */
    ACCOUNT_ENABLED,
    
    /**
     * Other security event.
     */
    OTHER
}

