package com.pressmeet247.auth.entity;

import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.auth.token.OauthToken;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * Entity representing a user in the system.
 * Implements UserDetails for Spring Security integration.
 */
@Entity
@Table(
    name = "users",
    indexes = {
        @Index(name = "idx_user_email", columnList = "email", unique = true),
        @Index(name = "idx_user_created_at", columnList = "created_at"),
        @Index(name = "idx_user_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE users SET deleted = true WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a user in the system")
public class User extends BaseEntity implements UserDetails {

    @Column(length = 50, nullable = false)
    @Schema(description = "User's first name", example = "John")
    private String firstName;

    @Column(length = 50, nullable = false)
    @Schema(description = "User's last name", example = "Doe")
    private String lastName;

    @Column(length = 100, nullable = false, unique = true)
    @Schema(description = "User's email address", example = "<EMAIL>")
    private String email;

    @Column(length = 100, nullable = false)
    @Schema(description = "User's password (hashed)", example = "$2a$10$...")
    private String password;

    @Column(nullable = false)
    @Schema(description = "Indicates if the user account is enabled", example = "true")
    private boolean enabled = false;

    @Column(nullable = false)
    @Schema(description = "Indicates if the user's email is verified", example = "true")
    private boolean emailVerified = false;

    @Column(name = "last_login_at")
    @Schema(description = "Last login timestamp")
    private LocalDateTime lastLoginAt;

    @Column(name = "last_password_change_at")
    @Schema(description = "Last password change timestamp")
    private LocalDateTime lastPasswordChangeAt;

    @Column(name = "failed_login_attempts", nullable = false)
    @Schema(description = "Failed login attempts count")
    private int failedLoginAttempts = 0;

    @Column(name = "locked_until")
    @Schema(description = "Account lock timestamp (if locked)")
    private LocalDateTime lockedUntil;

    @Column(name = "profile_image_url", length = 255)
    @Schema(description = "URL to user's profile image")
    private String profileImageUrl;

    @Column(name = "phone_number", length = 20)
    @Schema(description = "User's phone number")
    private String phoneNumber;

    @Column(name = "phone_verified", nullable = false)
    @Schema(description = "Indicates if the user's phone is verified")
    private boolean phoneVerified = false;

    @Column(name = "two_factor_enabled", nullable = false)
    @Schema(description = "Indicates if two-factor authentication is enabled")
    private boolean twoFactorEnabled = false;

    @Column(name = "two_factor_secret", length = 100)
    @Schema(description = "Secret key for two-factor authentication")
    private String twoFactorSecret;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", length = 20)
    @Schema(description = "Type/category of user for targeting and grouping", example = "STUDENT")
    private UserType userType = UserType.INDIVIDUAL;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id"),
        indexes = {
            @Index(name = "idx_user_roles_user_id", columnList = "user_id"),
            @Index(name = "idx_user_roles_role_id", columnList = "role_id")
        }
    )
    private List<Role> roles = new ArrayList<>();

    // Relationships
    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "Account verification details associated with the user")
    private AccountVerification accountVerification;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "List of reset password tokens associated with the user")
    private List<ResetPasswordToken> resetPasswordTokens = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "List of OAuth tokens associated with the user")
    private List<OauthToken> oauthTokens = new ArrayList<>();

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "User preferences")
    private UserPreferences preferences;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "List of user's login history")
    private List<LoginHistory> loginHistory = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Schema(description = "List of user's security events")
    private List<SecurityEvent> securityEvents = new ArrayList<>();

    // No need for @PrePersist as it's handled by BaseEntity

    // UserDetails interface methods
    @Override
    @Schema(hidden = true)
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roles.stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getName()))
                .collect(Collectors.toList());
    }

    @Override
    @Schema(description = "User's email address as username", example = "<EMAIL>")
    public String getUsername() {
        return email;
    }

    @Override
    @Schema(hidden = true)
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    @Schema(hidden = true)
    public boolean isAccountNonLocked() {
        return lockedUntil == null || LocalDateTime.now().isAfter(lockedUntil);
    }

    @Override
    @Schema(hidden = true)
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return enabled && isAccountNonLocked();
    }

    /**
     * Records a login attempt.
     *
     * @param successful Whether the login attempt was successful
     * @param ipAddress  IP address of the login attempt
     * @param userAgent  User agent of the login attempt
     */
    public void recordLoginAttempt(boolean successful, String ipAddress, String userAgent) {
        LoginHistory history = new LoginHistory();
        history.setUser(this);
        history.setSuccessful(successful);
        history.setIpAddress(ipAddress);
        history.setUserAgent(userAgent);
        history.setAttemptTime(LocalDateTime.now());

        this.loginHistory.add(history);

        if (successful) {
            this.lastLoginAt = LocalDateTime.now();
            this.failedLoginAttempts = 0;
        } else {
            this.failedLoginAttempts++;
        }
    }

    /**
     * Locks the user account.
     *
     * @param minutes Number of minutes to lock the account for
     */
    public void lockAccount(int minutes) {
        this.lockedUntil = LocalDateTime.now().plusMinutes(minutes);

        SecurityEvent event = new SecurityEvent();
        event.setUser(this);
        event.setEventType(SecurityEventType.ACCOUNT_LOCKED);
        event.setDescription("Account locked for " + minutes + " minutes due to too many failed login attempts");

        this.securityEvents.add(event);
    }

    /**
     * Unlocks the user account.
     */
    public void unlockAccount() {
        this.lockedUntil = null;
        this.failedLoginAttempts = 0;

        SecurityEvent event = new SecurityEvent();
        event.setUser(this);
        event.setEventType(SecurityEventType.ACCOUNT_UNLOCKED);
        event.setDescription("Account unlocked");

        this.securityEvents.add(event);
    }

    /**
     * Changes the user's password.
     *
     * @param newPassword New password (already hashed)
     */
    public void changePassword(String newPassword) {
        this.password = newPassword;
        this.lastPasswordChangeAt = LocalDateTime.now();

        SecurityEvent event = new SecurityEvent();
        event.setUser(this);
        event.setEventType(SecurityEventType.PASSWORD_CHANGED);
        event.setDescription("Password changed");

        this.securityEvents.add(event);
    }
}



