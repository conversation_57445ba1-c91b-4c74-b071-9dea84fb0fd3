package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a role in the system.
 * Used for role-based access control.
 */
@Entity
@Table(
    name = "roles",
    indexes = {
        @Index(name = "idx_role_name", columnList = "name", unique = true),
        @Index(name = "idx_role_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE roles SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a role in the system")
public class Role extends BaseEntity {

    @Column(length = 50, nullable = false, unique = true)
    @Schema(description = "Role name", example = "ADMIN")
    private String name;

    @Column(length = 255)
    @Schema(description = "Role description", example = "Administrator role with full access")
    private String description;

    @ManyToMany(mappedBy = "roles")
    private List<User> users = new ArrayList<>();

    @Column(name = "is_default", nullable = false)
    @Schema(description = "Whether this role is assigned to new users by default", example = "false")
    private boolean isDefault = false;

    @Column(name = "priority", nullable = false)
    @Schema(description = "Priority of the role (lower number means higher priority)", example = "1")
    private int priority = 100;
}



