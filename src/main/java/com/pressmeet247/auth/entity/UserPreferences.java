package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Entity representing user preferences.
 */
@Entity
@Table(
    name = "user_preferences",
    indexes = {
        @Index(name = "idx_user_preferences_user_id", columnList = "user_id", unique = true),
        @Index(name = "idx_user_preferences_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE user_preferences SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing user preferences")
public class UserPreferences extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "language", length = 10)
    @Schema(description = "User's preferred language", example = "en-US")
    private String language = "en-US";

    @Column(name = "timezone", length = 50)
    @Schema(description = "User's preferred timezone", example = "America/New_York")
    private String timezone = "UTC";

    @Column(name = "date_format", length = 20)
    @Schema(description = "User's preferred date format", example = "MM/dd/yyyy")
    private String dateFormat = "MM/dd/yyyy";

    @Column(name = "time_format", length = 20)
    @Schema(description = "User's preferred time format", example = "HH:mm:ss")
    private String timeFormat = "HH:mm:ss";

    @Column(name = "theme", length = 20)
    @Schema(description = "User's preferred theme", example = "light")
    private String theme = "light";

    @Column(name = "notifications_enabled")
    @Schema(description = "Whether notifications are enabled", example = "true")
    private boolean notificationsEnabled = true;

    @Column(name = "email_notifications")
    @Schema(description = "Whether email notifications are enabled", example = "true")
    private boolean emailNotifications = true;

    @Column(name = "sms_notifications")
    @Schema(description = "Whether SMS notifications are enabled", example = "false")
    private boolean smsNotifications = false;

    @Column(name = "push_notifications")
    @Schema(description = "Whether push notifications are enabled", example = "true")
    private boolean pushNotifications = true;
}



