package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing a password reset token.
 * Used for password reset functionality.
 */
@Entity
@Table(
    name = "reset_password_tokens",
    indexes = {
        @Index(name = "idx_reset_password_token_token", columnList = "token", unique = true),
        @Index(name = "idx_reset_password_token_user_id", columnList = "user_id"),
        @Index(name = "idx_reset_password_token_expiration_time", columnList = "expiration_time"),
        @Index(name = "idx_reset_password_token_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE reset_password_tokens SET deleted = true WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a password reset token")
public class ResetPasswordToken extends BaseEntity {

    @Column(name = "token", length = 100, nullable = false, unique = true)
    @Schema(description = "Token string", example = "7c9e6679-7425-40de-944b-e07fc1f90ae7")
    private String token;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @Schema(description = "User associated with this reset token")
    private User user;

    @Column(name = "expiration_time", nullable = false)
    @Schema(description = "Token expiration timestamp")
    private LocalDateTime expirationTime;

    @Column(name = "used_at")
    @Schema(description = "When the token was used for password reset")
    private LocalDateTime usedAt;

    @PrePersist
    protected void onCreate() {
        if (expirationTime == null) {
            expirationTime = LocalDateTime.now().plusHours(24); // Token valid for 24 hours
        }
    }

    /**
     * Checks if the token is expired.
     *
     * @return true if the token is expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expirationTime);
    }

    /**
     * Marks the token as used.
     */
    public void markAsUsed() {
        this.usedAt = LocalDateTime.now();
    }

    /**
     * Checks if the token has been used.
     *
     * @return true if the token has been used, false otherwise
     */
    public boolean isUsed() {
        return usedAt != null;
    }
}

