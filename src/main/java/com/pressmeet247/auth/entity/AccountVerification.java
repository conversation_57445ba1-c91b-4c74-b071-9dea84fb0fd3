package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing an account verification token.
 * Used for email verification during registration.
 */
@Entity
@Table(
    name = "account_verifications",
    indexes = {
        @Index(name = "idx_account_verification_user_id", columnList = "user_id", unique = true),
        @Index(name = "idx_account_verification_token", columnList = "token", unique = true),
        @Index(name = "idx_account_verification_expires_at", columnList = "expires_at"),
        @Index(name = "idx_account_verification_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE account_verifications SET deleted = true WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing an account verification token")
public class AccountVerification extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    @Schema(description = "User associated with this verification token")
    private User user;

    @Column(name = "token", length = 100, nullable = false, unique = true)
    @Schema(description = "Verification token", example = "7c9e6679-7425-40de-944b-e07fc1f90ae7")
    private String token;

    @Column(name = "otp_code", length = 6, nullable = true)
    @Schema(description = "6-digit OTP verification code", example = "123456")
    private String otpCode;

    @Column(name = "expires_at", nullable = false)
    @Schema(description = "Token expiration timestamp")
    private LocalDateTime expiresAt;

    @Column(name = "verified_at")
    @Schema(description = "When the token was used for verification")
    private LocalDateTime verifiedAt;

    @PrePersist
    protected void onCreate() {
        if (expiresAt == null) {
            expiresAt = LocalDateTime.now().plusHours(24); // Token valid for 24 hours
        }
    }

    /**
     * Checks if the token is expired.
     *
     * @return true if the token is expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * Marks the token as verified.
     */
    public void markAsVerified() {
        this.verifiedAt = LocalDateTime.now();
    }


}

