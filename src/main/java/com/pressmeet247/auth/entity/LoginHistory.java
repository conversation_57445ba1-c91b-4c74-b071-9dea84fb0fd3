package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing a user's login history.
 */
@Entity
@Table(
    name = "login_history",
    indexes = {
        @Index(name = "idx_login_history_user_id", columnList = "user_id"),
        @Index(name = "idx_login_history_attempt_time", columnList = "attempt_time"),
        @Index(name = "idx_login_history_successful", columnList = "successful"),
        @Index(name = "idx_login_history_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE login_history SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a user's login history")
public class LoginHistory extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "attempt_time", nullable = false)
    @Schema(description = "Time of the login attempt")
    private LocalDateTime attemptTime;

    @Column(name = "successful", nullable = false)
    @Schema(description = "Whether the login attempt was successful", example = "true")
    private boolean successful;

    @Column(name = "ip_address", length = 45)
    @Schema(description = "IP address of the login attempt", example = "***********")
    private String ipAddress;

    @Column(name = "user_agent", length = 255)
    @Schema(description = "User agent of the login attempt")
    private String userAgent;

    @Column(name = "device_type", length = 50)
    @Schema(description = "Device type of the login attempt", example = "Desktop")
    private String deviceType;

    @Column(name = "location", length = 100)
    @Schema(description = "Location of the login attempt", example = "New York, USA")
    private String location;

    @Column(name = "failure_reason", length = 100)
    @Schema(description = "Reason for login failure, if applicable", example = "Invalid password")
    private String failureReason;
}

