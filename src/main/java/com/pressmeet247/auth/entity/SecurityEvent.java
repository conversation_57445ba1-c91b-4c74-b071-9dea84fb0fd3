package com.pressmeet247.auth.entity;

import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Entity representing a security event related to a user.
 */
@Entity
@Table(
    name = "security_events",
    indexes = {
        @Index(name = "idx_security_events_user_id", columnList = "user_id"),
        @Index(name = "idx_security_events_event_type", columnList = "event_type"),
        @Index(name = "idx_security_events_created_at", columnList = "created_at"),
        @Index(name = "idx_security_events_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE security_events SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a security event related to a user")
public class SecurityEvent extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false, length = 50)
    @Schema(description = "Type of security event", example = "PASSWORD_CHANGED")
    private SecurityEventType eventType;

    @Column(name = "description", length = 255)
    @Schema(description = "Description of the security event", example = "Password changed successfully")
    private String description;

    @Column(name = "ip_address", length = 45)
    @Schema(description = "IP address associated with the event", example = "***********")
    private String ipAddress;

    @Column(name = "user_agent", length = 255)
    @Schema(description = "User agent associated with the event")
    private String userAgent;

    @Column(name = "device_type", length = 50)
    @Schema(description = "Device type associated with the event", example = "Desktop")
    private String deviceType;

    @Column(name = "location", length = 100)
    @Schema(description = "Location associated with the event", example = "New York, USA")
    private String location;

    @Column(name = "additional_data", columnDefinition = "TEXT")
    @Schema(description = "Additional data related to the event in JSON format")
    private String additionalData;
}

