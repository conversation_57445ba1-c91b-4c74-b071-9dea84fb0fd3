package com.pressmeet247.auth.enums;

/**
 * Enum representing different types of users for better categorization and targeting.
 */
public enum UserType {
    STUDENT("Student"),
    COMPANY("Company"),
    COLLEGE("College"),
    INDIVIDUAL("Individual"),
    ORGANIZATION("Organization"),
    PREMIUM("Premium User"),
    BASIC("Basic User");

    private final String displayName;

    UserType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
