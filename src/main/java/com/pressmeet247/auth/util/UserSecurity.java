package com.pressmeet247.auth.util;

import com.pressmeet247.auth.entity.User;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * Utility class for user security checks.
 */
@Component("userSecurity")
public class UserSecurity {

    /**
     * Checks if the current user is the user with the given ID.
     *
     * @param userId User ID to check
     * @return True if current user has the given ID
     */
    public boolean isCurrentUser(Long userId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof User)) {
            return false;
        }
        
        User user = (User) principal;
        return user.getId().equals(userId);
    }
}

