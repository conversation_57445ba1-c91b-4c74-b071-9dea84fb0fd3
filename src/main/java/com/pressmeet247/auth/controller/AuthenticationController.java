package com.pressmeet247.auth.controller;

import com.pressmeet247.auth.dto.request.EmailRequest;
import com.pressmeet247.auth.dto.request.LoginRequest;
import com.pressmeet247.auth.dto.request.OtpVerificationRequest;
import com.pressmeet247.auth.dto.request.RefreshTokenRequest;
import com.pressmeet247.auth.dto.request.SignUpRequest;
import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.dto.response.AuthResponse;
import com.pressmeet247.auth.dto.response.UserResponse;
import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.exception.AuthenticationException;
import com.pressmeet247.auth.exception.TokenException;
import com.pressmeet247.auth.exception.UserAlreadyExistsException;
import com.pressmeet247.auth.service.UserService;
import com.pressmeet247.auth.repository.AccountVerificationRepository;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.auth.service.AuthService;
import com.pressmeet247.common.util.CookieUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Controller for authentication endpoints with v1 API path.
 * This controller provides compatibility with the frontend that uses /api/v1/authentication/* paths.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/authentication")
@RequiredArgsConstructor
@Tag(name = "Authentication V1", description = "Authentication API V1")
public class AuthenticationController {

    private final AuthService authService;
    private final AccountVerificationRepository accountVerificationRepository;
    private final UserRepository userRepository;
    private final UserService userService;
    private final CookieUtils cookieUtils;

    @Value("${app.frontend.login-url}")
    private String frontendLoginUrl;

    /**
     * Registers a new user.
     *
     * @param request Registration request
     * @return Response entity
     */
    @PostMapping("/register")
    @Operation(summary = "Register a new user", description = "Registers a new user and sends a verification email")
    public ResponseEntity<ApiResponse<Void>> register(@Valid @RequestBody SignUpRequest request) {
        try {
            authService.registerUser(request);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("User registered successfully. Please check your email for verification."));
        } catch (UserAlreadyExistsException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Authenticates a user.
     *
     * @param request Login request
     * @param httpRequest HTTP request
     * @return Response entity
     */
    @PostMapping("/login")
    @Operation(summary = "Login", description = "Authenticates a user and returns JWT tokens")
    public ResponseEntity<ApiResponse<AuthResponse>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            // Check if user exists and is verified before attempting login
            User user = userRepository.findByEmail(request.getEmail());
            if (user != null && !user.isEmailVerified()) {
                // Resend verification email if not verified
                authService.resendVerificationEmail(request.getEmail());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("Please verify your email before logging in. A new verification email has been sent."));
            }

            AuthResponse authResponse = authService.login(request, httpRequest);

            // Add JWT token to cookie
            cookieUtils.addJwtCookie(httpResponse, authResponse.getAccessToken());

            // Add refresh token to cookie if present
            if (authResponse.getRefreshToken() != null) {
                cookieUtils.addRefreshTokenCookie(httpResponse, authResponse.getRefreshToken());
            }

            return ResponseEntity.ok(ApiResponse.success("Login successful", authResponse));
        } catch (AuthenticationException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error during login: ", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Login failed. Please try again later."));
        }
    }

    /**
     * Verifies a user's account using a token.
     *
     * @param token Verification token
     * @return Response entity
     */
    @GetMapping("/verify")
    @Operation(summary = "Verify account", description = "Verifies a user's account using a token")
    public ResponseEntity<ApiResponse<Boolean>> verifyAccount(@RequestParam String token) {
        try {
            boolean verified = authService.verifyAccount(token);
            return ResponseEntity.ok(ApiResponse.success("Account verified successfully", verified));
        } catch (TokenException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Validates an email verification token.
     * This endpoint is used by the email verification link.
     *
     * @param token Verification token
     * @return Response entity or redirect to login page
     */
    @GetMapping("/validate-email-verification-token")
    @Operation(summary = "Validate email verification token", description = "Validates an email verification token")
    public Object validateEmailVerificationToken(@RequestParam String token, HttpServletRequest request,
                                               HttpServletResponse response) throws IOException {
        try {
            boolean verified = authService.verifyAccount(token);

            log.info("Email verification successful for token: {}", token);

            // Check if this is a browser request (Accept header includes text/html)
            String acceptHeader = request.getHeader("Accept");
            if (acceptHeader != null && acceptHeader.contains("text/html")) {
                // Redirect to login page for browser requests
                String redirectUrl = frontendLoginUrl + "?verified=true";
                log.info("Redirecting to: {}", redirectUrl);
                response.sendRedirect(redirectUrl);
                return null;
            }

            // Return JSON response for API requests
            return ResponseEntity.ok(ApiResponse.success("Email verified successfully", verified));
        } catch (TokenException e) {
            log.error("Email verification failed for token: {}", token, e);

            // Check if this is a browser request
            String acceptHeader = request.getHeader("Accept");
            if (acceptHeader != null && acceptHeader.contains("text/html")) {
                // Redirect to login page with error for browser requests
                String redirectUrl = frontendLoginUrl + "?error=" + URLEncoder.encode(e.getMessage(), StandardCharsets.UTF_8);
                log.info("Redirecting to: {}", redirectUrl);
                response.sendRedirect(redirectUrl);
                return null;
            }

            // Return JSON error for API requests
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during email verification for token: {}", token, e);

            // Check if this is a browser request
            String acceptHeader = request.getHeader("Accept");
            if (acceptHeader != null && acceptHeader.contains("text/html")) {
                // Redirect to login page with error for browser requests
                response.sendRedirect(frontendLoginUrl + "?error=" + URLEncoder.encode("An unexpected error occurred. Please try again.", StandardCharsets.UTF_8));
                return null;
            }

            // Return JSON error for API requests
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Verifies a user's account using an OTP code.
     *
     * @param request OTP verification request containing email and OTP code
     * @return Response entity
     */
    @PostMapping("/verify-otp")
    @Operation(summary = "Verify account with OTP", description = "Verifies a user's account using an OTP code")
    public ResponseEntity<ApiResponse<Boolean>> verifyAccountWithOtp(@RequestBody OtpVerificationRequest request) {
        try {
            log.info("Received OTP verification request: {}", request);

            String email = request.getEmail();
            String otpCode = request.getOtpCode();

            if (email == null || email.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Email is required"));
            }

            if (otpCode == null || otpCode.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("OTP code is required"));
            }

            if (!otpCode.matches("^\\d{6}$")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("OTP code must be 6 digits"));
            }

            boolean verified = authService.verifyAccountWithOtp(email, otpCode);
            log.info("OTP verification successful for email: {}", email);
            return ResponseEntity.ok(ApiResponse.success("Email verified successfully", verified));
        } catch (TokenException e) {
            log.error("OTP verification failed for email: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during OTP verification for email: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Alternative endpoint for validating email verification token with POST/PUT methods.
     *
     * @param token Verification token
     * @return Response entity
     */
    @PostMapping("/validate-email-verification-token")
    @PutMapping("/validate-email-verification-token")
    @Operation(summary = "Validate email verification token (POST/PUT)", description = "Validates an email verification token using POST or PUT method")
    public ResponseEntity<ApiResponse<Boolean>> validateEmailVerificationTokenPost(@RequestParam String token) {
        try {
            boolean verified = authService.verifyAccount(token);
            return ResponseEntity.ok(ApiResponse.success("Email verified successfully", verified));
        } catch (TokenException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Sends a new email verification token to the user.
     *
     * @param email User email
     * @return Response entity
     */
    @GetMapping("/send-email-verification-token")
    @PostMapping("/send-email-verification-token")
    @Operation(summary = "Send email verification token", description = "Sends a new email verification token to the user")
    public ResponseEntity<ApiResponse<Void>> sendEmailVerificationToken(@RequestParam String email) {
        try {
            boolean sent = authService.resendVerificationEmail(email);
            if (sent) {
                return ResponseEntity.ok(ApiResponse.success("Verification code sent successfully. Please check your email."));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Could not send verification code. User may not exist or is already verified."));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Error sending verification code: " + e.getMessage()));
        }
    }

    /**
     * Resends OTP verification code to the user.
     *
     * @param request Request containing user email
     * @return Response entity
     */
    @PostMapping("/resend-otp")
    @Operation(summary = "Resend OTP code", description = "Resends OTP verification code to the user")
    public ResponseEntity<ApiResponse<Void>> resendOtp(@RequestBody EmailRequest request) {
        try {
            String email = request.getEmail();
            log.info("Resending OTP for email: {}", email);

            if (email == null || email.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Email is required"));
            }

            boolean sent = authService.resendVerificationEmail(email);
            if (sent) {
                log.info("Successfully resent OTP for email: {}", email);
                return ResponseEntity.ok(ApiResponse.success("New verification code sent successfully. Please check your email."));
            } else {
                log.warn("Failed to resend OTP for email: {}", email);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Could not send verification code. User may not exist or is already verified."));
            }
        } catch (Exception e) {
            log.error("Error resending OTP for email: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Error sending verification code: " + e.getMessage()));
        }
    }

    /**
     * Gets the latest verification token for testing purposes.
     * This endpoint should be removed in production.
     *
     * @param email User email
     * @return Response entity with the token
     */
    @GetMapping("/get-verification-token")
    @Operation(summary = "Get verification token", description = "Gets the latest verification token for testing purposes")
    public ResponseEntity<ApiResponse<String>> getVerificationToken(@RequestParam String email) {
        AccountVerification verification = accountVerificationRepository.findTopByUserEmailOrderByCreatedAtDesc(email)
                .orElse(null);

        if (verification == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("No verification token found for email: " + email));
        }

        return ResponseEntity.ok(ApiResponse.success("Verification token retrieved", verification.getToken()));
    }

    /**
     * Gets the latest OTP code for testing purposes.
     * This endpoint should be removed in production.
     *
     * @param email User email
     * @return Response entity with the OTP code
     */
    @GetMapping("/get-otp-code")
    @Operation(summary = "Get OTP code (GET)", description = "Gets the latest OTP code for testing purposes")
    public ResponseEntity<ApiResponse<String>> getOtpCode(@RequestParam String email) {
        log.info("Getting OTP code for email: {}", email);

        if (email == null || email.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Email is required"));
        }

        AccountVerification verification = accountVerificationRepository.findTopByUserEmailOrderByCreatedAtDesc(email)
                .orElse(null);

        if (verification == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("No verification found for email: " + email));
        }

        String otpCode = verification.getOtpCode();
        log.info("Retrieved OTP code for email {}: {}", email, otpCode);

        return ResponseEntity.ok(ApiResponse.success("OTP code retrieved", otpCode));
    }

    /**
     * Gets the latest OTP code for testing purposes (POST version).
     * This endpoint should be removed in production.
     *
     * @param request Request containing user email
     * @return Response entity with the OTP code
     */
    @PostMapping("/get-otp-code")
    @Operation(summary = "Get OTP code (POST)", description = "Gets the latest OTP code for testing purposes")
    public ResponseEntity<ApiResponse<String>> getOtpCodePost(@RequestBody EmailRequest request) {
        log.info("Getting OTP code for email (POST): {}", request.getEmail());

        if (request.getEmail() == null || request.getEmail().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Email is required"));
        }

        AccountVerification verification = accountVerificationRepository.findTopByUserEmailOrderByCreatedAtDesc(request.getEmail())
                .orElse(null);

        if (verification == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("No verification found for email: " + request.getEmail()));
        }

        String otpCode = verification.getOtpCode();
        log.info("Retrieved OTP code for email {}: {}", request.getEmail(), otpCode);

        return ResponseEntity.ok(ApiResponse.success("OTP code retrieved", otpCode));
    }

    /**
     * Logs out a user.
     *
     * @param user Authenticated user
     * @return Response entity
     */
    @PostMapping("/logout")
    @Operation(summary = "Logout", description = "Logs out a user by invalidating their tokens")
    public ResponseEntity<ApiResponse<Void>> logout(
            @AuthenticationPrincipal User user,
            HttpServletResponse response) {
        authService.logout(user);

        // Clear cookies
        cookieUtils.clearJwtCookie(response);
        cookieUtils.clearRefreshTokenCookie(response);

        return ResponseEntity.ok(ApiResponse.success("Logged out successfully"));
    }

    /**
     * Refreshes an access token.
     *
     * @param request Refresh token request
     * @param httpRequest HTTP request
     * @return Response entity
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "Refresh token", description = "Refreshes an access token using a refresh token")
    public ResponseEntity<ApiResponse<AuthResponse>> refreshToken(
            @Valid @RequestBody(required = false) RefreshTokenRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            log.info("Received token refresh request");

            // Try to get refresh token from cookie if not provided in request body
            String refreshToken = null;
            if (request != null && request.getRefreshToken() != null) {
                refreshToken = request.getRefreshToken();
            } else {
                refreshToken = cookieUtils.getRefreshTokenFromCookies(httpRequest);
            }

            if (refreshToken == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("Refresh token not found"));
            }

            // Create a request object with the refresh token
            RefreshTokenRequest refreshRequest = new RefreshTokenRequest(refreshToken);

            AuthResponse authResponse = authService.refreshToken(refreshRequest, httpRequest);

            // Update cookies with new tokens
            cookieUtils.addJwtCookie(httpResponse, authResponse.getAccessToken());
            if (authResponse.getRefreshToken() != null) {
                cookieUtils.addRefreshTokenCookie(httpResponse, authResponse.getRefreshToken());
            }

            log.info("Token refresh successful");
            return ResponseEntity.ok(ApiResponse.success("Token refreshed successfully", authResponse));
        } catch (TokenException e) {
            log.warn("Token refresh failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during token refresh: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Gets the current user.
     *
     * @param user Authenticated user
     * @return Response entity
     */
    @GetMapping("/users/me")
    @Operation(summary = "Get current user", description = "Returns the currently authenticated user")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentUser(@AuthenticationPrincipal User user) {
        UserResponse userResponse = userService.getUserById(user.getId());
        return ResponseEntity.ok(ApiResponse.success("User retrieved successfully", userResponse));
    }
}
