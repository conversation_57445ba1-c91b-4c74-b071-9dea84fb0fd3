package com.pressmeet247.auth.controller;

import com.pressmeet247.auth.dto.request.LoginRequest;
import com.pressmeet247.auth.dto.request.RefreshTokenRequest;
import com.pressmeet247.auth.dto.request.SignUpRequest;
import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.dto.response.AuthResponse;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.exception.AuthenticationException;
import com.pressmeet247.auth.exception.TokenException;
import com.pressmeet247.auth.exception.UserAlreadyExistsException;
import com.pressmeet247.auth.service.AuthService;
import com.pressmeet247.common.util.CookieUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for authentication endpoints.
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Authentication API")
public class AuthController {

    private final AuthService authService;
    private final CookieUtils cookieUtils;

    /**
     * Registers a new user.
     *
     * @param request Registration request
     * @return Response entity
     */
    @PostMapping("/register")
    @Operation(summary = "Register a new user", description = "Registers a new user and sends a verification email")
    public ResponseEntity<ApiResponse<Void>> register(@Valid @RequestBody SignUpRequest request) {
        try {
            authService.registerUser(request);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("User registered successfully. Please check your email for verification."));
        } catch (UserAlreadyExistsException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Authenticates a user.
     *
     * @param request Login request
     * @param httpRequest HTTP request
     * @return Response entity
     */
    @PostMapping("/login")
    @Operation(summary = "Login", description = "Authenticates a user and returns JWT tokens")
    public ResponseEntity<ApiResponse<AuthResponse>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            AuthResponse authResponse = authService.login(request, httpRequest);

            // Add JWT token to cookie
            cookieUtils.addJwtCookie(httpResponse, authResponse.getAccessToken());

            // Add refresh token to cookie if present
            if (authResponse.getRefreshToken() != null) {
                cookieUtils.addRefreshTokenCookie(httpResponse, authResponse.getRefreshToken());
            }

            return ResponseEntity.ok(ApiResponse.success("Login successful", authResponse));
        } catch (AuthenticationException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error during login: ", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Login failed. Please try again later."));
        }
    }

    /**
     * Refreshes an access token.
     *
     * @param request Refresh token request
     * @param httpRequest HTTP request
     * @return Response entity
     */
    @PostMapping("/refresh")
    @Operation(summary = "Refresh token", description = "Refreshes an access token using a refresh token")
    public ResponseEntity<ApiResponse<AuthResponse>> refreshToken(
            @Valid @RequestBody(required = false) RefreshTokenRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            log.info("Received token refresh request");

            // Try to get refresh token from cookie if not provided in request body
            String refreshToken = null;
            if (request != null && request.getRefreshToken() != null) {
                refreshToken = request.getRefreshToken();
            } else {
                refreshToken = cookieUtils.getRefreshTokenFromCookies(httpRequest);
            }

            if (refreshToken == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("Refresh token not found"));
            }

            // Create a request object with the refresh token
            RefreshTokenRequest refreshRequest = new RefreshTokenRequest(refreshToken);

            AuthResponse authResponse = authService.refreshToken(refreshRequest, httpRequest);

            // Update cookies with new tokens
            cookieUtils.addJwtCookie(httpResponse, authResponse.getAccessToken());
            if (authResponse.getRefreshToken() != null) {
                cookieUtils.addRefreshTokenCookie(httpResponse, authResponse.getRefreshToken());
            }

            log.info("Token refresh successful");
            return ResponseEntity.ok(ApiResponse.success("Token refreshed successfully", authResponse));
        } catch (TokenException e) {
            log.warn("Token refresh failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during token refresh: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred. Please try again."));
        }
    }

    /**
     * Logs out a user.
     *
     * @param user Authenticated user
     * @return Response entity
     */
    @PostMapping("/logout")
    @Operation(summary = "Logout", description = "Logs out a user by invalidating their tokens")
    public ResponseEntity<ApiResponse<Void>> logout(
            @AuthenticationPrincipal User user,
            HttpServletResponse response) {
        authService.logout(user);

        // Clear cookies
        cookieUtils.clearJwtCookie(response);
        cookieUtils.clearRefreshTokenCookie(response);

        return ResponseEntity.ok(ApiResponse.success("Logged out successfully"));
    }
}
