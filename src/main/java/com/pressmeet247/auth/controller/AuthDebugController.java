package com.pressmeet247.auth.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.auth.entity.User;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for authentication debugging endpoints.
 */
@Slf4j
@RestController
@RequestMapping("/api/auth/debug")
@RequiredArgsConstructor
@Tag(name = "Auth Debug", description = "Authentication debugging API")
public class AuthDebugController {

    /**
     * Gets authentication status and details.
     *
     * @param request HTTP request
     * @return Response entity
     */
    @GetMapping("/status")
    @Operation(summary = "Get authentication status", description = "Returns authentication status and details")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAuthStatus(HttpServletRequest request) {
        Map<String, Object> status = new HashMap<>();
        
        // Get authentication from security context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null) {
            status.put("authenticated", authentication.isAuthenticated());
            status.put("principal", authentication.getPrincipal().toString());
            status.put("name", authentication.getName());
            status.put("authorities", authentication.getAuthorities().toString());
            
            if (authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                status.put("userId", user.getId());
                status.put("email", user.getEmail());
                status.put("roles", user.getRoles().stream()
                        .map(role -> role.getName())
                        .collect(Collectors.toList()));
            }
        } else {
            status.put("authenticated", false);
            status.put("message", "No authentication found in security context");
        }
        
        // Get request headers
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        status.put("headers", headers);
        
        // Get cookies
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            Map<String, String> cookieMap = Arrays.stream(cookies)
                    .collect(Collectors.toMap(
                            Cookie::getName,
                            cookie -> cookie.getValue() != null ? "[present]" : "[empty]"
                    ));
            status.put("cookies", cookieMap);
        } else {
            status.put("cookies", "No cookies found");
        }
        
        return ResponseEntity.ok(ApiResponse.success(
                "Authentication status retrieved successfully",
                status
        ));
    }
}
