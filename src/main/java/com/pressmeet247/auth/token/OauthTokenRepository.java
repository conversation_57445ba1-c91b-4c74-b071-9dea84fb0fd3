package com.pressmeet247.auth.token;

import com.pressmeet247.auth.token.OauthToken;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for OauthToken entity operations.
 */
@Repository
public interface OauthTokenRepository extends JpaRepository<OauthToken, Long> {

    /**
     * Find token by refresh token.
     *
     * @param refreshToken Refresh token string
     * @return OauthToken if found
     */
    OauthToken findByRefreshToken(String refreshToken);

    /**
     * Find token by refresh token as Optional.
     *
     * @param refreshToken Refresh token string
     * @return Optional containing the token if found
     */
    Optional<OauthToken> findOptionalByRefreshToken(String refreshToken);

    /**
     * Delete all tokens for a user.
     *
     * @param user User entity
     */
    void deleteByUser(User user);

    /**
     * Find all tokens for a user.
     *
     * @param user User entity
     * @return List of tokens
     */
    List<OauthToken> findByUser(User user);

    /**
     * Find expired tokens.
     *
     * @param now Current timestamp
     * @return List of expired tokens
     */
    @Query("SELECT ot FROM OauthToken ot WHERE ot.expirationTime < :now")
    List<OauthToken> findExpiredTokens(LocalDateTime now);
}


