package com.pressmeet247.auth.token;

import com.pressmeet247.common.jwt.JwtProperties;
import com.pressmeet247.common.jwt.JwtUtils;
import com.pressmeet247.auth.dto.response.AuthResponse;
import com.pressmeet247.auth.entity.AccountVerification;
import com.pressmeet247.auth.entity.ResetPasswordToken;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.exception.TokenException;
import com.pressmeet247.auth.repository.AccountVerificationRepository;
import com.pressmeet247.auth.repository.ResetPasswordTokenRepository;
import com.pressmeet247.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Service for token management.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TokenService {

    private static final String TEMP_TOKEN_PREFIX = "temp_token:";

    private final JwtUtils jwtUtils;
    private final JwtProperties jwtProperties;
    private final UserRepository userRepository;
    private final OauthTokenRepository oauthTokenRepository;
    private final AccountVerificationRepository accountVerificationRepository;
    private final ResetPasswordTokenRepository resetPasswordTokenRepository;
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * Generates a JWT token for a user.
     *
     * @param user User
     * @return JWT token
     */
    public String generateJwtToken(User user) {
        return jwtUtils.generateJwtToken(user);
    }

    /**
     * Generates a refresh token.
     *
     * @return Refresh token
     */
    public String generateRefreshToken() {
        return UUID.randomUUID().toString();
    }

    /**
     * Generates an authentication response with tokens.
     *
     * @param user User
     * @return Authentication response
     */
    public AuthResponse generateTokenResponse(User user) {
        return generateTokenResponse(user, null, null);
    }

    /**
     * Generates an authentication response with tokens.
     *
     * @param user User
     * @param ipAddress IP address
     * @param userAgent User agent
     * @return Authentication response
     */
    @Transactional
    public AuthResponse generateTokenResponse(User user, String ipAddress, String userAgent) {
        // Generate unique tokens
        String accessToken = generateJwtToken(user);
        String refreshToken = generateRefreshToken();
        LocalDateTime expirationTime = LocalDateTime.now().plus(Duration.ofMillis(jwtProperties.getExpiration()));

        try {
            // Save tokens
            OauthToken oauthToken = new OauthToken();
            oauthToken.setAccessToken(accessToken);
            oauthToken.setRefreshToken(refreshToken);
            oauthToken.setExpirationTime(expirationTime);
            oauthToken.setUser(user);
            oauthToken.setIpAddress(ipAddress);
            oauthToken.setUserAgent(userAgent);

            oauthTokenRepository.save(oauthToken);
        } catch (Exception e) {
            log.warn("Error saving token, generating new one: {}", e.getMessage());
            // If there's a duplicate, try again with a new token
            accessToken = generateJwtToken(user);
            refreshToken = generateRefreshToken();

            OauthToken oauthToken = new OauthToken();
            oauthToken.setAccessToken(accessToken);
            oauthToken.setRefreshToken(refreshToken);
            oauthToken.setExpirationTime(expirationTime);
            oauthToken.setUser(user);
            oauthToken.setIpAddress(ipAddress);
            oauthToken.setUserAgent(userAgent);

            oauthTokenRepository.save(oauthToken);
        }

        // Build response
        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expirationTime(expirationTime)
                .tokenType("Bearer")
                .userId(user.getId())
                .email(user.getEmail())
                .roles(user.getRoles().stream()
                        .map(role -> role.getName())
                        .reduce((a, b) -> a + "," + b)
                        .orElse(""))
                .build();
    }

    /**
     * Refreshes an access token.
     *
     * @param refreshToken Refresh token
     * @param ipAddress IP address
     * @param userAgent User agent
     * @return Authentication response
     * @throws TokenException if token is invalid
     */
    @Transactional
    public AuthResponse refreshAccessToken(String refreshToken, String ipAddress, String userAgent) {
        try {
            OauthToken oauthToken = oauthTokenRepository.findByRefreshToken(refreshToken);

            if (oauthToken == null || oauthToken.isExpired()) {
                log.warn("Invalid or expired refresh token: {}", refreshToken);
                throw new TokenException("Invalid or expired refresh token");
            }

            User user = oauthToken.getUser();

            // Mark the old token as revoked instead of deleting it
            oauthToken.revoke();
            oauthTokenRepository.save(oauthToken);
            log.info("Revoked old refresh token: {}", refreshToken);

            // Generate new tokens
            AuthResponse response = generateTokenResponse(user, ipAddress, userAgent);
            log.info("Generated new tokens for user: {}", user.getEmail());
            return response;
        } catch (TokenException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error refreshing token: {}", e.getMessage(), e);
            throw new TokenException("Error refreshing token: " + e.getMessage());
        }
    }

    /**
     * Revokes all tokens for a user.
     *
     * @param user User
     */
    @Transactional
    public void revokeAllUserTokens(User user) {
        List<OauthToken> validTokens = oauthTokenRepository.findByUser(user);
        if (!validTokens.isEmpty()) {
            for (OauthToken token : validTokens) {
                token.revoke();
            }
            oauthTokenRepository.saveAll(validTokens);
            log.info("Revoked {} tokens for user: {}", validTokens.size(), user.getEmail());
        }
    }

    /**
     * Stores a temporary token.
     *
     * @param token Token
     * @param email Email
     */
    public void storeTempToken(String token, String email) {
        redisTemplate.opsForValue().set(TEMP_TOKEN_PREFIX + token, email, 15, TimeUnit.MINUTES);
        log.info("Stored temporary token for email: {}", email);
    }

    /**
     * Gets an email by temporary token.
     *
     * @param token Token
     * @return Email
     */
    public String getEmailByTempToken(String token) {
        return redisTemplate.opsForValue().get(TEMP_TOKEN_PREFIX + token);
    }

    /**
     * Removes a temporary token.
     *
     * @param token Token
     */
    public void removeTempToken(String token) {
        redisTemplate.delete(TEMP_TOKEN_PREFIX + token);
    }

    /**
     * Creates a verification token for a user.
     *
     * @param user User
     * @return Verification token
     */
    @Transactional
    public AccountVerification createVerificationToken(User user) {
        // Check if user already has a verification token
        AccountVerification verification = accountVerificationRepository.findByUser(user);

        if (verification == null) {
            verification = new AccountVerification();
            verification.setUser(user);
        }

        verification.setToken(UUID.randomUUID().toString());

        // Generate a 6-digit OTP code
        String otpCode = generateOtpCode();
        verification.setOtpCode(otpCode);

        // Set expiration time to 15 minutes for OTP
        verification.setExpiresAt(LocalDateTime.now().plusMinutes(15));

        log.info("Generated OTP code for user {}: {}", user.getEmail(), otpCode);
        return accountVerificationRepository.save(verification);
    }

    /**
     * Generates a random 6-digit OTP code.
     *
     * @return 6-digit OTP code
     */
    private String generateOtpCode() {
        // Generate a random number between 100000 and 999999
        int code = 100000 + (int)(Math.random() * 900000);
        String otpCode = String.valueOf(code);
        log.info("Generated 6-digit OTP code: {}", otpCode);
        return otpCode;
    }

    /**
     * Validates a verification token.
     *
     * @param token Token
     * @return Verification
     * @throws TokenException if token is invalid
     */
    @Transactional(readOnly = true)
    public AccountVerification validateVerificationToken(String token) {
        AccountVerification verification = accountVerificationRepository.findByToken(token);

        if (verification == null) {
            log.warn("Invalid verification token: {}", token);
            throw new TokenException("Invalid verification token");
        }

        if (verification.isExpired()) {
            log.warn("Expired verification token: {}", token);
            throw new TokenException("Verification token has expired");
        }

        return verification;
    }

    /**
     * Validates an OTP code for a user.
     *
     * @param email User's email
     * @param otpCode OTP code to validate
     * @return Verification if valid
     * @throws TokenException if OTP is invalid
     */
    @Transactional(readOnly = true)
    public AccountVerification validateOtpCode(String email, String otpCode) {
        log.info("Validating OTP code for email: {}, OTP: {}", email, otpCode);

        User user = userRepository.findByEmail(email);
        if (user == null) {
            log.warn("User not found for email: {}", email);
            throw new TokenException("Invalid email address");
        }

        AccountVerification verification = accountVerificationRepository.findByUser(user);
        if (verification == null) {
            log.warn("No verification found for user: {}", email);
            throw new TokenException("No verification code found for this email");
        }

        log.info("Found verification for user: {}, stored OTP: {}, expires at: {}",
                email, verification.getOtpCode(), verification.getExpiresAt());

        if (verification.isExpired()) {
            log.warn("Expired OTP code for user: {}", email);
            throw new TokenException("Verification code has expired. Please request a new one.");
        }

        if (!verification.getOtpCode().equals(otpCode)) {
            log.warn("Invalid OTP code for user: {}. Expected: {}, Received: {}",
                    email, verification.getOtpCode(), otpCode);
            throw new TokenException("Invalid verification code");
        }

        log.info("OTP code validated successfully for user: {}", email);
        return verification;
    }

    /**
     * Deletes a verification token.
     *
     * @param verification Verification
     */
    @Transactional
    public void deleteVerificationToken(AccountVerification verification) {
        accountVerificationRepository.delete(verification);
    }

    /**
     * Creates a password reset token for a user.
     *
     * @param user User
     * @return Reset token
     */
    @Transactional
    public ResetPasswordToken createPasswordResetToken(User user) {
        // Delete any existing reset tokens for the user
        List<ResetPasswordToken> existingTokens = resetPasswordTokenRepository.findByUser(user);
        if (!existingTokens.isEmpty()) {
            resetPasswordTokenRepository.deleteAll(existingTokens);
        }

        // Create new reset token
        ResetPasswordToken resetToken = new ResetPasswordToken();
        resetToken.setToken(UUID.randomUUID().toString());
        resetToken.setUser(user);
        resetToken.setExpirationTime(LocalDateTime.now().plusHours(24));

        return resetPasswordTokenRepository.save(resetToken);
    }

    /**
     * Validates a password reset token.
     *
     * @param token Token
     * @return Reset token
     * @throws TokenException if token is invalid
     */
    @Transactional(readOnly = true)
    public ResetPasswordToken validatePasswordResetToken(String token) {
        ResetPasswordToken resetToken = resetPasswordTokenRepository.findById(token)
                .orElseThrow(() -> new TokenException("Invalid password reset token"));

        if (resetToken.isExpired()) {
            throw new TokenException("Password reset token has expired");
        }

        return resetToken;
    }

    /**
     * Deletes a password reset token.
     *
     * @param resetToken Reset token
     */
    @Transactional
    public void deletePasswordResetToken(ResetPasswordToken resetToken) {
        resetPasswordTokenRepository.delete(resetToken);
    }

    /**
     * Cleans up expired tokens.
     */
    @Transactional
    public void cleanupExpiredTokens() {
        LocalDateTime now = LocalDateTime.now();

        // Clean up expired OAuth tokens
        List<OauthToken> expiredOauthTokens = oauthTokenRepository.findExpiredTokens(now);
        if (!expiredOauthTokens.isEmpty()) {
            oauthTokenRepository.deleteAll(expiredOauthTokens);
            log.info("Cleaned up {} expired OAuth tokens", expiredOauthTokens.size());
        }

        // Clean up expired verification tokens
        List<AccountVerification> expiredVerifications = accountVerificationRepository.findExpiredTokens(now);
        if (!expiredVerifications.isEmpty()) {
            accountVerificationRepository.deleteAll(expiredVerifications);
            log.info("Cleaned up {} expired verification tokens", expiredVerifications.size());
        }

        // Clean up expired reset tokens
        List<ResetPasswordToken> expiredResetTokens = resetPasswordTokenRepository.findExpiredTokens(now);
        if (!expiredResetTokens.isEmpty()) {
            resetPasswordTokenRepository.deleteAll(expiredResetTokens);
            log.info("Cleaned up {} expired reset tokens", expiredResetTokens.size());
        }
    }
}


