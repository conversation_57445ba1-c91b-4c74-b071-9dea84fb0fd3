package com.pressmeet247.auth.token;

import com.pressmeet247.common.base.UuidEntity;
import com.pressmeet247.auth.entity.User;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing an OAuth token.
 * Used for storing JWT and refresh tokens.
 */
@Entity
@Table(
    name = "oauth_tokens",
    indexes = {
        @Index(name = "idx_oauth_token_refresh_token", columnList = "refresh_token", unique = true),
        @Index(name = "idx_oauth_token_user_id", columnList = "user_id"),
        @Index(name = "idx_oauth_token_expiration_time", columnList = "expiration_time"),
        @Index(name = "idx_oauth_token_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE oauth_tokens SET deleted = true WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing an OAuth token")
public class OauthToken extends UuidEntity {

    @Column(name = "access_token", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "JWT access token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Column(name = "refresh_token", length = 100, nullable = false, unique = true)
    @Schema(description = "Refresh token", example = "7c9e6679-7425-40de-944b-e07fc1f90ae7")
    private String refreshToken;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @Schema(description = "User associated with this token")
    private User user;

    @Column(name = "expiration_time", nullable = false)
    @Schema(description = "Token expiration timestamp")
    private LocalDateTime expirationTime;

    @Column(name = "ip_address", length = 45)
    @Schema(description = "IP address that requested the token", example = "***********")
    private String ipAddress;

    @Column(name = "user_agent", length = 255)
    @Schema(description = "User agent that requested the token")
    private String userAgent;

    @Column(name = "device_type", length = 50)
    @Schema(description = "Device type that requested the token", example = "Desktop")
    private String deviceType;

    @Column(name = "location", length = 100)
    @Schema(description = "Location that requested the token", example = "New York, USA")
    private String location;

    @Column(name = "revoked", nullable = false)
    @Schema(description = "Whether the token has been revoked", example = "false")
    private boolean revoked = false;

    @Column(name = "revoked_at")
    @Schema(description = "When the token was revoked")
    private LocalDateTime revokedAt;

    /**
     * Checks if the token is expired.
     *
     * @return true if the token is expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expirationTime);
    }

    /**
     * Revokes the token.
     */
    public void revoke() {
        this.revoked = true;
        this.revokedAt = LocalDateTime.now();
    }

    /**
     * Checks if the token is valid (not expired and not revoked).
     *
     * @return true if the token is valid, false otherwise
     */
    public boolean isValid() {
        return !isExpired() && !revoked;
    }
}


