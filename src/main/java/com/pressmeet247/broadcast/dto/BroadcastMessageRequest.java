package com.pressmeet247.broadcast.dto;

import com.pressmeet247.broadcast.entity.BroadcastMessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * DTO for broadcast message request.
 */
@Data
@Schema(description = "Broadcast message request")
public class BroadcastMessageRequest {
    
    @NotBlank(message = "Title is required")
    @Schema(description = "Message title", example = "Important Announcement")
    private String title;
    
    @NotBlank(message = "Content is required")
    @Schema(description = "Message content", example = "This is an important announcement for all users.")
    private String content;
    
    @NotNull(message = "Type is required")
    @Schema(description = "Message type", example = "ANNOUNCEMENT")
    private BroadcastMessageType type;
    
    @Schema(description = "Whether to target all users", example = "false")
    private boolean targetAllUsers;
    
    @Schema(description = "Whether to target premium users", example = "false")
    private boolean targetPremiumUsers;
    
    @Schema(description = "IDs of specific users to target")
    private List<Long> specificUserIds;
}

