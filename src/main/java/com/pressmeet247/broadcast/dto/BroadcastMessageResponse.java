package com.pressmeet247.broadcast.dto;

import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.entity.BroadcastMessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DTO for broadcast message response.
 */
@Data
@Builder
@Schema(description = "Broadcast message response")
public class BroadcastMessageResponse {
    
    @Schema(description = "Message ID", example = "1")
    private Long id;
    
    @Schema(description = "Sender ID", example = "1")
    private Long senderId;
    
    @Schema(description = "Sender name", example = "John Doe")
    private String senderName;
    
    @Schema(description = "Message title", example = "Important Announcement")
    private String title;
    
    @Schema(description = "Message content", example = "This is an important announcement for all users.")
    private String content;
    
    @Schema(description = "Message type", example = "ANNOUNCEMENT")
    private BroadcastMessageType type;
    
    @Schema(description = "Whether the message targets all users", example = "false")
    private boolean targetAllUsers;
    
    @Schema(description = "Whether the message targets premium users", example = "false")
    private boolean targetPremiumUsers;
    
    @Schema(description = "IDs of specific users targeted by the message")
    private List<Long> specificUserIds;
    
    @Schema(description = "Date and time when the message was created")
    private LocalDateTime createdAt;
    
    /**
     * Converts a BroadcastMessage entity to BroadcastMessageResponse DTO.
     *
     * @param message BroadcastMessage entity
     * @return BroadcastMessageResponse DTO
     */
    public static BroadcastMessageResponse fromEntity(BroadcastMessage message) {
        String senderName = message.getSender().getFirstName() + " " + message.getSender().getLastName();
        
        List<Long> specificUserIds = message.getSpecificUsers().stream()
                .map(user -> user.getId())
                .collect(Collectors.toList());
        
        return BroadcastMessageResponse.builder()
                .id(message.getId())
                .senderId(message.getSender().getId())
                .senderName(senderName)
                .title(message.getTitle())
                .content(message.getContent())
                .type(message.getType())
                .targetAllUsers(message.isTargetAllUsers())
                .targetPremiumUsers(message.isTargetPremiumUsers())
                .specificUserIds(specificUserIds)
                .createdAt(message.getCreatedAt())
                .build();
    }
}

