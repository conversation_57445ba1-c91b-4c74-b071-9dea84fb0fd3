package com.pressmeet247.broadcast.dto;

import com.pressmeet247.broadcast.entity.MessageDeliveryStatus;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for message delivery status response.
 */
@Data
@Builder
@Schema(description = "Message delivery status response")
public class MessageDeliveryStatusResponse {
    
    @Schema(description = "Status ID", example = "1")
    private Long id;
    
    @Schema(description = "Message ID", example = "1")
    private Long messageId;
    
    @Schema(description = "Recipient ID", example = "1")
    private Long recipientId;
    
    @Schema(description = "Recipient name", example = "John Doe")
    private String recipientName;
    
    @Schema(description = "Delivery status", example = "DELIVERED")
    private MessageDeliveryStatusType status;
    
    @Schema(description = "Date and time when the message was delivered")
    private LocalDateTime deliveredAt;
    
    @Schema(description = "Date and time when the message was read")
    private LocalDateTime readAt;
    
    @Schema(description = "Error message if delivery failed")
    private String errorMessage;
    
    /**
     * Converts a MessageDeliveryStatus entity to MessageDeliveryStatusResponse DTO.
     *
     * @param status MessageDeliveryStatus entity
     * @return MessageDeliveryStatusResponse DTO
     */
    public static MessageDeliveryStatusResponse fromEntity(MessageDeliveryStatus status) {
        String recipientName = status.getRecipient().getFirstName() + " " + status.getRecipient().getLastName();
        
        return MessageDeliveryStatusResponse.builder()
                .id(status.getId())
                .messageId(status.getMessage().getId())
                .recipientId(status.getRecipient().getId())
                .recipientName(recipientName)
                .status(status.getStatus())
                .deliveredAt(status.getDeliveredAt())
                .readAt(status.getReadAt())
                .errorMessage(status.getErrorMessage())
                .build();
    }
}

