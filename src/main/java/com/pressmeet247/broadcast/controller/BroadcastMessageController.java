package com.pressmeet247.broadcast.controller;

import com.pressmeet247.auth.dto.response.ApiResponse;
import com.pressmeet247.broadcast.dto.BroadcastMessageRequest;
import com.pressmeet247.broadcast.dto.BroadcastMessageResponse;
import com.pressmeet247.broadcast.dto.MessageDeliveryStatusResponse;
import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatus;
import com.pressmeet247.broadcast.service.BroadcastMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for broadcast message operations.
 */
@RestController
@RequestMapping("/api/messages")
@RequiredArgsConstructor
@Tag(name = "Broadcast Messages", description = "Broadcast Messages API")
public class BroadcastMessageController {

    private final BroadcastMessageService broadcastMessageService;

    /**
     * Creates a broadcast message.
     *
     * @param request Broadcast message request
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create broadcast message", description = "Creates a broadcast message (requires ADMIN role)")
    public ResponseEntity<ApiResponse<BroadcastMessageResponse>> createBroadcastMessage(
            @RequestBody BroadcastMessageRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();

        BroadcastMessage message = broadcastMessageService.createBroadcastMessageByEmail(
                email,
                request.getTitle(),
                request.getContent(),
                request.getType(),
                request.isTargetAllUsers(),
                request.isTargetPremiumUsers(),
                request.getSpecificUserIds()
        );

        return ResponseEntity.ok(ApiResponse.success(
                "Broadcast message created successfully",
                BroadcastMessageResponse.fromEntity(message)
        ));
    }

    /**
     * Gets a broadcast message by ID.
     *
     * @param id Message ID
     * @return Response entity
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get broadcast message", description = "Returns a broadcast message by ID (requires ADMIN role)")
    public ResponseEntity<ApiResponse<BroadcastMessageResponse>> getBroadcastMessage(@PathVariable Long id) {
        BroadcastMessage message = broadcastMessageService.getBroadcastMessageById(id);

        return ResponseEntity.ok(ApiResponse.success(
                "Broadcast message retrieved successfully",
                BroadcastMessageResponse.fromEntity(message)
        ));
    }

    /**
     * Gets all broadcast messages.
     *
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all broadcast messages", description = "Returns all broadcast messages (requires ADMIN role)")
    public ResponseEntity<ApiResponse<Page<BroadcastMessageResponse>>> getAllBroadcastMessages(Pageable pageable) {
        Page<BroadcastMessage> messagePage = broadcastMessageService.getAllBroadcastMessages(pageable);
        Page<BroadcastMessageResponse> responsePage = messagePage.map(BroadcastMessageResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Broadcast messages retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets broadcast messages for the authenticated user.
     *
     * @param userDetails Authenticated user details
     * @param pageable Pageable
     * @return Response entity
     */
    @GetMapping("/my-messages")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get my messages", description = "Returns broadcast messages for the authenticated user")
    public ResponseEntity<ApiResponse<Page<BroadcastMessageResponse>>> getMyBroadcastMessages(
            @AuthenticationPrincipal UserDetails userDetails,
            Pageable pageable) {

        String email = userDetails.getUsername();
        Page<BroadcastMessage> messagePage = broadcastMessageService.getBroadcastMessagesForUserByEmail(email, pageable);
        Page<BroadcastMessageResponse> responsePage = messagePage.map(BroadcastMessageResponse::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(
                "Broadcast messages retrieved successfully",
                responsePage
        ));
    }

    /**
     * Gets unread message count for the authenticated user.
     *
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @GetMapping("/unread-count")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get unread message count", description = "Returns the number of unread messages for the authenticated user")
    public ResponseEntity<ApiResponse<Long>> getUnreadMessageCount(
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();
        long unreadCount = broadcastMessageService.getUnreadMessageCountByEmail(email);

        return ResponseEntity.ok(ApiResponse.success(
                "Unread message count retrieved successfully",
                unreadCount
        ));
    }

    /**
     * Marks a message as read.
     *
     * @param id Message ID
     * @param userDetails Authenticated user details
     * @return Response entity
     */
    @PostMapping("/{id}/read")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Mark message as read", description = "Marks a message as read for the authenticated user")
    public ResponseEntity<ApiResponse<MessageDeliveryStatusResponse>> markMessageAsRead(
            @PathVariable Long id,
            @AuthenticationPrincipal UserDetails userDetails) {

        String email = userDetails.getUsername();
        MessageDeliveryStatus status = broadcastMessageService.markMessageAsReadByEmail(id, email);

        return ResponseEntity.ok(ApiResponse.success(
                "Message marked as read successfully",
                MessageDeliveryStatusResponse.fromEntity(status)
        ));
    }

    /**
     * Gets delivery status for a message.
     *
     * @param messageId Message ID
     * @param recipientId Recipient ID
     * @return Response entity
     */
    @GetMapping("/{messageId}/status/{recipientId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get delivery status", description = "Returns the delivery status of a message for a recipient (requires ADMIN role)")
    public ResponseEntity<ApiResponse<MessageDeliveryStatusResponse>> getDeliveryStatus(
            @PathVariable Long messageId,
            @PathVariable Long recipientId) {

        MessageDeliveryStatus status = broadcastMessageService.getDeliveryStatus(messageId, recipientId);

        return ResponseEntity.ok(ApiResponse.success(
                "Delivery status retrieved successfully",
                MessageDeliveryStatusResponse.fromEntity(status)
        ));
    }
}
