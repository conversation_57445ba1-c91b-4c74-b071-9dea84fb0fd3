package com.pressmeet247.broadcast.repository;

import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.entity.BroadcastMessageType;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for BroadcastMessage entity operations.
 */
@Repository
public interface BroadcastMessageRepository extends JpaRepository<BroadcastMessage, Long> {
    
    /**
     * Find messages by sender.
     *
     * @param sender Sender
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    Page<BroadcastMessage> findBySender(User sender, Pageable pageable);
    
    /**
     * Find messages by type.
     *
     * @param type Message type
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    Page<BroadcastMessage> findByType(BroadcastMessageType type, Pageable pageable);
    
    /**
     * Find messages targeting all users.
     *
     * @param targetAllUsers Target all users flag
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    Page<BroadcastMessage> findByTargetAllUsers(boolean targetAllUsers, Pageable pageable);
    
    /**
     * Find messages targeting premium users.
     *
     * @param targetPremiumUsers Target premium users flag
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    Page<BroadcastMessage> findByTargetPremiumUsers(boolean targetPremiumUsers, Pageable pageable);
    
    /**
     * Find messages targeting a specific user.
     *
     * @param user User
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    @Query("SELECT bm FROM BroadcastMessage bm JOIN bm.specificUsers u WHERE u = :user")
    Page<BroadcastMessage> findBySpecificUser(@Param("user") User user, Pageable pageable);
    
    /**
     * Find messages relevant to a user (all messages, premium messages if user is premium, or specific messages).
     *
     * @param user User
     * @param isPremium Whether the user is premium
     * @param pageable Pageable
     * @return Page of BroadcastMessage
     */
    @Query("SELECT DISTINCT bm FROM BroadcastMessage bm LEFT JOIN bm.specificUsers u " +
            "WHERE bm.targetAllUsers = true " +
            "OR (bm.targetPremiumUsers = true AND :isPremium = true) " +
            "OR u = :user")
    Page<BroadcastMessage> findMessagesForUser(
            @Param("user") User user,
            @Param("isPremium") boolean isPremium,
            Pageable pageable);
}



