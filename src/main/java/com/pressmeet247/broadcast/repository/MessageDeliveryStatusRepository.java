package com.pressmeet247.broadcast.repository;

import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatus;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatusType;
import com.pressmeet247.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for MessageDeliveryStatus entity operations.
 */
@Repository
public interface MessageDeliveryStatusRepository extends JpaRepository<MessageDeliveryStatus, Long> {
    
    /**
     * Find delivery status by message and recipient.
     *
     * @param message Message
     * @param recipient Recipient
     * @return Optional of MessageDeliveryStatus
     */
    Optional<MessageDeliveryStatus> findByMessageAndRecipient(BroadcastMessage message, User recipient);
    
    /**
     * Find delivery statuses by message.
     *
     * @param message Message
     * @return List of MessageDeliveryStatus
     */
    List<MessageDeliveryStatus> findByMessage(BroadcastMessage message);
    
    /**
     * Find delivery statuses by recipient.
     *
     * @param recipient Recipient
     * @param pageable Pageable
     * @return Page of MessageDeliveryStatus
     */
    Page<MessageDeliveryStatus> findByRecipient(User recipient, Pageable pageable);
    
    /**
     * Find delivery statuses by status.
     *
     * @param status Status
     * @param pageable Pageable
     * @return Page of MessageDeliveryStatus
     */
    Page<MessageDeliveryStatus> findByStatus(MessageDeliveryStatusType status, Pageable pageable);
    
    /**
     * Find delivery statuses by recipient and status.
     *
     * @param recipient Recipient
     * @param status Status
     * @param pageable Pageable
     * @return Page of MessageDeliveryStatus
     */
    Page<MessageDeliveryStatus> findByRecipientAndStatus(User recipient, MessageDeliveryStatusType status, Pageable pageable);
    
    /**
     * Count unread messages for a recipient.
     *
     * @param recipient Recipient
     * @param status Status
     * @return Count of unread messages
     */
    long countByRecipientAndStatus(User recipient, MessageDeliveryStatusType status);
}

