package com.pressmeet247.broadcast.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a broadcast message in the system.
 */
@Entity
@Table(
    name = "broadcast_messages",
    indexes = {
        @Index(name = "idx_broadcast_messages_sender_id", columnList = "sender_id"),
        @Index(name = "idx_broadcast_messages_type", columnList = "type"),
        @Index(name = "idx_broadcast_messages_created_at", columnList = "created_at"),
        @Index(name = "idx_broadcast_messages_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE broadcast_messages SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing a broadcast message")
public class BroadcastMessage extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id", nullable = false)
    @Schema(description = "User who sent the message")
    private User sender;

    @Column(name = "title", length = 255, nullable = false)
    @Schema(description = "Message title", example = "Important Announcement")
    private String title;

    @Column(name = "content", columnDefinition = "TEXT", nullable = false)
    @Schema(description = "Message content")
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    @Schema(description = "Type of broadcast message", example = "ANNOUNCEMENT")
    private BroadcastMessageType type;

    @Column(name = "target_all_users", nullable = false)
    @Schema(description = "Whether the message targets all users", example = "false")
    private boolean targetAllUsers = false;

    @Column(name = "target_premium_users", nullable = false)
    @Schema(description = "Whether the message targets premium users", example = "false")
    private boolean targetPremiumUsers = false;

    @ManyToMany
    @JoinTable(
        name = "broadcast_message_recipients",
        joinColumns = @JoinColumn(name = "message_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id")
    )
    @Schema(description = "Specific users targeted by the message")
    private List<User> specificUsers = new ArrayList<>();

    @OneToMany(mappedBy = "message", cascade = CascadeType.ALL, orphanRemoval = true)
    @Schema(description = "Message delivery status for each recipient")
    private List<MessageDeliveryStatus> deliveryStatuses = new ArrayList<>();
}



