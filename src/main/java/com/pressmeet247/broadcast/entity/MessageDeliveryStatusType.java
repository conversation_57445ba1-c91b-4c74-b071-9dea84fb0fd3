package com.pressmeet247.broadcast.entity;

/**
 * Enum representing the status of message delivery.
 */
public enum MessageDeliveryStatusType {
    /**
     * Message is pending delivery.
     */
    PENDING,
    
    /**
     * Message has been sent.
     */
    SENT,
    
    /**
     * Message has been delivered.
     */
    DELIVERED,
    
    /**
     * Message has been read.
     */
    READ,
    
    /**
     * Message delivery failed.
     */
    FAILED
}

