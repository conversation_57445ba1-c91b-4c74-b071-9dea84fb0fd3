package com.pressmeet247.broadcast.entity;

import com.pressmeet247.auth.entity.User;
import com.pressmeet247.common.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Entity representing the delivery status of a message to a recipient.
 */
@Entity
@Table(
    name = "message_delivery_statuses",
    indexes = {
        @Index(name = "idx_message_delivery_statuses_message_id", columnList = "message_id"),
        @Index(name = "idx_message_delivery_statuses_recipient_id", columnList = "recipient_id"),
        @Index(name = "idx_message_delivery_statuses_status", columnList = "status"),
        @Index(name = "idx_message_delivery_statuses_created_at", columnList = "created_at"),
        @Index(name = "idx_message_delivery_statuses_deleted", columnList = "deleted")
    }
)
@SQLDelete(sql = "UPDATE message_delivery_statuses SET deleted = true, deleted_at = NOW(), deleted_by = NULL WHERE id = ?")
@Where(clause = "deleted = false")
@Getter
@Setter
@Schema(description = "Entity representing the delivery status of a message")
public class MessageDeliveryStatus extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "message_id", nullable = false)
    @Schema(description = "Broadcast message")
    private BroadcastMessage message;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id", nullable = false)
    @Schema(description = "Message recipient")
    private User recipient;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Schema(description = "Delivery status", example = "SENT")
    private MessageDeliveryStatusType status = MessageDeliveryStatusType.PENDING;

    @Column(name = "delivered_at")
    @Schema(description = "Date and time when the message was delivered")
    private LocalDateTime deliveredAt;

    @Column(name = "read_at")
    @Schema(description = "Date and time when the message was read")
    private LocalDateTime readAt;

    @Column(name = "error_message", length = 255)
    @Schema(description = "Error message if delivery failed")
    private String errorMessage;

    /**
     * Marks the message as delivered.
     */
    public void markAsDelivered() {
        this.status = MessageDeliveryStatusType.DELIVERED;
        this.deliveredAt = LocalDateTime.now();
    }

    /**
     * Marks the message as read.
     */
    public void markAsRead() {
        this.status = MessageDeliveryStatusType.READ;
        this.readAt = LocalDateTime.now();
    }

    /**
     * Marks the message as failed.
     *
     * @param errorMessage Error message
     */
    public void markAsFailed(String errorMessage) {
        this.status = MessageDeliveryStatusType.FAILED;
        this.errorMessage = errorMessage;
    }
}

