package com.pressmeet247.broadcast.service;

import com.pressmeet247.auth.entity.*;
import com.pressmeet247.broadcast.entity.BroadcastMessage;
import com.pressmeet247.broadcast.entity.BroadcastMessageType;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatus;
import com.pressmeet247.broadcast.entity.MessageDeliveryStatusType;
import com.pressmeet247.common.exception.ResourceNotFoundException;
import com.pressmeet247.broadcast.repository.BroadcastMessageRepository;
import com.pressmeet247.broadcast.repository.MessageDeliveryStatusRepository;
import com.pressmeet247.subscription.repository.SubscriptionRepository;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for broadcast message operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BroadcastMessageService {

    private final BroadcastMessageRepository broadcastMessageRepository;
    private final MessageDeliveryStatusRepository messageDeliveryStatusRepository;
    private final UserRepository userRepository;
    private final SubscriptionRepository subscriptionRepository;
    private final EmailService emailService;

    /**
     * Creates a broadcast message.
     *
     * @param senderId Sender ID
     * @param title Message title
     * @param content Message content
     * @param type Message type
     * @param targetAllUsers Whether to target all users
     * @param targetPremiumUsers Whether to target premium users
     * @param specificUserIds IDs of specific users to target
     * @return Created broadcast message
     */
    @Transactional
    public BroadcastMessage createBroadcastMessage(
            Long senderId,
            String title,
            String content,
            BroadcastMessageType type,
            boolean targetAllUsers,
            boolean targetPremiumUsers,
            List<Long> specificUserIds) {

        // Find sender
        User sender = userRepository.findById(senderId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + senderId));

        return createBroadcastMessageInternal(sender, title, content, type, targetAllUsers, targetPremiumUsers, specificUserIds);
    }

    /**
     * Creates a broadcast message by sender email.
     *
     * @param senderEmail Sender email
     * @param title Message title
     * @param content Message content
     * @param type Message type
     * @param targetAllUsers Whether to target all users
     * @param targetPremiumUsers Whether to target premium users
     * @param specificUserIds IDs of specific users to target
     * @return Created broadcast message
     */
    @Transactional
    public BroadcastMessage createBroadcastMessageByEmail(
            String senderEmail,
            String title,
            String content,
            BroadcastMessageType type,
            boolean targetAllUsers,
            boolean targetPremiumUsers,
            List<Long> specificUserIds) {

        // Find sender by email
        User sender = userRepository.findByEmail(senderEmail);
        if (sender == null) {
            throw new ResourceNotFoundException("User not found with email: " + senderEmail);
        }

        return createBroadcastMessageInternal(sender, title, content, type, targetAllUsers, targetPremiumUsers, specificUserIds);
    }

    /**
     * Internal method to create a broadcast message.
     *
     * @param sender Sender
     * @param title Message title
     * @param content Message content
     * @param type Message type
     * @param targetAllUsers Whether to target all users
     * @param targetPremiumUsers Whether to target premium users
     * @param specificUserIds IDs of specific users to target
     * @return Created broadcast message
     */
    private BroadcastMessage createBroadcastMessageInternal(
            User sender,
            String title,
            String content,
            BroadcastMessageType type,
            boolean targetAllUsers,
            boolean targetPremiumUsers,
            List<Long> specificUserIds) {

        // Create broadcast message
        BroadcastMessage message = new BroadcastMessage();
        message.setSender(sender);
        message.setTitle(title);
        message.setContent(content);
        message.setType(type);
        message.setTargetAllUsers(targetAllUsers);
        message.setTargetPremiumUsers(targetPremiumUsers);

        // Add specific users if provided
        if (specificUserIds != null && !specificUserIds.isEmpty()) {
            List<User> specificUsers = userRepository.findAllById(specificUserIds);
            message.setSpecificUsers(specificUsers);
        }

        // Save message
        BroadcastMessage savedMessage = broadcastMessageRepository.save(message);

        // Create delivery statuses for recipients
        createDeliveryStatuses(savedMessage);

        // Send message to recipients
        sendMessageToRecipients(savedMessage);

        return savedMessage;
    }

    /**
     * Creates delivery statuses for recipients.
     *
     * @param message Broadcast message
     */
    private void createDeliveryStatuses(BroadcastMessage message) {
        List<User> recipients = getRecipients(message);

        for (User recipient : recipients) {
            MessageDeliveryStatus status = new MessageDeliveryStatus();
            status.setMessage(message);
            status.setRecipient(recipient);
            status.setStatus(MessageDeliveryStatusType.PENDING);

            messageDeliveryStatusRepository.save(status);
        }
    }

    /**
     * Gets recipients for a broadcast message.
     *
     * @param message Broadcast message
     * @return List of recipients
     */
    private List<User> getRecipients(BroadcastMessage message) {
        if (message.isTargetAllUsers()) {
            return userRepository.findAll();
        } else if (message.isTargetPremiumUsers()) {
            return userRepository.findAll().stream()
                    .filter(user -> subscriptionRepository.hasActiveSubscription(user))
                    .collect(Collectors.toList());
        } else {
            return message.getSpecificUsers();
        }
    }

    /**
     * Sends message to recipients.
     *
     * @param message Broadcast message
     */
    private void sendMessageToRecipients(BroadcastMessage message) {
        List<MessageDeliveryStatus> deliveryStatuses = messageDeliveryStatusRepository.findByMessage(message);

        for (MessageDeliveryStatus status : deliveryStatuses) {
            try {
                // Send email notification
                emailService.sendSimpleEmail(
                        status.getRecipient().getEmail(),
                        message.getTitle(),
                        message.getContent()
                );

                // Update delivery status
                status.markAsDelivered();
                messageDeliveryStatusRepository.save(status);
            } catch (Exception e) {
                log.error("Failed to send message to recipient {}: {}", status.getRecipient().getEmail(), e.getMessage());
                status.markAsFailed(e.getMessage());
                messageDeliveryStatusRepository.save(status);
            }
        }
    }

    /**
     * Gets a broadcast message by ID.
     *
     * @param id Message ID
     * @return Broadcast message
     */
    public BroadcastMessage getBroadcastMessageById(Long id) {
        return broadcastMessageRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Broadcast message not found with ID: " + id));
    }

    /**
     * Gets all broadcast messages.
     *
     * @param pageable Pageable
     * @return Page of broadcast messages
     */
    public Page<BroadcastMessage> getAllBroadcastMessages(Pageable pageable) {
        return broadcastMessageRepository.findAll(pageable);
    }

    /**
     * Gets broadcast messages by sender.
     *
     * @param senderId Sender ID
     * @param pageable Pageable
     * @return Page of broadcast messages
     */
    public Page<BroadcastMessage> getBroadcastMessagesBySender(Long senderId, Pageable pageable) {
        User sender = userRepository.findById(senderId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + senderId));
        return broadcastMessageRepository.findBySender(sender, pageable);
    }

    /**
     * Gets broadcast messages by type.
     *
     * @param type Message type
     * @param pageable Pageable
     * @return Page of broadcast messages
     */
    public Page<BroadcastMessage> getBroadcastMessagesByType(BroadcastMessageType type, Pageable pageable) {
        return broadcastMessageRepository.findByType(type, pageable);
    }

    /**
     * Gets broadcast messages for a user.
     *
     * @param userId User ID
     * @param pageable Pageable
     * @return Page of broadcast messages
     */
    public Page<BroadcastMessage> getBroadcastMessagesForUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        boolean isPremium = subscriptionRepository.hasActiveSubscription(user);

        return broadcastMessageRepository.findMessagesForUser(user, isPremium, pageable);
    }

    /**
     * Gets broadcast messages for a user by email.
     *
     * @param email User email
     * @param pageable Pageable
     * @return Page of broadcast messages
     */
    public Page<BroadcastMessage> getBroadcastMessagesForUserByEmail(String email, Pageable pageable) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }
        boolean isPremium = subscriptionRepository.hasActiveSubscription(user);

        return broadcastMessageRepository.findMessagesForUser(user, isPremium, pageable);
    }

    /**
     * Gets delivery status for a message and recipient.
     *
     * @param messageId Message ID
     * @param recipientId Recipient ID
     * @return Message delivery status
     */
    public MessageDeliveryStatus getDeliveryStatus(Long messageId, Long recipientId) {
        BroadcastMessage message = getBroadcastMessageById(messageId);
        User recipient = userRepository.findById(recipientId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + recipientId));

        return messageDeliveryStatusRepository.findByMessageAndRecipient(message, recipient)
                .orElseThrow(() -> new ResourceNotFoundException("Delivery status not found for message ID: " + messageId + " and recipient ID: " + recipientId));
    }

    /**
     * Marks a message as read for a recipient.
     *
     * @param messageId Message ID
     * @param recipientId Recipient ID
     * @return Updated message delivery status
     */
    @Transactional
    public MessageDeliveryStatus markMessageAsRead(Long messageId, Long recipientId) {
        MessageDeliveryStatus status = getDeliveryStatus(messageId, recipientId);
        status.markAsRead();
        return messageDeliveryStatusRepository.save(status);
    }

    /**
     * Marks a message as read for a recipient by email.
     *
     * @param messageId Message ID
     * @param recipientEmail Recipient email
     * @return Updated message delivery status
     */
    @Transactional
    public MessageDeliveryStatus markMessageAsReadByEmail(Long messageId, String recipientEmail) {
        User recipient = userRepository.findByEmail(recipientEmail);
        if (recipient == null) {
            throw new ResourceNotFoundException("User not found with email: " + recipientEmail);
        }

        BroadcastMessage message = getBroadcastMessageById(messageId);

        MessageDeliveryStatus status = messageDeliveryStatusRepository.findByMessageAndRecipient(message, recipient)
                .orElseThrow(() -> new ResourceNotFoundException("Delivery status not found for message ID: " + messageId + " and recipient email: " + recipientEmail));

        status.markAsRead();
        return messageDeliveryStatusRepository.save(status);
    }

    /**
     * Gets unread message count for a user.
     *
     * @param userId User ID
     * @return Unread message count
     */
    public long getUnreadMessageCount(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        return messageDeliveryStatusRepository.countByRecipientAndStatus(user, MessageDeliveryStatusType.DELIVERED);
    }

    /**
     * Gets unread message count for a user by email.
     *
     * @param email User email
     * @return Unread message count
     */
    public long getUnreadMessageCountByEmail(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with email: " + email);
        }

        return messageDeliveryStatusRepository.countByRecipientAndStatus(user, MessageDeliveryStatusType.DELIVERED);
    }
}



