spring:
  application:
    name: auth-service-test

  # Database configuration - use H2 for testing
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
    defer-datasource-initialization: true

  # SQL initialization
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql
      continue-on-error: false

  # Redis configuration - disabled for testing
  data:
    redis:
      host: localhost
      port: 6379

  # Email configuration - disabled for testing
  mail:
    host: localhost
    port: 3025
    username: test
    password: test

  # OAuth2 configuration - disabled for testing
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: test-client-id
            client-secret: test-client-secret
            redirect-uri: http://localhost:8080/oauth2/callback/google
            scope:
              - email
              - profile

# Server configuration
server:
  port: 0 # Random port for testing
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

# Logging configuration
logging:
  level:
    root: INFO
    com.auth.service: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO

# Application configuration
app:
  # JWT configuration
  jwt:
    secret: dGVzdFNlY3JldEtleUZvclRlc3RpbmdKd3RUb2tlbkdlbmVyYXRpb25BbmRWYWxpZGF0aW9u
    expiration: 3600000
    refreshExpiration: ********
    issuer: auth-service-test
    audience: test

  # Security configuration
  security:
    allowedOrigins:
      - http://localhost:3000
    maxFailedLoginAttempts: 5
    accountLockoutMinutes: 15
    passwordResetExpirationHours: 24
    accountVerificationExpirationHours: 24

  # OAuth2 configuration
  oauth2:
    googleRedirectUri: http://localhost:8080/oauth2/callback/google
    facebookRedirectUri: http://localhost:8080/oauth2/callback/facebook
    registrationUrl: http://localhost:3000/complete-registration
    profileUrl: http://localhost:3000/profile
    errorUrl: http://localhost:3000/error

  # Email configuration
  email:
    from: <EMAIL>
    verification-url: http://localhost:3000/verify
    reset-password-url: http://localhost:3000/reset-password
