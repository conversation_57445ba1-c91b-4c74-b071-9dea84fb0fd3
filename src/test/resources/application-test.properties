# Test Database Configuration
spring.datasource.url=*********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=

# JPA Configuration for testing
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# SQL Initialization for testing
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema-create-if-not-exists.sql
spring.sql.init.continue-on-error=true

# Disable Redis for testing
spring.data.redis.repositories.enabled=false
