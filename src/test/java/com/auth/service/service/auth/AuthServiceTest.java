package com.auth.service.service.auth;

import com.auth.service.dto.request.SignUpRequest;
import com.auth.service.entity.Role;
import com.auth.service.entity.User;
import com.auth.service.exception.UserAlreadyExistsException;
import com.auth.service.repository.RoleRepository;
import com.auth.service.repository.UserRepository;
import com.auth.service.service.email.EmailService;
import com.auth.service.service.token.TokenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private TokenService tokenService;

    @Mock
    private EmailService emailService;

    @Mock
    private AuthenticationManager authenticationManager;

    @InjectMocks
    private AuthService authService;

    private SignUpRequest signUpRequest;
    private Role userRole;

    @BeforeEach
    void setUp() {
        signUpRequest = new SignUpRequest();
        signUpRequest.setFirstName("John");
        signUpRequest.setLastName("Doe");
        signUpRequest.setEmail("<EMAIL>");
        signUpRequest.setPassword("Password123!");

        userRole = new Role();
        userRole.setId(1L);
        userRole.setName("USER");
    }

    @Test
    void registerUser_Success() {
        // Arrange
        when(userRepository.existsByEmail(signUpRequest.getEmail())).thenReturn(false);
        when(roleRepository.findByName("USER")).thenReturn(Optional.of(userRole));
        when(passwordEncoder.encode(signUpRequest.getPassword())).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> {
            User savedUser = invocation.getArgument(0);
            savedUser.setId(1L);
            return savedUser;
        });

        // Act
        User result = authService.registerUser(signUpRequest);

        // Assert
        assertNotNull(result);
        assertEquals(signUpRequest.getEmail(), result.getEmail());
        assertEquals("encodedPassword", result.getPassword());
        assertFalse(result.isEnabled());
        assertFalse(result.isEmailVerified());
        verify(emailService).sendVerificationEmail(any(User.class));
    }

    @Test
    void registerUser_UserAlreadyExists() {
        // Arrange
        User existingUser = new User();
        existingUser.setEmail(signUpRequest.getEmail());
        existingUser.setEnabled(true);

        when(userRepository.existsByEmail(signUpRequest.getEmail())).thenReturn(true);
        when(userRepository.findByEmail(signUpRequest.getEmail())).thenReturn(existingUser);

        // Act & Assert
        assertThrows(UserAlreadyExistsException.class, () -> authService.registerUser(signUpRequest));
        verify(emailService, never()).sendVerificationEmail(any(User.class));
    }

    @Test
    void registerUser_ExistingUserNotEnabled() {
        // Arrange
        User existingUser = new User();
        existingUser.setEmail(signUpRequest.getEmail());
        existingUser.setEnabled(false);

        when(userRepository.existsByEmail(signUpRequest.getEmail())).thenReturn(true);
        when(userRepository.findByEmail(signUpRequest.getEmail())).thenReturn(existingUser);

        // Act
        User result = authService.registerUser(signUpRequest);

        // Assert
        assertNotNull(result);
        assertEquals(existingUser, result);
        verify(emailService).sendVerificationEmail(existingUser);
    }

    @Test
    void verifyAccount_Success() {
        // Arrange
        String token = "valid-token";
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setEnabled(false);
        user.setEmailVerified(false);

        com.auth.service.entity.AccountVerification verification = new com.auth.service.entity.AccountVerification();
        verification.setUser(user);
        verification.setToken(token);

        when(tokenService.validateVerificationToken(token)).thenReturn(verification);

        // Act
        boolean result = authService.verifyAccount(token);

        // Assert
        assertTrue(result);
        assertTrue(user.isEnabled());
        assertTrue(user.isEmailVerified());
        verify(userRepository).save(user);
        verify(tokenService).deleteVerificationToken(verification);
    }
}
