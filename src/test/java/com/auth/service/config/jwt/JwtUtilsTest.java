package com.auth.service.config.jwt;

import com.auth.service.entity.Role;
import com.auth.service.entity.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JwtUtilsTest {

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private JwtUtils jwtUtils;

    private User user;
    private String testSecret = "ZGVmYXVsdFNlY3JldEtleUZvclRlc3RpbmdKd3RUb2tlbkdlbmVyYXRpb25BbmRWYWxpZGF0aW9u";

    @BeforeEach
    void setUp() {
        // Set up test user
        user = new User();
        user.setId(1L);
        user.setEmail("<EMAIL>");
        user.setFirstName("Test");
        user.setLastName("User");
        
        Role role = new Role();
        role.setName("USER");
        user.setRoles(Collections.singletonList(role));
        
        // Configure JWT properties
        when(jwtProperties.getSecret()).thenReturn(testSecret);
        when(jwtProperties.getExpiration()).thenReturn(3600000L);
        when(jwtProperties.getIssuer()).thenReturn("test-issuer");
        when(jwtProperties.getAudience()).thenReturn("test-audience");
        
        // Initialize the signing key
        jwtUtils.init();
    }

    @Test
    void generateJwtToken_Success() {
        // Act
        String token = jwtUtils.generateJwtToken(user);
        
        // Assert
        assertNotNull(token);
        assertTrue(token.length() > 0);
    }

    @Test
    void validateJwtToken_Success() {
        // Arrange
        String token = jwtUtils.generateJwtToken(user);
        
        // Act
        boolean isValid = jwtUtils.validateJwtToken(token);
        
        // Assert
        assertTrue(isValid);
    }

    @Test
    void getEmailFromJwtToken_Success() {
        // Arrange
        String token = jwtUtils.generateJwtToken(user);
        
        // Act
        String email = jwtUtils.getEmailFromJwtToken(token);
        
        // Assert
        assertEquals(user.getEmail(), email);
    }

    @Test
    void getClaimFromToken_Success() {
        // Arrange
        String token = jwtUtils.generateJwtToken(user);
        
        // Act
        Object userId = jwtUtils.getClaimFromToken(token, "userId");
        
        // Assert
        assertEquals(user.getId(), userId);
    }
}
