package com.auth.service.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Tests for database initialization.
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatabaseInitializationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    void testDatabaseInitialization() {
        // Check if tables exist
        Integer userTableCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'users'",
                Integer.class);
        
        Integer roleTableCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'roles'",
                Integer.class);
        
        // Assert tables exist
        assertEquals(1, userTableCount);
        assertEquals(1, roleTableCount);
        
        // Check if otp_code column exists in account_verifications table
        Integer otpColumnCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() " +
                        "AND table_name = 'account_verifications' AND column_name = 'otp_code'",
                Integer.class);
        
        // Assert otp_code column exists
        assertEquals(1, otpColumnCount);
    }
}
