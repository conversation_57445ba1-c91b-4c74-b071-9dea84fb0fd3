package com.auth.service.controller;

import com.auth.service.dto.request.LoginRequest;
import com.auth.service.dto.request.SignUpRequest;
import com.auth.service.dto.response.ApiResponse;
import com.auth.service.dto.response.AuthResponse;
import com.auth.service.entity.User;
import com.auth.service.exception.AuthenticationException;
import com.auth.service.exception.UserAlreadyExistsException;
import com.auth.service.service.auth.AuthService;
import com.auth.service.service.user.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(AuthController.class)
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AuthService authService;

    @MockBean
    private UserService userService;

    private SignUpRequest signUpRequest;
    private LoginRequest loginRequest;
    private AuthResponse authResponse;

    @BeforeEach
    void setUp() {
        signUpRequest = new SignUpRequest();
        signUpRequest.setFirstName("John");
        signUpRequest.setLastName("Doe");
        signUpRequest.setEmail("<EMAIL>");
        signUpRequest.setPassword("Password123!");

        loginRequest = new LoginRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("Password123!");

        authResponse = AuthResponse.builder()
                .accessToken("test-access-token")
                .refreshToken("test-refresh-token")
                .expirationTime(LocalDateTime.now().plusHours(1))
                .tokenType("Bearer")
                .userId(1L)
                .email("<EMAIL>")
                .roles("USER")
                .build();
    }

    @Test
    @WithMockUser
    void register_Success() throws Exception {
        // Arrange
        User user = new User();
        user.setEmail(signUpRequest.getEmail());
        when(authService.registerUser(any(SignUpRequest.class))).thenReturn(user);

        // Act & Assert
        mockMvc.perform(post("/api/auth/register")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.message").value("User registered successfully. Please check your email for verification."));

        verify(authService).registerUser(any(SignUpRequest.class));
    }

    @Test
    @WithMockUser
    void register_UserAlreadyExists() throws Exception {
        // Arrange
        when(authService.registerUser(any(SignUpRequest.class)))
                .thenThrow(new UserAlreadyExistsException("User already exists with email: " + signUpRequest.getEmail()));

        // Act & Assert
        mockMvc.perform(post("/api/auth/register")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value("error"))
                .andExpect(jsonPath("$.message").value("User already exists with email: " + signUpRequest.getEmail()));

        verify(authService).registerUser(any(SignUpRequest.class));
    }

    @Test
    @WithMockUser
    void login_Success() throws Exception {
        // Arrange
        when(authService.login(any(LoginRequest.class), any())).thenReturn(authResponse);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.message").value("Login successful"))
                .andExpect(jsonPath("$.data.accessToken").value(authResponse.getAccessToken()))
                .andExpect(jsonPath("$.data.refreshToken").value(authResponse.getRefreshToken()))
                .andExpect(jsonPath("$.data.tokenType").value(authResponse.getTokenType()))
                .andExpect(jsonPath("$.data.userId").value(authResponse.getUserId()))
                .andExpect(jsonPath("$.data.email").value(authResponse.getEmail()))
                .andExpect(jsonPath("$.data.roles").value(authResponse.getRoles()));

        verify(authService).login(any(LoginRequest.class), any());
    }

    @Test
    @WithMockUser
    void login_Failure() throws Exception {
        // Arrange
        when(authService.login(any(LoginRequest.class), any()))
                .thenThrow(new AuthenticationException("Invalid email or password"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value("error"))
                .andExpect(jsonPath("$.message").value("Invalid email or password"));

        verify(authService).login(any(LoginRequest.class), any());
    }
}
