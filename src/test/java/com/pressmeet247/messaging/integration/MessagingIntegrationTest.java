package com.pressmeet247.messaging.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.auth.repository.RoleRepository;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.messaging.dto.SendMessageRequest;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageType;
import com.pressmeet247.messaging.repository.MessageLogRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for messaging functionality.
 */
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY)
@ActiveProfiles("test")
@Transactional
class MessagingIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private MessageLogRepository messageLogRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private User adminUser;
    private User testUser;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        // Create admin role
        Role adminRole = new Role();
        adminRole.setName("ADMIN");
        adminRole.setDescription("Administrator role");
        adminRole = roleRepository.save(adminRole);

        // Create user role
        Role userRole = new Role();
        userRole.setName("USER");
        userRole.setDescription("Regular user role");
        userRole = roleRepository.save(userRole);

        // Create admin user
        adminUser = new User();
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setEmail("<EMAIL>");
        adminUser.setPassword(passwordEncoder.encode("password"));
        adminUser.setEnabled(true);
        adminUser.setEmailVerified(true);
        adminUser.setUserType(UserType.INDIVIDUAL);
        adminUser.setRoles(List.of(adminRole));
        adminUser = userRepository.save(adminUser);

        // Create test user
        testUser = new User();
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setEmail("<EMAIL>");
        testUser.setPhoneNumber("+1234567890");
        testUser.setPassword(passwordEncoder.encode("password"));
        testUser.setEnabled(true);
        testUser.setEmailVerified(true);
        testUser.setPhoneVerified(true);
        testUser.setUserType(UserType.STUDENT);
        testUser.setRoles(List.of(userRole));
        testUser = userRepository.save(testUser);
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSendEmailToSingleUser() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.EMAIL);
        request.setMessageType(MessageType.ANNOUNCEMENT);
        request.setSubject("Test Email");
        request.setContent("This is a test email message.");
        request.setTargetType(SendMessageRequest.TargetType.SINGLE_USER);
        request.setRecipientUserId(testUser.getId());

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Message(s) sent successfully"))
                .andExpect(jsonPath("$.data.channel").value("EMAIL"))
                .andExpect(jsonPath("$.data.messageType").value("ANNOUNCEMENT"))
                .andExpect(jsonPath("$.data.subject").value("Test Email"))
                .andExpect(jsonPath("$.data.content").value("This is a test email message."))
                .andExpect(jsonPath("$.data.recipientUserId").value(testUser.getId()));
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSendWhatsAppToSingleUser() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.WHATSAPP);
        request.setMessageType(MessageType.NOTIFICATION);
        request.setContent("This is a test WhatsApp message.");
        request.setTargetType(SendMessageRequest.TargetType.SINGLE_USER);
        request.setRecipientUserId(testUser.getId());

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.channel").value("WHATSAPP"))
                .andExpect(jsonPath("$.data.recipientIdentifier").value("+1234567890"));
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSendBroadcastMessage() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.EMAIL);
        request.setMessageType(MessageType.ANNOUNCEMENT);
        request.setSubject("Broadcast Test");
        request.setContent("This is a broadcast message to all users.");
        request.setTargetType(SendMessageRequest.TargetType.ALL_USERS);

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].isBroadcast").value(true));
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSendMessageToUserType() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.EMAIL);
        request.setMessageType(MessageType.REMINDER);
        request.setSubject("Student Reminder");
        request.setContent("This is a reminder for all students.");
        request.setTargetType(SendMessageRequest.TargetType.USER_TYPE);
        request.setTargetUserType(UserType.STUDENT);

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testGetMessageHistory() throws Exception {
        mockMvc.perform(get("/api/admin/messaging/history")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testGetMessageStatistics() throws Exception {
        mockMvc.perform(get("/api/admin/messaging/statistics"))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalMessages").isNumber())
                .andExpect(jsonPath("$.data.sentMessages").isNumber())
                .andExpect(jsonPath("$.data.emailMessages").isNumber())
                .andExpect(jsonPath("$.data.whatsappMessages").isNumber());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testGetActiveUsers() throws Exception {
        mockMvc.perform(get("/api/admin/users/active")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testGetUsersByType() throws Exception {
        mockMvc.perform(get("/api/admin/users/by-type/STUDENT")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSearchUsers() throws Exception {
        mockMvc.perform(get("/api/admin/users/search")
                .param("searchTerm", "test")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"USER"})
    void testSendMessage_AccessDenied_ForNonAdmin() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.EMAIL);
        request.setMessageType(MessageType.ANNOUNCEMENT);
        request.setSubject("Test");
        request.setContent("Test content");
        request.setTargetType(SendMessageRequest.TargetType.SINGLE_USER);
        request.setRecipientUserId(testUser.getId());

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = {"ADMIN"})
    void testSendMessage_ValidationError_MissingContent() throws Exception {
        SendMessageRequest request = new SendMessageRequest();
        request.setChannel(MessageChannel.EMAIL);
        request.setMessageType(MessageType.ANNOUNCEMENT);
        request.setSubject("Test");
        // Missing content
        request.setTargetType(SendMessageRequest.TargetType.SINGLE_USER);
        request.setRecipientUserId(testUser.getId());

        mockMvc.perform(post("/api/admin/messaging/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}
