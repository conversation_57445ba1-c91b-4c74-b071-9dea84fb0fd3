package com.pressmeet247.messaging.service;

import com.pressmeet247.auth.entity.Role;
import com.pressmeet247.auth.entity.User;
import com.pressmeet247.auth.enums.UserType;
import com.pressmeet247.auth.repository.UserRepository;
import com.pressmeet247.email.service.EmailService;
import com.pressmeet247.messaging.entity.MessageLog;
import com.pressmeet247.messaging.enums.MessageChannel;
import com.pressmeet247.messaging.enums.MessageStatus;
import com.pressmeet247.messaging.enums.MessageType;
import com.pressmeet247.messaging.repository.MessageLogRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for UnifiedMessagingService.
 */
@ExtendWith(MockitoExtension.class)
class UnifiedMessagingServiceTest {

    @Mock
    private EmailService emailService;

    @Mock
    private WhatsAppService whatsAppService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private MessageLogRepository messageLogRepository;

    @InjectMocks
    private UnifiedMessagingService messagingService;

    private User adminUser;
    private User recipientUser1;
    private User recipientUser2;

    @BeforeEach
    void setUp() {
        // Create admin user
        adminUser = new User();
        adminUser.setId(1L);
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setEmail("<EMAIL>");
        adminUser.setEnabled(true);

        Role adminRole = new Role();
        adminRole.setName("ADMIN");
        adminUser.setRoles(List.of(adminRole));

        // Create recipient users
        recipientUser1 = new User();
        recipientUser1.setId(2L);
        recipientUser1.setFirstName("John");
        recipientUser1.setLastName("Doe");
        recipientUser1.setEmail("<EMAIL>");
        recipientUser1.setPhoneNumber("+1234567890");
        recipientUser1.setUserType(UserType.STUDENT);
        recipientUser1.setEnabled(true);

        recipientUser2 = new User();
        recipientUser2.setId(3L);
        recipientUser2.setFirstName("Jane");
        recipientUser2.setLastName("Smith");
        recipientUser2.setEmail("<EMAIL>");
        recipientUser2.setPhoneNumber("+1234567891");
        recipientUser2.setUserType(UserType.COMPANY);
        recipientUser2.setEnabled(true);
    }

    @Test
    void testSendMessageToUser_Email_Success() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(2L)).thenReturn(Optional.of(recipientUser1));
        when(messageLogRepository.save(any(MessageLog.class))).thenAnswer(invocation -> {
            MessageLog messageLog = invocation.getArgument(0);
            messageLog.setId(100L);
            return messageLog;
        });

        // Act
        MessageLog result = messagingService.sendMessageToUser(
                1L, 2L, MessageChannel.EMAIL, MessageType.ANNOUNCEMENT,
                "Test Subject", "Test Content"
        );

        // Assert
        assertNotNull(result);
        assertEquals(adminUser, result.getSenderAdmin());
        assertEquals(recipientUser1, result.getRecipientUser());
        assertEquals(MessageChannel.EMAIL, result.getChannel());
        assertEquals(MessageType.ANNOUNCEMENT, result.getMessageType());
        assertEquals("Test Subject", result.getSubject());
        assertEquals("Test Content", result.getContent());
        assertEquals("<EMAIL>", result.getRecipientIdentifier());
        assertEquals(MessageStatus.PENDING, result.getStatus());

        verify(messageLogRepository).save(any(MessageLog.class));
    }

    @Test
    void testSendMessageToUser_WhatsApp_Success() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(2L)).thenReturn(Optional.of(recipientUser1));
        when(messageLogRepository.save(any(MessageLog.class))).thenAnswer(invocation -> {
            MessageLog messageLog = invocation.getArgument(0);
            messageLog.setId(101L);
            return messageLog;
        });

        // Act
        MessageLog result = messagingService.sendMessageToUser(
                1L, 2L, MessageChannel.WHATSAPP, MessageType.NOTIFICATION,
                null, "WhatsApp Test Message"
        );

        // Assert
        assertNotNull(result);
        assertEquals(MessageChannel.WHATSAPP, result.getChannel());
        assertEquals("+1234567890", result.getRecipientIdentifier());
        assertEquals("WhatsApp Test Message", result.getContent());

        verify(messageLogRepository).save(any(MessageLog.class));
    }

    @Test
    void testSendMessageToMultipleUsers_Success() {
        // Arrange
        List<Long> recipientIds = Arrays.asList(2L, 3L);
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findAllById(recipientIds)).thenReturn(Arrays.asList(recipientUser1, recipientUser2));
        when(messageLogRepository.save(any(MessageLog.class))).thenAnswer(invocation -> {
            MessageLog messageLog = invocation.getArgument(0);
            messageLog.setId(System.currentTimeMillis()); // Simulate different IDs
            return messageLog;
        });

        // Act
        List<MessageLog> results = messagingService.sendMessageToMultipleUsers(
                1L, recipientIds, MessageChannel.EMAIL, MessageType.MARKETING,
                "Marketing Subject", "Marketing Content"
        );

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        
        MessageLog log1 = results.get(0);
        MessageLog log2 = results.get(1);
        
        assertTrue(log1.isBroadcast());
        assertTrue(log2.isBroadcast());
        assertEquals(log1.getBroadcastId(), log2.getBroadcastId());
        
        verify(messageLogRepository, times(2)).save(any(MessageLog.class));
    }

    @Test
    void testSendBroadcastMessage_Success() {
        // Arrange
        List<User> allUsers = Arrays.asList(recipientUser1, recipientUser2);
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findAll()).thenReturn(allUsers);
        when(messageLogRepository.save(any(MessageLog.class))).thenAnswer(invocation -> {
            MessageLog messageLog = invocation.getArgument(0);
            messageLog.setId(System.currentTimeMillis());
            return messageLog;
        });

        // Act
        List<MessageLog> results = messagingService.sendBroadcastMessage(
                1L, MessageChannel.EMAIL, MessageType.ANNOUNCEMENT,
                "Broadcast Subject", "Broadcast Content"
        );

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        
        for (MessageLog log : results) {
            assertTrue(log.isBroadcast());
            assertEquals(MessageType.ANNOUNCEMENT, log.getMessageType());
            assertEquals("Broadcast Content", log.getContent());
        }
        
        verify(messageLogRepository, times(2)).save(any(MessageLog.class));
    }

    @Test
    void testSendBroadcastMessageToUserType_Success() {
        // Arrange
        List<User> studentUsers = Arrays.asList(recipientUser1);
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findByUserType(UserType.STUDENT)).thenReturn(studentUsers);
        when(messageLogRepository.save(any(MessageLog.class))).thenAnswer(invocation -> {
            MessageLog messageLog = invocation.getArgument(0);
            messageLog.setId(200L);
            return messageLog;
        });

        // Act
        List<MessageLog> results = messagingService.sendBroadcastMessageToUserType(
                1L, UserType.STUDENT, MessageChannel.EMAIL, MessageType.REMINDER,
                "Student Reminder", "Don't forget your assignment"
        );

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        
        MessageLog log = results.get(0);
        assertEquals(recipientUser1, log.getRecipientUser());
        assertEquals(MessageType.REMINDER, log.getMessageType());
        assertTrue(log.isBroadcast());
        
        verify(userRepository).findByUserType(UserType.STUDENT);
        verify(messageLogRepository).save(any(MessageLog.class));
    }

    @Test
    void testGetMessageHistory_Success() {
        // Arrange
        MessageLog messageLog = new MessageLog();
        messageLog.setId(1L);
        messageLog.setSenderAdmin(adminUser);
        
        Page<MessageLog> messagePage = new PageImpl<>(Arrays.asList(messageLog));
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(messageLogRepository.findBySenderAdmin(eq(adminUser), any(Pageable.class)))
                .thenReturn(messagePage);

        // Act
        Page<MessageLog> result = messagingService.getMessageHistory(1L, Pageable.unpaged());

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(messageLog, result.getContent().get(0));
        
        verify(messageLogRepository).findBySenderAdmin(eq(adminUser), any(Pageable.class));
    }

    @Test
    void testGetReceivedMessages_Success() {
        // Arrange
        MessageLog messageLog = new MessageLog();
        messageLog.setId(1L);
        messageLog.setRecipientUser(recipientUser1);
        
        Page<MessageLog> messagePage = new PageImpl<>(Arrays.asList(messageLog));
        when(userRepository.findById(2L)).thenReturn(Optional.of(recipientUser1));
        when(messageLogRepository.findByRecipientUser(eq(recipientUser1), any(Pageable.class)))
                .thenReturn(messagePage);

        // Act
        Page<MessageLog> result = messagingService.getReceivedMessages(2L, Pageable.unpaged());

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(messageLog, result.getContent().get(0));
        
        verify(messageLogRepository).findByRecipientUser(eq(recipientUser1), any(Pageable.class));
    }

    @Test
    void testGetMessageStatistics_Success() {
        // Arrange
        when(messageLogRepository.count()).thenReturn(100L);
        when(messageLogRepository.countByStatus(MessageStatus.SENT)).thenReturn(90L);
        when(messageLogRepository.countByStatus(MessageStatus.DELIVERED)).thenReturn(85L);
        when(messageLogRepository.countByStatus(MessageStatus.FAILED)).thenReturn(10L);
        when(messageLogRepository.countByChannel(MessageChannel.EMAIL)).thenReturn(70L);
        when(messageLogRepository.countByChannel(MessageChannel.WHATSAPP)).thenReturn(30L);

        // Act
        UnifiedMessagingService.MessageStatistics result = messagingService.getMessageStatistics();

        // Assert
        assertNotNull(result);
        assertEquals(100L, result.totalMessages);
        assertEquals(90L, result.sentMessages);
        assertEquals(85L, result.deliveredMessages);
        assertEquals(10L, result.failedMessages);
        assertEquals(70L, result.emailMessages);
        assertEquals(30L, result.whatsappMessages);
    }

    @Test
    void testSendMessageToUser_UserNotFound_ThrowsException() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            messagingService.sendMessageToUser(
                    1L, 999L, MessageChannel.EMAIL, MessageType.ANNOUNCEMENT,
                    "Test Subject", "Test Content"
            );
        });
    }

    @Test
    void testSendMessageToUser_AdminNotFound_ThrowsException() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            messagingService.sendMessageToUser(
                    999L, 2L, MessageChannel.EMAIL, MessageType.ANNOUNCEMENT,
                    "Test Subject", "Test Content"
            );
        });
    }
}
