<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    
    <div class="container">
        <h2>Login</h2>
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="Aa12345678@">
        </div>
        <div style="margin-top: 10px;">
            <button onclick="login()">Login</button>
        </div>
        <div id="loginResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Response</h2>
        <pre id="responseData"></pre>
    </div>
    
    <div class="container">
        <h2>Stored Tokens</h2>
        <pre id="storedTokens"></pre>
        <button onclick="clearTokens()">Clear Tokens</button>
    </div>

    <script>
        // Display any stored tokens on page load
        document.addEventListener('DOMContentLoaded', function() {
            displayStoredTokens();
        });

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginResult = document.getElementById('loginResult');
            
            if (!email || !password) {
                loginResult.innerHTML = '<span class="error">Please enter email and password</span>';
                return;
            }
            
            loginResult.innerHTML = 'Logging in...';
            
            fetch('http://localhost:8080/api/v1/authentication/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success' && data.data) {
                    // Store tokens in localStorage
                    localStorage.setItem('accessToken', data.data.accessToken);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('tokenType', data.data.tokenType);
                    localStorage.setItem('userId', data.data.userId);
                    localStorage.setItem('email', data.data.email);
                    localStorage.setItem('roles', data.data.roles);
                    
                    loginResult.innerHTML = '<span class="success">Login successful! Tokens stored.</span>';
                    displayStoredTokens();
                } else {
                    loginResult.innerHTML = `<span class="error">Login failed: ${data.message}</span>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loginResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            });
        }
        
        function displayStoredTokens() {
            const storedTokens = document.getElementById('storedTokens');
            const accessToken = localStorage.getItem('accessToken');
            const refreshToken = localStorage.getItem('refreshToken');
            
            if (accessToken && refreshToken) {
                const tokenData = {
                    accessToken: accessToken.substring(0, 20) + '...',
                    refreshToken: refreshToken.substring(0, 10) + '...',
                    tokenType: localStorage.getItem('tokenType'),
                    userId: localStorage.getItem('userId'),
                    email: localStorage.getItem('email'),
                    roles: localStorage.getItem('roles')
                };
                
                storedTokens.textContent = JSON.stringify(tokenData, null, 2);
            } else {
                storedTokens.textContent = 'No tokens stored';
            }
        }
        
        function clearTokens() {
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('tokenType');
            localStorage.removeItem('userId');
            localStorage.removeItem('email');
            localStorage.removeItem('roles');
            
            displayStoredTokens();
            document.getElementById('loginResult').innerHTML = '<span class="success">Tokens cleared</span>';
        }
    </script>
</body>
</html>
