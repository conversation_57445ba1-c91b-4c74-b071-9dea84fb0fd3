# Admin Authentication Process Documentation

This document provides a comprehensive overview of the admin authentication process in the PressMeet247 application. It includes all relevant code snippets and explains how different components are connected.

## Table of Contents

1. [Authentication Flow Overview](#authentication-flow-overview)
2. [Admin Login Process](#admin-login-process)
3. [OTP Verification Process](#otp-verification-process)
4. [Authentication Context](#authentication-context)
5. [API Requests](#api-requests)
6. [Admin Layout and Protected Routes](#admin-layout-and-protected-routes)
7. [Dashboard Page](#dashboard-page)
8. [Current Issues](#current-issues)
9. [Recommendations](#recommendations)

## Authentication Flow Overview

The admin authentication process follows these steps:

1. <PERSON><PERSON> enters email on the login page
2. Backend sends OTP to the admin's email
3. <PERSON><PERSON> enters OTP on the verification page
4. Backend verifies OTP and returns a JWT token
5. Frontend stores the token and authentication flags
6. Admin is redirected to the dashboard
7. All subsequent API requests include the JWT token

## Admin Login Process

### AdminLoginPage.jsx

```jsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { initiateAdminLogin } from '../../api/adminAuthApi';
import { ROUTES } from '../../config';
import './AdminLoginPage.css';

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Call the API to initiate login
      const response = await initiateAdminLogin(email);

      if (response && response.success) {
        // Store email in session storage for OTP verification
        sessionStorage.setItem('adminEmail', email);

        // Redirect to OTP verification page
        navigate(ROUTES.ADMIN_OTP_VERIFICATION);
      } else {
        setError('Failed to initiate login. Please try again.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-auth-container">
      <div className="admin-auth-card">
        <div className="admin-auth-logo">
          <i className="bi bi-newspaper"></i>
          <span>Press<span className="highlight">Meet</span></span>
        </div>
        <h1 className="admin-auth-title">Admin Login</h1>
        <p className="admin-auth-subtitle">Enter your email to receive a one-time password</p>

        {error && <div className="admin-auth-error">{error}</div>}

        <form onSubmit={handleSubmit} className="admin-auth-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <div className="input-with-icon">
              <i className="bi bi-envelope"></i>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                disabled={isLoading}
              />
            </div>
          </div>

          <button
            type="submit"
            className="admin-auth-button"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading-spinner-small"></span>
            ) : (
              'Continue'
            )}
          </button>
        </form>

        <div className="admin-auth-links">
          <Link to={ROUTES.HOME}>← Back to Website</Link>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginPage;
```

## OTP Verification Process

### AdminOTPVerificationPage.jsx

```jsx
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { verifyAdminLoginOTP } from '../../api/adminAuthApi';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { ROUTES } from '../../config';
import './AdminLoginPage.css';

const AdminOTPVerificationPage = () => {
  const [otp, setOtp] = useState('');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(60);
  const navigate = useNavigate();
  const { setAdminUserData } = useAdminAuth();

  // Get email from session storage
  useEffect(() => {
    const storedEmail = sessionStorage.getItem('adminEmail');
    if (!storedEmail) {
      // Redirect to login if email is not found
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    } else {
      setEmail(storedEmail);
    }
  }, [navigate]);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!otp) {
      setError('Please enter the OTP sent to your email');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Call the API to verify OTP
      const apiResponse = await verifyAdminLoginOTP(email, otp);

      if (apiResponse && apiResponse.success) {
        console.log('OTP verification successful');

        // Set admin user data in context
        setAdminUserData({
          email: email,
          role: 'ADMIN'
        });

        console.log('Admin authentication successful, redirecting to dashboard...');

        // Check if the API response included a token
        if (apiResponse.data && apiResponse.data.token) {
          console.log('Token received from API response, storing for Authorization header');
          localStorage.setItem('adminToken', apiResponse.data.token);
        } else {
          console.log('No token in API response data object, will rely on HTTP-only cookies');
        }

        // Set authentication flags for UI state management
        localStorage.setItem('admin_authenticated', 'true');
        localStorage.setItem('userRole', 'admin');

        console.log('Admin authentication flag set, redirecting to dashboard...');

        // Add a small delay before redirecting to ensure cookies are properly set
        setTimeout(() => {
          console.log('Redirecting to admin dashboard...');
          window.location.href = '/admin/dashboard';
        }, 500);
      } else {
        setError('Failed to verify OTP. Please try again.');
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      setError(err.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Call the API to resend OTP
      const response = await initiateAdminLogin(email);

      if (response && response.success) {
        setCountdown(60);
        alert('A new OTP has been sent to your email');
      } else {
        setError('Failed to resend OTP. Please try again.');
      }
    } catch (err) {
      console.error('Resend OTP error:', err);
      setError(err.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-auth-container">
      <div className="admin-auth-card">
        <div className="admin-auth-logo">
          <i className="bi bi-newspaper"></i>
          <span>Press<span className="highlight">Meet</span></span>
        </div>
        <h1 className="admin-auth-title">Verify OTP</h1>
        <p className="admin-auth-subtitle">
          Enter the one-time password sent to<br />
          <strong>{email}</strong>
        </p>

        {error && <div className="admin-auth-error">{error}</div>}

        <form onSubmit={handleSubmit} className="admin-auth-form">
          <div className="form-group">
            <label htmlFor="otp">One-Time Password</label>
            <div className="input-with-icon">
              <i className="bi bi-shield-lock"></i>
              <input
                type="text"
                id="otp"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter OTP"
                disabled={isLoading}
                maxLength={6}
              />
            </div>
          </div>

          <button
            type="submit"
            className="admin-auth-button"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading-spinner-small"></span>
            ) : (
              'Verify & Login'
            )}
          </button>
        </form>

        <div className="admin-auth-links">
          <div>
            {countdown > 0 ? (
              <span>Resend OTP in {countdown}s</span>
            ) : (
              <button
                onClick={handleResendOTP}
                className="text-button"
                disabled={isLoading}
              >
                Resend OTP
              </button>
            )}
          </div>
          <Link to={ROUTES.ADMIN_LOGIN}>← Back to Login</Link>
        </div>
      </div>
    </div>
  );
};

export default AdminOTPVerificationPage;
```

## Authentication Context

### AdminAuthContext.jsx

```jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { getCurrentAdmin, adminLogout, refreshAdminToken } from '../api/adminAuthApi';

// Create context
const AdminAuthContext = createContext();

// Provider component
export const AdminAuthProvider = ({ children }) => {
  const [adminUser, setAdminUser] = useState(null);
  const [isAdminAuthenticated, setIsAdminAuthenticated] = useState(false);
  const [isAdminLoading, setIsAdminLoading] = useState(true);
  const [adminError, setAdminError] = useState(null);

  // Check if admin is authenticated on mount
  useEffect(() => {
    // Create a flag to track if the component is mounted
    let isMounted = true;

    const checkAdminAuth = async () => {
      // With HTTP-only cookies, we can't directly check if the cookie exists
      // We'll use the admin_authenticated flag as a UI indicator only
      const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';

      console.log('AdminAuthContext - Checking authentication:');
      console.log('- admin_authenticated flag:', isAuthenticated);

      // If not authenticated according to UI flag, exit early
      if (!isAuthenticated) {
        console.log('No admin authentication found based on UI flag');
        if (isMounted) setIsAdminLoading(false);
        return;
      }

      // Set authenticated state based on UI flag
      // The actual authentication will be verified when we make API calls
      if (isMounted) setIsAdminAuthenticated(true);

      try {
        if (isMounted) setIsAdminLoading(true);

        // Limit API calls to prevent infinite loops
        // Only make one API call to verify authentication
        try {
          // Try to get admin user data
          const response = await getCurrentAdmin();

          // Only update state if component is still mounted
          if (!isMounted) return;

          if (response && response.data) {
            console.log('Admin user data retrieved successfully');
            setAdminUser(response.data);
          } else {
            console.log('No admin user data returned, creating basic user object');
            // If we can't get user data but have a token, create a basic user object
            setAdminUser({
              email: localStorage.getItem('adminEmail') || '<EMAIL>',
              role: 'ADMIN'
            });
          }
        } catch (apiError) {
          // Only update state if component is still mounted
          if (!isMounted) return;

          console.error('Error fetching admin user data:', apiError);

          // Handle authentication errors
          if (apiError.response && apiError.response.status === 401) {
            console.log('Received 401 Unauthorized - clearing admin authentication state');
            setAdminError('Session expired. Please log in again.');
            setIsAdminAuthenticated(false);
            localStorage.removeItem('admin_authenticated');
            localStorage.removeItem('userRole');
          }
        }
      } catch (error) {
        // Only update state if component is still mounted
        if (!isMounted) return;

        console.error('Error checking admin authentication:', error);

        // For other errors, keep the user logged in but show an error
        setAdminError('Failed to load admin data. Please refresh the page.');
      } finally {
        // Only update state if component is still mounted
        if (isMounted) setIsAdminLoading(false);
      }
    };

    // Call the check function
    checkAdminAuth();

    // Cleanup function to set the mounted flag to false when the component unmounts
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array means this runs once on mount

  // Admin logout function
  const handleAdminLogout = async () => {
    try {
      // Call the API to clear the HTTP-only cookie on the server
      await adminLogout();

      // Clear UI state
      setAdminUser(null);
      setIsAdminAuthenticated(false);

      // Remove UI flags
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('userRole');

      return { success: true };
    } catch (error) {
      console.error('Error during admin logout:', error);

      // Even if the API call fails, clear UI state
      setAdminUser(null);
      setIsAdminAuthenticated(false);
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('userRole');

      setAdminError('Failed to log out. Please try again.');
      return { success: false, error };
    }
  };

  // Set admin user data after successful login
  const setAdminUserData = (userData) => {
    console.log('Setting admin user data:', userData);
    setAdminUser(userData);
    setIsAdminAuthenticated(true);

    // With HTTP-only cookies, we don't need to manage tokens in localStorage
    // We only need to set a flag for UI state management
    console.log('Setting admin_authenticated flag for UI state');
    localStorage.setItem('admin_authenticated', 'true');
  };

  // Admin token refresh function
  const handleAdminTokenRefresh = async () => {
    try {
      console.log('Attempting to refresh admin token via HTTP-only cookie');
      const response = await refreshAdminToken();

      if (response && response.status === 'success') {
        console.log('Admin token refreshed successfully via HTTP-only cookie');
        return true;
      }

      console.log('Admin token refresh failed');
      return false;
    } catch (error) {
      console.error('Error refreshing admin token:', error);

      // Clear UI state on token refresh failure
      setAdminError('Session expired. Please log in again.');
      setIsAdminAuthenticated(false);
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('userRole');

      return false;
    }
  };

  // Context value
  const value = {
    adminUser,
    isAdminAuthenticated,
    isAdminLoading,
    adminError,
    setAdminUserData,
    adminLogout: handleAdminLogout,
    refreshAdminToken: handleAdminTokenRefresh,
    clearAdminError: () => setAdminError(null)
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Custom hook to use the admin auth context
export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};
```

## API Requests

### adminAuthApi.js

```jsx
import axiosInstance from '../utils/axiosConfig';

// Base URL for admin auth API
const ADMIN_AUTH_URL = '/api/admin/auth';

/**
 * Initiate admin login by sending OTP to email
 * @param {string} email - Admin email
 * @returns {Promise<Object>} - API response
 */
export const initiateAdminLogin = async (email) => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/login/initiate`, { email });
    return response.data;
  } catch (error) {
    console.error('Error initiating admin login:', error);
    throw error;
  }
};

/**
 * Verify admin login OTP
 * @param {string} email - Admin email
 * @param {string} otp - One-time password
 * @returns {Promise<Object>} - API response with token
 */
export const verifyAdminLoginOTP = async (email, otp) => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/login/verify`, { email, otp });
    return response.data;
  } catch (error) {
    console.error('Error verifying admin OTP:', error);
    throw error;
  }
};

/**
 * Get current admin user data
 * @returns {Promise<Object>} - Admin user data
 */
export const getCurrentAdmin = async () => {
  try {
    const response = await axiosInstance.get(`${ADMIN_AUTH_URL}/me`);
    return response.data;
  } catch (error) {
    console.error('Error getting current admin:', error);
    throw error;
  }
};

/**
 * Admin logout
 * @returns {Promise<Object>} - API response
 */
export const adminLogout = async () => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/logout`);
    return response.data;
  } catch (error) {
    console.error('Error logging out admin:', error);
    throw error;
  }
};

/**
 * Refresh admin token
 * @returns {Promise<Object>} - API response with new token
 */
export const refreshAdminToken = async () => {
  try {
    const response = await axiosInstance.post(`${ADMIN_AUTH_URL}/refresh-token`);
    return response.data;
  } catch (error) {
    console.error('Error refreshing admin token:', error);
    throw error;
  }
};
```

### axiosConfig.js

```jsx
import axios from 'axios';

// Create axios instance with base URL
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
  timeout: 10000,
  withCredentials: true // Important for HTTP-only cookies
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Check if the request is for an admin endpoint
    if (config.url.includes('/api/admin/')) {
      const adminToken = localStorage.getItem('adminToken');

      // If we have a token, add it to the Authorization header
      if (adminToken) {
        config.headers.Authorization = `Bearer ${adminToken}`;
        console.log('Adding admin token to request:', config.url);
      } else {
        console.log('No admin token found for request:', config.url);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 Unauthorized and the request is not a retry
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const response = await axios.post('/api/admin/auth/refresh-token', {}, { withCredentials: true });

        if (response.data.token) {
          // Update the token in localStorage
          localStorage.setItem('adminToken', response.data.token);

          // Update the Authorization header
          originalRequest.headers.Authorization = `Bearer ${response.data.token}`;

          // Retry the original request
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);

        // Clear authentication state
        localStorage.removeItem('admin_authenticated');
        localStorage.removeItem('userRole');

        // Redirect to login page
        window.location.href = '/admin/login';
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
```

## Admin Layout and Protected Routes

### AdminLayout.jsx

```jsx
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '../../config';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { getCurrentAdmin } from '../../api/adminAuthApi';
import '../styles/admin.css';

const AdminLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { isAdminAuthenticated, isAdminLoading, adminLogout } = useAdminAuth();

  // Check if user is authenticated as admin
  useEffect(() => {
    console.log('AdminLayout - Authentication check');

    // With HTTP-only cookies, we can only check the UI flag
    // The actual authentication will be verified when API calls are made
    const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';
    const hasToken = !!localStorage.getItem('adminToken');

    console.log('- isAdminAuthenticated (context):', isAdminAuthenticated);
    console.log('- isAuthenticated (localStorage):', isAuthenticated);
    console.log('- hasToken (localStorage):', hasToken);

    // If we're not authenticated and not loading, redirect to login
    if (!isAdminAuthenticated && !isAuthenticated && !isAdminLoading) {
      console.log('AdminLayout - Not authenticated, redirecting to login');
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    } else {
      console.log('AdminLayout - Authenticated, staying on dashboard');

      // We'll verify authentication only once when the component mounts
      // This prevents infinite loops of API calls
    }
  }, [isAdminAuthenticated, isAdminLoading, navigate]);

  // Verify authentication once on mount
  useEffect(() => {
    // Only run this effect once when the component mounts
    const verifyAuth = async () => {
      try {
        // Check if we're authenticated according to UI state
        const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';
        if (!isAuthenticated) return;

        console.log('Verifying admin authentication with /api/admin/auth/me');
        await getCurrentAdmin();
        console.log('Admin authentication verified successfully');
      } catch (error) {
        console.error('Error verifying admin authentication:', error);

        if (error.response && (error.response.status === 403 || error.response.status === 401)) {
          console.log('Authentication failed, redirecting to login');
          localStorage.removeItem('admin_authenticated');
          localStorage.removeItem('userRole');
          navigate(ROUTES.ADMIN_LOGIN, { replace: true });
        }
      }
    };

    verifyAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array means this runs once on mount

  // Close mobile sidebar when route changes
  useEffect(() => {
    setMobileOpen(false);
  }, [location]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 992) {
        setSidebarCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = async () => {
    try {
      console.log('Logging out admin user');

      // Use the adminLogout function from the context
      // This will call the API to clear the HTTP-only cookie on the server
      await adminLogout();

      // Clear UI state flags only (not tokens, as they're in HTTP-only cookies)
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('userRole');

      console.log('Admin logged out successfully');
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    } catch (error) {
      console.error('Error during logout:', error);

      // Still clear UI state flags even if the API call fails
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('userRole');

      // Navigate to login page even if logout fails
      navigate(ROUTES.ADMIN_LOGIN, { replace: true });
    }
  };

  // Check if menu item is active
  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="admin-container">
      {/* Sidebar */}
      <aside className={`admin-sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${mobileOpen ? 'open' : ''}`}>
        {/* Sidebar content */}
      </aside>

      {/* Main Content */}
      <main className={`admin-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        {/* Header */}
        <header className="admin-header">
          {/* Header content */}
        </header>

        {/* Content */}
        <div className="admin-content">
          {children}
        </div>
      </main>
    </div>
  );
};

export default AdminLayout;
```

### ProtectedAdminRoute.jsx

```jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAdminAuth } from '../context/AdminAuthContext';
import { ROUTES } from '../config';

const ProtectedAdminRoute = ({ children }) => {
  const { isAdminAuthenticated, isAdminLoading } = useAdminAuth();

  // Check if we have the authentication flag in localStorage
  const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';

  // If we're still loading, show a loading indicator
  if (isAdminLoading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  // If we're not authenticated, redirect to login
  if (!isAdminAuthenticated && !isAuthenticated) {
    return <Navigate to={ROUTES.ADMIN_LOGIN} replace />;
  }

  // If we're authenticated, render the children
  return children;
};

export default ProtectedAdminRoute;
```

## Dashboard Page

### DashboardPage.jsx

```jsx
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AdminLayout from '../layouts/AdminLayout';
import { ROUTES } from '../../config';
import { useAdminAuth } from '../../context/AdminAuthContext';
import '../styles/admin.css';

const DashboardPage = () => {
  const navigate = useNavigate();
  const { isAdminAuthenticated, isAdminLoading } = useAdminAuth();

  const [stats, setStats] = useState({
    totalNews: 0,
    pendingNews: 0,
    publishedNews: 0,
    rejectedNews: 0,
    totalUsers: 0,
    newUsers: 0,
    activeSubscriptions: 0,
    totalRevenue: 0,
    mrr: 0,
    trends: {
      news: 0,
      users: 0,
      subscriptions: 0,
      revenue: 0
    }
  });

  const [recentSubmissions, setRecentSubmissions] = useState([]);
  const [topUsers, setTopUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Check authentication on mount
  useEffect(() => {
    console.log('DashboardPage - Authentication check');

    // Check if we have the authentication flag
    const isAuthenticated = localStorage.getItem('admin_authenticated') === 'true';
    const hasToken = !!localStorage.getItem('adminToken');

    console.log('- isAdminAuthenticated (context):', isAdminAuthenticated);
    console.log('- isAuthenticated (localStorage):', isAuthenticated);
    console.log('- hasToken (localStorage):', hasToken);

    // If we're not authenticated and not loading, redirect to login
    if (!isAdminAuthenticated && !isAuthenticated && !isAdminLoading) {
      console.log('DashboardPage - Not authenticated, redirecting to login');
      navigate('/admin/login', { replace: true });
    } else {
      console.log('DashboardPage - Authenticated, staying on dashboard');
    }
  }, [isAdminAuthenticated, isAdminLoading, navigate]);

  // Fetch dashboard data
  useEffect(() => {
    // Create a flag to track if the component is mounted
    let isMounted = true;

    const fetchDashboardData = async () => {
      try {
        // Only set loading state if the component is still mounted
        if (isMounted) setIsLoading(true);

        // Import the API functions
        const { getDashboardStats } = await import('../../api/adminApi');
        const { getNewsByStatus } = await import('../../api/newsApi');

        // Fetch dashboard statistics
        const statsResponse = await getDashboardStats();
        console.log('Dashboard stats response:', statsResponse);

        // Only update state if the component is still mounted
        if (!isMounted) return;

        if (statsResponse && statsResponse.success && statsResponse.stats) {
          const { users, news, subscriptions, revenue } = statsResponse.stats;

          // Update stats with real data
          setStats({
            totalNews: news.total || 0,
            pendingNews: news.pending || 0,
            publishedNews: news.published || 0,
            rejectedNews: news.rejected || 0,
            totalUsers: users.total || 0,
            newUsers: users.regular || 0, // Using regular users as new users for now
            activeSubscriptions: subscriptions.active || 0,
            totalRevenue: revenue.total || 0,
            mrr: revenue.monthly || 0,
            trends: {
              news: news.trend || 5, // Default to 5% if not provided
              users: users.trend || 8, // Default to 8% if not provided
              subscriptions: subscriptions.trend || 12, // Default to 12% if not provided
              revenue: revenue.trend || 15 // Default to 15% if not provided
            }
          });
        }

        // Fetch recent submissions (pending news)
        const pendingNewsResponse = await getNewsByStatus('pending', { limit: 5 });
        console.log('Recent submissions response:', pendingNewsResponse);

        // Only update state if the component is still mounted
        if (!isMounted) return;

        if (pendingNewsResponse && pendingNewsResponse.success && pendingNewsResponse.news) {
          // Map the API response to the format expected by the component
          const recentNews = pendingNewsResponse.news.map(item => ({
            id: item.id,
            title: item.title,
            author: item.user ? item.user.name : 'Unknown',
            email: item.user ? item.user.email : '<EMAIL>',
            date: new Date(item.createdAt).toLocaleString(),
            status: item.status
          }));

          setRecentSubmissions(recentNews);
        }

        // Fetch top users
        try {
          const { getAllUsers } = await import('../../api/adminApi');
          const usersResponse = await getAllUsers({ limit: 10, sort: 'submissions' });
          console.log('Top users response:', usersResponse);

          // Only update state if the component is still mounted
          if (!isMounted) return;

          if (usersResponse && usersResponse.users) {
            // Map the API response to the format expected by the component
            const topUsersData = usersResponse.users
              .sort((a, b) => (b.newsSubmissions || 0) - (a.newsSubmissions || 0))
              .slice(0, 5)
              .map(user => ({
                id: user.id,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                submissions: user.newsSubmissions || 0,
                published: user.publishedNews || 0
              }));

            setTopUsers(topUsersData);
          }
        } catch (userError) {
          console.error("Error fetching top users:", userError);
          // Fallback to empty array if the API call fails
          if (isMounted) setTopUsers([]);
        }

        // Only update loading state if the component is still mounted
        if (isMounted) setIsLoading(false);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);

        // Only update state if the component is still mounted
        if (!isMounted) return;

        setIsLoading(false);

        // Set fallback data in case of error
        setStats({
          totalNews: 0,
          pendingNews: 0,
          publishedNews: 0,
          rejectedNews: 0,
          totalUsers: 0,
          newUsers: 0,
          activeSubscriptions: 0,
          totalRevenue: 0,
          mrr: 0,
          trends: {
            news: 0,
            users: 0,
            subscriptions: 0,
            revenue: 0
          }
        });
        setRecentSubmissions([]);
        setTopUsers([]);
      }
    };

    // Call the fetch function
    fetchDashboardData();

    // Cleanup function to set the mounted flag to false when the component unmounts
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array means this runs once on mount

  return (
    <AdminLayout>
      <div className="page-header">
        <h1 className="page-title">Dashboard</h1>
        <p className="page-subtitle">Welcome to PressMeet247 Admin Dashboard</p>
      </div>

      {isLoading ? (
        <div className="loading-spinner">Loading...</div>
      ) : (
        <>
          {/* Dashboard content */}
        </>
      )}
    </AdminLayout>
  );
};

export default DashboardPage;
```

## Current Issues

1. **Continuous Page Reloading**: The admin dashboard is experiencing continuous page reloading due to infinite API call loops.

2. **Authentication Token Handling**: There's confusion between using HTTP-only cookies and localStorage for token storage.

3. **API Endpoint Inconsistency**: The code tries multiple API endpoints when the primary one fails, indicating inconsistency in API design.

4. **Error Handling**: Error handling is inconsistent across components, leading to unpredictable behavior.

5. **Authentication State Management**: The authentication state is managed in multiple places (context, localStorage, sessionStorage), making it difficult to track.

## Recommendations

1. **Standardize Authentication Method**: Choose either HTTP-only cookies or localStorage for token storage, not both. HTTP-only cookies are more secure.

2. **Fix API Endpoint Structure**: Standardize API endpoints to follow a consistent pattern.

3. **Implement Proper Token Refresh**: Ensure token refresh mechanism works correctly and handles expired tokens gracefully.

4. **Improve Error Handling**: Implement consistent error handling across all components.

5. **Centralize Authentication Logic**: Move all authentication logic to the context provider to avoid duplication.

6. **Add Loading States**: Ensure proper loading states are shown during authentication checks and API calls.

7. **Implement Proper Cleanup**: Add cleanup functions to all useEffect hooks to prevent memory leaks and state updates after component unmount.

8. **Add Request/Response Logging**: Implement comprehensive logging for debugging authentication issues.

9. **Implement Session Timeout**: Add session timeout handling to improve security.

10. **Add Unit Tests**: Create unit tests for authentication flows to catch regressions.
