
# AI-Powered News Platform

A comprehensive, AI-powered news platform that enables users to submit local area news easily through an intuitive web interface. This system is designed to handle user-generated content, automate initial news generation using ChatGPT, and streamline administrative oversight, content approval, and publishing processes.

## Features

### 1. User Submission Flow
- Users must be authenticated to submit news
- Upon submission, the news content is processed using OpenAI's ChatGPT
- The generated news, along with the submitting user's ID, is stored in the database for further review

### 2. Admin Dashboard
- Admins have a dedicated backend dashboard to:
  - View all user-submitted and AI-generated news entries
  - Edit, approve, and publish news articles
  - Manage content publication visibility
  - Monitor system analytics, user activity, and content trends

### 3. Broadcast Messaging System
- Admins can send personalized or mass notifications such as:
  - General announcements
  - Event updates
  - Special offers
  - Premium membership invitations
  - Congratulatory messages to top contributors or individuals
- Broadcast targets can be:
  - All users
  - Premium members only
  - Specific users or user groups

### 4. Subscription Management
- The platform features a separate, robust subscription management module to distinguish between:
  - Free users
  - Premium members
- Premium users receive additional features such as early access to news, direct engagement, exclusive content, and priority in broadcast messages

### 5. Authentication & Security
- JWT-based authentication
- Role-based access control
- OAuth2 integration with social logins
- Email verification
- Password reset functionality

## Technology Stack

- **Backend**: Spring Boot 3.x
- **Security**: Spring Security 6.x
- **Database**: MySQL with Hibernate
- **AI Integration**: OpenAI API (Azure OpenAI Service)
- **Documentation**: Swagger/OpenAPI 3
- **Email**: Spring Mail with Thymeleaf templates
- **JWT**: JJWT library
- **Build Tool**: Maven
- **Frontend**: React.js (separate repository)

## Getting Started

### Prerequisites

- Java 17+
- Maven
- MySQL Database (XAMPP recommended)
- OpenAI API Key (Azure OpenAI Service)

### Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/yourusername/ai-news-platform.git
   cd ai-news-platform
   ```

2. **Configure application properties:**

   The application uses YAML configuration with environment variable support. You can:

   - Set environment variables directly
   - Modify `application.yml` for development purposes

   Key configuration properties:

   ```yaml
   # Database configuration
   DB_URL: *****************************************
   DB_USERNAME: root
   DB_PASSWORD: your_password

   # JWT configuration
   JWT_SECRET: your_secure_jwt_secret_key
   JWT_EXPIRATION: 3600000
   JWT_REFRESH_EXPIRATION: 86400000

   # Email configuration
   MAIL_HOST: smtp.gmail.com
   MAIL_PORT: 587
   MAIL_USERNAME: <EMAIL>
   MAIL_PASSWORD: your-app-password

   # OAuth2 configuration
   GOOGLE_CLIENT_ID: your-google-client-id
   GOOGLE_CLIENT_SECRET: your-google-client-secret
   FACEBOOK_CLIENT_ID: your-facebook-client-id
   FACEBOOK_CLIENT_SECRET: your-facebook-client-secret

   # Azure OpenAI configuration
   AZURE_API_URL: https://api.openai.com/v1/chat/completions
   AZURE_API_KEY: your-openai-api-key
   ```

   **Important:** Replace placeholder values with your actual configurations. Ensure sensitive data is not committed to version control.

3. **Build and Run the Application:**

   ```bash
   mvn clean package
   java -jar target/news-platform-1.0.0.jar
   ```

   Alternatively, use your IDE to run the main application class.

## API Documentation

Once the application is running, you can access the Swagger UI at:
```
http://localhost:8080/swagger-ui.html
```

## Key Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login and receive JWT tokens
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout and invalidate tokens
- `GET /api/auth/verify` - Verify user account
- `POST /api/auth/reset-password/request` - Request password reset
- `POST /api/auth/reset-password/confirm` - Confirm password reset

### News Management

- `POST /api/news/generate` - Generate news using AI (authenticated users only)
- `GET /api/news/published` - Get published news (public)
- `GET /api/news/featured` - Get featured news (public)
- `GET /api/news/type/{type}` - Get news by type (public)
- `GET /api/news/my-news` - Get news submitted by the authenticated user
- `GET /api/news/{id}` - Get news by ID

### Admin News Management

- `GET /api/news` - Get all news (admin only)
- `GET /api/news/status/{status}` - Get news by status (admin only)
- `POST /api/news/{id}/approve` - Approve news (admin only)
- `POST /api/news/{id}/reject` - Reject news (admin only)
- `PUT /api/news/{id}/content` - Update news content (admin only)
- `PUT /api/news/{id}/featured` - Set news as featured (admin only)

### Broadcast Messaging

- `POST /api/messages` - Create broadcast message (admin only)
- `GET /api/messages` - Get all broadcast messages (admin only)
- `GET /api/messages/my-messages` - Get messages for the authenticated user
- `GET /api/messages/unread-count` - Get unread message count
- `POST /api/messages/{id}/read` - Mark message as read

### Subscription Management

- `GET /api/subscriptions/plans/active` - Get active subscription plans
- `POST /api/subscriptions` - Create subscription
- `GET /api/subscriptions/active` - Get active subscription
- `POST /api/subscriptions/{id}/cancel` - Cancel subscription
- `POST /api/subscriptions/{id}/renew` - Renew subscription
- `GET /api/subscriptions/check-active` - Check if user has active subscription

## Integration with Frontend

This platform is designed to work with a React.js frontend. Here's how to integrate it:

1. **Authentication Flow**:
   - Frontend collects user details and sends to `/api/auth/register`
   - User receives verification email
   - User clicks verification link
   - Frontend handles the verification callback and redirects to login
   - Frontend stores JWT tokens securely
   - Frontend includes access token in Authorization header for API requests

2. **News Generation Flow**:
   - Authenticated users fill out the news submission form
   - Frontend sends data to `/api/news/generate`
   - Backend processes the request using AI and stores the generated news
   - User can view their submitted news in their dashboard

3. **Admin Dashboard Flow**:
   - Admin users can view all news submissions
   - Admins can approve, reject, or edit news content
   - Admins can feature selected news articles

4. **Subscription Flow**:
   - Users can view available subscription plans
   - Users can subscribe to premium plans
   - Premium users get access to additional features

## Testing with Postman

Here are examples of how to test the main endpoints using curl commands:

### Authentication

#### Register a new user
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "StrongPassword123!"
  }'
```

#### Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "StrongPassword123!"
  }'
```

### News Management

#### Generate News (Authenticated Users Only)
```bash
curl -X POST http://localhost:8080/api/news/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "date": "2023-04-15",
    "place": "City Hall",
    "whatHappened": "A community meeting was held to discuss the new park project",
    "whereHappened": "Main Conference Room, City Hall",
    "whoInvolved": "Mayor John Smith, City Council members, and local residents",
    "whenHappened": "7:00 PM",
    "whoTold": "City Press Office",
    "howHappened": "The meeting was conducted in a town hall format with presentations followed by Q&A",
    "whyHappened": "To gather community feedback on the proposed park design",
    "newsType": "Local"
  }'
```

#### Get Published News (Public)
```bash
curl -X GET http://localhost:8080/api/news/published
```

#### Get Featured News (Public)
```bash
curl -X GET http://localhost:8080/api/news/featured
```

### Admin News Management

#### Approve News
```bash
curl -X POST http://localhost:8080/api/news/1/approve \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

#### Reject News
```bash
curl -X POST http://localhost:8080/api/news/1/reject \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### Broadcast Messaging

#### Create Broadcast Message
```bash
curl -X POST http://localhost:8080/api/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -d '{
    "title": "Important Announcement",
    "content": "This is an important announcement for all users.",
    "type": "ANNOUNCEMENT",
    "targetAllUsers": true,
    "targetPremiumUsers": false,
    "specificUserIds": []
  }'
```

### Subscription Management

#### Create Subscription
```bash
curl -X POST http://localhost:8080/api/subscriptions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "planId": 1,
    "paymentMethod": "CREDIT_CARD",
    "paymentReference": "txn_1234567890"
  }'
```

## Security Considerations

- Store tokens securely (HttpOnly cookies or secure storage)
- Implement HTTPS in production
- Set appropriate CORS configuration
- Regularly rotate secrets and keys
- Monitor failed login attempts

## Troubleshooting

If you encounter any issues:

1. **Check the logs** for detailed error messages
2. **Verify database connection** in XAMPP
3. **Ensure all required services** are running
4. **Check token expiration** if authentication fails after some time
5. **Verify request format** matches the expected format

## License

This project is licensed under the MIT License - see the LICENSE file for details.
