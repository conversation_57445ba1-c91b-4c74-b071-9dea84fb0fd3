$directories = @(
    "src\main\java\com\pressmeet247\auth\controller",
    "src\main\java\com\pressmeet247\auth\service",
    "src\main\java\com\pressmeet247\auth\dto\request",
    "src\main\java\com\pressmeet247\auth\dto\response",
    "src\main\java\com\pressmeet247\auth\entity",
    "src\main\java\com\pressmeet247\auth\repository",
    "src\main\java\com\pressmeet247\auth\config",
    "src\main\java\com\pressmeet247\auth\exception",
    "src\main\java\com\pressmeet247\auth\util",
    
    "src\main\java\com\pressmeet247\admin\controller",
    "src\main\java\com\pressmeet247\admin\service",
    "src\main\java\com\pressmeet247\admin\dto",
    "src\main\java\com\pressmeet247\admin\entity",
    "src\main\java\com\pressmeet247\admin\repository",
    "src\main\java\com\pressmeet247\admin\config",
    
    "src\main\java\com\pressmeet247\ai_news\controller",
    "src\main\java\com\pressmeet247\ai_news\service",
    "src\main\java\com\pressmeet247\ai_news\dto",
    "src\main\java\com\pressmeet247\ai_news\entity",
    "src\main\java\com\pressmeet247\ai_news\repository",
    "src\main\java\com\pressmeet247\ai_news\impl",
    
    "src\main\java\com\pressmeet247\subscription\controller",
    "src\main\java\com\pressmeet247\subscription\service",
    "src\main\java\com\pressmeet247\subscription\dto",
    "src\main\java\com\pressmeet247\subscription\entity",
    "src\main\java\com\pressmeet247\subscription\repository",
    
    "src\main\java\com\pressmeet247\email\service",
    "src\main\java\com\pressmeet247\email\config",
    
    "src\main\java\com\pressmeet247\common\config",
    "src\main\java\com\pressmeet247\common\security",
    "src\main\java\com\pressmeet247\common\jwt",
    "src\main\java\com\pressmeet247\common\oauth2",
    "src\main\java\com\pressmeet247\common\exception",
    "src\main\java\com\pressmeet247\common\base",
    "src\main\java\com\pressmeet247\common\util"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir"
    } else {
        Write-Host "Directory already exists: $dir"
    }
}
