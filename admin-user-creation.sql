-- Insert admin role if it doesn't exist
INSERT INTO roles (name, description, is_default, priority, created_at, updated_at, deleted, version)
SELECT 'ADMIN', 'Administrator role with full access', 0, 100, NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'ADMIN');

-- Get the admin role ID
SET @admin_role_id = (SELECT id FROM roles WHERE name = 'ADMIN');

-- Insert admin user if it doesn't exist
INSERT INTO users (first_name, last_name, email, password, enabled, email_verified, created_at, updated_at, deleted, version)
SELECT 'Admin', 'User', '<EMAIL>', '$2a$10$Uj0Aw9Yx3jCkzG7Uo5As0.IfRcJCnZ0Hl4MvbVUXvCdkRZ5vXtGwC', 1, 1, NOW(), NOW(), 0, 0
WHERE NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- Get the admin user ID
SET @admin_user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Assign admin role to the user if not already assigned
INSERT INTO user_roles (user_id, role_id)
SELECT @admin_user_id, @admin_role_id
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE user_id = @admin_user_id AND role_id = @admin_role_id);
