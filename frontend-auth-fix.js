// Fix for frontend authentication issues

// 1. API Service - Make sure it handles cookies correctly
import axios from 'axios';

// Create an axios instance with the correct configuration
const api = axios.create({
  baseURL: '/api',
  withCredentials: true, // This is crucial for cookies to be sent with requests
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Admin Auth API service
const adminAuthApi = {
  // Login initiation
  initiateLogin: async (email, password) => {
    try {
      const response = await api.post('/admin/auth/login/initiate', { email, password });
      return response.data;
    } catch (error) {
      throw error.response?.data || { status: 'error', message: 'Login failed' };
    }
  },

  // OTP verification
  verifyOtp: async (email, password, otp) => {
    try {
      const response = await api.post('/admin/auth/login/verify', { email, password, otp });
      return response.data;
    } catch (error) {
      throw error.response?.data || { status: 'error', message: 'OTP verification failed' };
    }
  },

  // Get current admin user
  getCurrentAdmin: async () => {
    try {
      const response = await api.get('/admin/auth/me');
      return response.data;
    } catch (error) {
      throw error.response?.data || { status: 'error', message: 'Failed to get user data' };
    }
  },

  // Logout
  logout: async () => {
    try {
      const response = await api.post('/auth/logout');
      return response.data;
    } catch (error) {
      throw error.response?.data || { status: 'error', message: 'Logout failed' };
    }
  }
};

// 2. Authentication Context - Improved version
import { createContext, useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';

const AdminAuthContext = createContext(null);

export const AdminAuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [adminUser, setAdminUser] = useState(null);
  const [authError, setAuthError] = useState(null);
  
  // Check authentication status on mount and when isAuthenticated changes
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);
        setAuthError(null);
        
        const response = await adminAuthApi.getCurrentAdmin();
        
        if (response.status === 'success' && response.data) {
          console.log('Auth check successful, user data:', response.data);
          setIsAuthenticated(true);
          setAdminUser(response.data);
        } else {
          console.log('Auth check failed, no user data');
          setIsAuthenticated(false);
          setAdminUser(null);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        setAdminUser(null);
        setAuthError(error.message || 'Authentication check failed');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      setAuthError(null);
      const response = await adminAuthApi.initiateLogin(email, password);
      
      if (response.status === 'success') {
        return { success: true, message: response.message || 'OTP sent successfully' };
      } else {
        setAuthError(response.message || 'Login failed');
        return { success: false, message: response.message || 'Login failed' };
      }
    } catch (error) {
      setAuthError(error.message || 'Login failed');
      return { success: false, message: error.message || 'Login failed' };
    }
  };

  // Verify OTP function
  const verifyOtp = async (email, password, otp) => {
    try {
      setAuthError(null);
      const response = await adminAuthApi.verifyOtp(email, password, otp);
      
      if (response.status === 'success') {
        // Update auth state immediately after successful verification
        setIsAuthenticated(true);
        
        // Get user data
        try {
          const userResponse = await adminAuthApi.getCurrentAdmin();
          if (userResponse.status === 'success' && userResponse.data) {
            setAdminUser(userResponse.data);
          }
        } catch (userError) {
          console.error('Error fetching user data after login:', userError);
        }
        
        return { 
          success: true, 
          message: response.message || 'Login successful',
          redirectUrl: response.data?.redirectUrl || '/admin/dashboard'
        };
      } else {
        setAuthError(response.message || 'OTP verification failed');
        return { success: false, message: response.message || 'OTP verification failed' };
      }
    } catch (error) {
      setAuthError(error.message || 'OTP verification failed');
      return { success: false, message: error.message || 'OTP verification failed' };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const response = await adminAuthApi.logout();
      
      // Always clear auth state regardless of response
      setIsAuthenticated(false);
      setAdminUser(null);
      
      return { success: true, message: response.message || 'Logout successful' };
    } catch (error) {
      console.error('Logout error:', error);
      
      // Still clear auth state even if API call fails
      setIsAuthenticated(false);
      setAdminUser(null);
      
      return { success: false, message: error.message || 'Logout failed' };
    }
  };

  // Provide auth context
  const value = {
    isAuthenticated,
    isLoading,
    adminUser,
    authError,
    login,
    verifyOtp,
    logout,
    // Add a manual method to force refresh auth state
    refreshAuthState: async () => {
      try {
        setIsLoading(true);
        const response = await adminAuthApi.getCurrentAdmin();
        
        if (response.status === 'success' && response.data) {
          setIsAuthenticated(true);
          setAdminUser(response.data);
          return true;
        } else {
          setIsAuthenticated(false);
          setAdminUser(null);
          return false;
        }
      } catch (error) {
        setIsAuthenticated(false);
        setAdminUser(null);
        return false;
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

// 3. Protected Route Component - Fixed version
import { Navigate, useLocation } from 'react-router-dom';

export const ProtectedAdminRoute = ({ children }) => {
  const { isAuthenticated, isLoading, refreshAuthState } = useAdminAuth();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Add a state check to prevent infinite redirects
  const [checkedAuth, setCheckedAuth] = useState(false);
  
  useEffect(() => {
    // Only check once to prevent loops
    if (!checkedAuth) {
      const checkAuth = async () => {
        // Force refresh auth state
        const isAuth = await refreshAuthState();
        setCheckedAuth(true);
        
        if (!isAuth) {
          // Redirect with the current location as state
          navigate('/admin/login', { 
            state: { from: location.pathname },
            replace: true 
          });
        }
      };
      
      checkAuth();
    }
  }, [checkedAuth, refreshAuthState, navigate, location]);
  
  // Show loading state
  if (isLoading || !checkedAuth) {
    return <div className="loading-spinner">Loading...</div>;
  }
  
  // If authenticated, render children
  return isAuthenticated ? children : null;
};

// 4. Login Page Component - Fixed version
const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login, verifyOtp, isAuthenticated } = useAdminAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from || '/admin/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);
  
  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    
    try {
      const result = await login(email, password);
      
      if (result.success) {
        setShowOtpForm(true);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    
    try {
      const result = await verifyOtp(email, password, otp);
      
      if (result.success) {
        // The auth context will update isAuthenticated
        // which will trigger the redirect in the useEffect above
        console.log('OTP verification successful, redirecting to:', result.redirectUrl);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || 'OTP verification failed');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Login form JSX (same as before)
  
  return (
    <div className="admin-login-container">
      <h1>Admin Login</h1>
      
      {!showOtpForm ? (
        <form onSubmit={handleLogin}>
          {/* Email and password fields */}
          <button type="submit" disabled={isLoading}>
            {isLoading ? 'Loading...' : 'Login'}
          </button>
        </form>
      ) : (
        <form onSubmit={handleVerifyOtp}>
          {/* OTP field */}
          <button type="submit" disabled={isLoading}>
            {isLoading ? 'Verifying...' : 'Verify OTP'}
          </button>
        </form>
      )}
    </div>
  );
};

// 5. Dashboard Component - Fixed version
const AdminDashboardPage = () => {
  const { adminUser, isAuthenticated, isLoading } = useAdminAuth();
  const [dashboardStats, setDashboardStats] = useState(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  
  useEffect(() => {
    // Redirect if not authenticated
    if (!isLoading && !isAuthenticated) {
      navigate('/admin/login');
      return;
    }
    
    // Only fetch data if authenticated
    if (isAuthenticated) {
      const fetchDashboardStats = async () => {
        try {
          setIsLoadingStats(true);
          const response = await api.get('/admin/dashboard/stats');
          
          if (response.data.status === 'success') {
            setDashboardStats(response.data.data);
          } else {
            setError(response.data.message || 'Failed to load dashboard data');
          }
        } catch (error) {
          console.error('Error fetching dashboard stats:', error);
          setError(error.response?.data?.message || 'Failed to load dashboard data');
        } finally {
          setIsLoadingStats(false);
        }
      };
      
      fetchDashboardStats();
    }
  }, [isAuthenticated, isLoading, navigate]);
  
  if (isLoading || isLoadingStats) {
    return <div className="loading-spinner">Loading...</div>;
  }
  
  if (error) {
    return <div className="error-message">{error}</div>;
  }
  
  return (
    <div className="admin-dashboard">
      <h1>Welcome, {adminUser?.firstName || 'Admin'}</h1>
      
      {dashboardStats && (
        <div className="dashboard-stats">
          {/* Dashboard stats display */}
        </div>
      )}
    </div>
  );
};
