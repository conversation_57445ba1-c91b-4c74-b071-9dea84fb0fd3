# Script to complete the migration of remaining files

# Create additional directories
$additionalDirectories = @(
    "src\main\java\com\pressmeet247\broadcast\controller",
    "src\main\java\com\pressmeet247\broadcast\service",
    "src\main\java\com\pressmeet247\broadcast\dto",
    "src\main\java\com\pressmeet247\broadcast\entity",
    "src\main\java\com\pressmeet247\broadcast\repository",
    "src\main\java\com\pressmeet247\auth\token",
    "src\main\java\com\pressmeet247\common\config\redis",
    "src\main\java\com\pressmeet247\common\config\db",
    "src\main\java\com\pressmeet247\common\init"
)

foreach ($dir in $additionalDirectories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir"
    } else {
        Write-Host "Directory already exists: $dir"
    }
}

# Define mappings for remaining files
$remainingFileMappings = @(
    # Broadcast module
    @{
        Source = "src\main\java\com\auth\service\controller\BroadcastMessageController.java"
        Target = "src\main\java\com\pressmeet247\broadcast\controller\BroadcastMessageController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.broadcast.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\BroadcastMessageService.java"
        Target = "src\main\java\com\pressmeet247\broadcast\service\BroadcastMessageService.java"
        PackageFrom = "package com.auth.service.service"
        PackageTo = "package com.pressmeet247.broadcast.service"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\BroadcastMessage.java"
        Target = "src\main\java\com\pressmeet247\broadcast\entity\BroadcastMessage.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.broadcast.entity"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\BroadcastMessageRepository.java"
        Target = "src\main\java\com\pressmeet247\broadcast\repository\BroadcastMessageRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.broadcast.repository"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\request\BroadcastMessageRequest.java"
        Target = "src\main\java\com\pressmeet247\broadcast\dto\BroadcastMessageRequest.java"
        PackageFrom = "package com.auth.service.dto.request"
        PackageTo = "package com.pressmeet247.broadcast.dto"
    },
    @{
        Source = "src\main\java\com\auth\service\dto\response\BroadcastMessageResponse.java"
        Target = "src\main\java\com\pressmeet247\broadcast\dto\BroadcastMessageResponse.java"
        PackageFrom = "package com.auth.service.dto.response"
        PackageTo = "package com.pressmeet247.broadcast.dto"
    },

    # Auth module - User related
    @{
        Source = "src\main\java\com\auth\service\controller\UserController.java"
        Target = "src\main\java\com\pressmeet247\auth\controller\UserController.java"
        PackageFrom = "package com.auth.service.controller"
        PackageTo = "package com.pressmeet247.auth.controller"
    },
    @{
        Source = "src\main\java\com\auth\service\service\UserService.java"
        Target = "src\main\java\com\pressmeet247\auth\service\UserService.java"
        PackageFrom = "package com.auth.service.service"
        PackageTo = "package com.pressmeet247.auth.service"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\UserPreferences.java"
        Target = "src\main\java\com\pressmeet247\auth\entity\UserPreferences.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.auth.entity"
    },

    # Auth module - Token related
    @{
        Source = "src\main\java\com\auth\service\service\token\TokenService.java"
        Target = "src\main\java\com\pressmeet247\auth\token\TokenService.java"
        PackageFrom = "package com.auth.service.service.token"
        PackageTo = "package com.pressmeet247.auth.token"
    },
    @{
        Source = "src\main\java\com\auth\service\repository\OauthTokenRepository.java"
        Target = "src\main\java\com\pressmeet247\auth\token\OauthTokenRepository.java"
        PackageFrom = "package com.auth.service.repository"
        PackageTo = "package com.pressmeet247.auth.token"
    },
    @{
        Source = "src\main\java\com\auth\service\entity\OauthToken.java"
        Target = "src\main\java\com\pressmeet247\auth\token\OauthToken.java"
        PackageFrom = "package com.auth.service.entity"
        PackageTo = "package com.pressmeet247.auth.token"
    },

    # AI News module
    @{
        Source = "src\main\java\com\auth\service\service\ai\AIService.java"
        Target = "src\main\java\com\pressmeet247\ai_news\service\AIService.java"
        PackageFrom = "package com.auth.service.service.ai"
        PackageTo = "package com.pressmeet247.ai_news.service"
    },
    @{
        Source = "src\main\java\com\auth\service\service\ai\AIServiceImpl.java"
        Target = "src\main\java\com\pressmeet247\ai_news\impl\AIServiceImpl.java"
        PackageFrom = "package com.auth.service.service.ai"
        PackageTo = "package com.pressmeet247.ai_news.impl"
    },

    # Common module - Redis config
    @{
        Source = "src\main\java\com\auth\service\config\redis\RedisConfig.java"
        Target = "src\main\java\com\pressmeet247\common\config\redis\RedisConfig.java"
        PackageFrom = "package com.auth.service.config.redis"
        PackageTo = "package com.pressmeet247.common.config.redis"
    },
    @{
        Source = "src\main\java\com\auth\service\config\redis\InMemoryRedisConfig.java"
        Target = "src\main\java\com\pressmeet247\common\config\redis\InMemoryRedisConfig.java"
        PackageFrom = "package com.auth.service.config.redis"
        PackageTo = "package com.pressmeet247.common.config.redis"
    },

    # Common module - Database initialization
    @{
        Source = "src\main\java\com\auth\service\config\DatabaseMigrationRunner.java"
        Target = "src\main\java\com\pressmeet247\common\init\DatabaseMigrationRunner.java"
        PackageFrom = "package com.auth.service.config"
        PackageTo = "package com.pressmeet247.common.init"
    }
)

# Function to update imports in a file
function Update-Imports {
    param (
        [string]$filePath,
        [string]$oldPackageBase,
        [string]$newPackageBase
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Update package declaration
        $content = $content -replace "package $oldPackageBase", "package $newPackageBase"
        
        # Update imports for old package structure
        $content = $content -replace "import com.auth.service", "import com.pressmeet247"
        
        # Update specific imports for new module structure
        $content = $content -replace "import com.pressmeet247.entity", "import com.pressmeet247.auth.entity"
        $content = $content -replace "import com.pressmeet247.repository", "import com.pressmeet247.auth.repository"
        $content = $content -replace "import com.pressmeet247.dto", "import com.pressmeet247.auth.dto"
        $content = $content -replace "import com.pressmeet247.service.token", "import com.pressmeet247.auth.token"
        $content = $content -replace "import com.pressmeet247.service.auth", "import com.pressmeet247.auth.service"
        $content = $content -replace "import com.pressmeet247.service.email", "import com.pressmeet247.email.service"
        $content = $content -replace "import com.pressmeet247.service.ai", "import com.pressmeet247.ai_news.service"
        $content = $content -replace "import com.pressmeet247.config.redis", "import com.pressmeet247.common.config.redis"
        
        # Write updated content back to file
        Set-Content -Path $filePath -Value $content
        
        Write-Host "Updated imports in $filePath"
    }
    else {
        Write-Host "File not found: $filePath"
    }
}

# Process each file mapping
foreach ($mapping in $remainingFileMappings) {
    $sourceFile = $mapping.Source
    $targetFile = $mapping.Target
    $packageFrom = $mapping.PackageFrom
    $packageTo = $mapping.PackageTo
    
    # Create target directory if it doesn't exist
    $targetDir = Split-Path -Path $targetFile -Parent
    if (-not (Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        Write-Host "Created directory: $targetDir"
    }
    
    # Copy file if source exists
    if (Test-Path $sourceFile) {
        Copy-Item -Path $sourceFile -Destination $targetFile -Force
        Write-Host "Copied $sourceFile to $targetFile"
        
        # Update package declaration and imports
        Update-Imports -filePath $targetFile -oldPackageBase $packageFrom -newPackageBase $packageTo
    }
    else {
        Write-Host "Source file not found: $sourceFile"
    }
}

# Create package-info.java for broadcast module
$broadcastPackageInfo = @"
/**
 * Broadcast module.
 * 
 * This module handles broadcast messaging functionality, including sending
 * notifications and messages to users.
 */
package com.pressmeet247.broadcast;
"@

Set-Content -Path "src\main\java\com\pressmeet247\broadcast\package-info.java" -Value $broadcastPackageInfo
Write-Host "Created package-info.java for broadcast module"

Write-Host "Migration of remaining files completed!"
