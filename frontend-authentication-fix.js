// This is a sample implementation for the frontend to fix the authentication flow

// 1. Create an authentication state manager
import { createContext, useState, useEffect, useContext } from 'react';
import { adminAuthApi } from './api/adminAuthApi';

// Create an auth context
export const AdminAuthContext = createContext(null);

export const AdminAuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [adminUser, setAdminUser] = useState(null);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);
        const response = await adminAuthApi.getCurrentAdmin();
        
        if (response.status === 'success' && response.data) {
          setIsAuthenticated(true);
          setAdminUser(response.data);
        } else {
          setIsAuthenticated(false);
          setAdminUser(null);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setIsAuthenticated(false);
        setAdminUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Login function - two-step process
  const login = async (email, password) => {
    // Step 1: Initiate login
    const initResponse = await adminAuthApi.initiateLogin(email, password);
    
    if (initResponse.status === 'success') {
      // Store credentials temporarily for OTP verification
      sessionStorage.setItem('adminEmail', email);
      sessionStorage.setItem('adminPassword', password);
      return { success: true, message: 'OTP sent successfully' };
    }
    
    return { success: false, message: initResponse.message || 'Login failed' };
  };

  // Verify OTP function
  const verifyOtp = async (otp) => {
    const email = sessionStorage.getItem('adminEmail');
    const password = sessionStorage.getItem('adminPassword');
    
    if (!email || !password) {
      return { success: false, message: 'Session expired. Please login again.' };
    }
    
    try {
      // Step 2: Verify OTP
      const verifyResponse = await adminAuthApi.verifyOtp(email, password, otp);
      
      if (verifyResponse.status === 'success') {
        // Clear stored credentials
        sessionStorage.removeItem('adminEmail');
        sessionStorage.removeItem('adminPassword');
        
        // Update auth state
        setIsAuthenticated(true);
        setAdminUser(verifyResponse.data.user);
        
        return { 
          success: true, 
          message: 'Authentication successful',
          redirectUrl: verifyResponse.data.redirectUrl || '/admin/dashboard'
        };
      }
      
      return { success: false, message: verifyResponse.message || 'OTP verification failed' };
    } catch (error) {
      console.error('OTP verification error:', error);
      return { success: false, message: error.message || 'OTP verification failed' };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await adminAuthApi.logout();
      setIsAuthenticated(false);
      setAdminUser(null);
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, message: error.message };
    }
  };

  return (
    <AdminAuthContext.Provider value={{ 
      isAuthenticated, 
      isLoading, 
      adminUser, 
      login, 
      verifyOtp, 
      logout 
    }}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

// 2. Create a protected route component
export const ProtectedAdminRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAdminAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/admin/login');
    }
  }, [isAuthenticated, isLoading, navigate]);
  
  if (isLoading) {
    return <div>Loading...</div>;
  }
  
  return isAuthenticated ? children : null;
};

// 3. Update the login page component
const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login, verifyOtp } = useAdminAuth();
  const navigate = useNavigate();
  
  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    
    try {
      const result = await login(email, password);
      
      if (result.success) {
        setShowOtpForm(true);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };
  
  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    
    try {
      const result = await verifyOtp(otp);
      
      if (result.success) {
        navigate(result.redirectUrl);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || 'OTP verification failed');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="admin-login-container">
      <h1>Admin Login</h1>
      
      {!showOtpForm ? (
        <form onSubmit={handleLogin}>
          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label>Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" disabled={loading}>
            {loading ? 'Loading...' : 'Login'}
          </button>
        </form>
      ) : (
        <form onSubmit={handleVerifyOtp}>
          <div className="form-group">
            <label>Enter OTP sent to your email</label>
            <input
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              required
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" disabled={loading}>
            {loading ? 'Verifying...' : 'Verify OTP'}
          </button>
          
          <button 
            type="button" 
            onClick={() => setShowOtpForm(false)}
            disabled={loading}
          >
            Back to Login
          </button>
        </form>
      )}
    </div>
  );
};

// 4. Update the admin dashboard component
const AdminDashboardPage = () => {
  const { adminUser } = useAdminAuth();
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        // Only fetch dashboard data if user is authenticated
        const response = await adminApi.getDashboardStats();
        
        if (response.status === 'success') {
          setDashboardStats(response.data);
        } else {
          setError(response.message || 'Failed to load dashboard data');
        }
      } catch (error) {
        setError(error.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };
    
    loadDashboardData();
  }, []);
  
  if (loading) {
    return <div>Loading dashboard...</div>;
  }
  
  if (error) {
    return <div className="error-message">{error}</div>;
  }
  
  return (
    <div className="admin-dashboard">
      <h1>Welcome, {adminUser?.firstName || 'Admin'}</h1>
      
      {dashboardStats && (
        <div className="dashboard-stats">
          <div className="stat-card">
            <h3>Total Users</h3>
            <p>{dashboardStats.totalUsers}</p>
          </div>
          
          <div className="stat-card">
            <h3>Active Subscriptions</h3>
            <p>{dashboardStats.activeSubscriptions}</p>
          </div>
          
          <div className="stat-card">
            <h3>Pending News</h3>
            <p>{dashboardStats.pendingNews}</p>
          </div>
          
          <div className="stat-card">
            <h3>Published News</h3>
            <p>{dashboardStats.publishedNews}</p>
          </div>
        </div>
      )}
      
      {/* Other dashboard components */}
    </div>
  );
};

// 5. Update the main app component to use the auth provider
const App = () => {
  return (
    <AdminAuthProvider>
      <Router>
        <Routes>
          <Route path="/admin/login" element={<AdminLoginPage />} />
          <Route 
            path="/admin/dashboard" 
            element={
              <ProtectedAdminRoute>
                <AdminDashboardPage />
              </ProtectedAdminRoute>
            } 
          />
          {/* Other routes */}
        </Routes>
      </Router>
    </AdminAuthProvider>
  );
};
