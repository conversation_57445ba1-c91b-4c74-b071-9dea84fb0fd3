# AI-Powered News Platform API Documentation

This document provides comprehensive documentation for the AI-Powered News Platform API endpoints. Use this as a reference for integrating the frontend with the backend services.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:8080
```

## Authentication

The API uses JWT (JSON Web Token) for authentication. Most endpoints require authentication.

### Authentication Endpoints

#### Register a new user

```
POST /api/auth/register
```

Request body:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "StrongPassword123!"
}
```

Response:
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email for verification.",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "verified": false,
    "roles": ["USER"]
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Login

```
POST /api/auth/login
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "StrongPassword123!"
}
```

Response:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Refresh Token

```
POST /api/auth/refresh
```

Request body:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

Response:
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Logout

```
POST /api/auth/logout
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Logged out successfully",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Verify Account

```
GET /api/auth/verify?token={verification_token}
```

Response:
```json
{
  "success": true,
  "message": "Email verified successfully",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Resend Verification Email

```
POST /api/auth/resend-verification-email
```

Request body:
```json
{
  "email": "<EMAIL>"
}
```

Response:
```json
{
  "success": true,
  "message": "Verification email sent successfully",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Request Password Reset

```
POST /api/auth/reset-password/request
```

Request body:
```json
{
  "email": "<EMAIL>"
}
```

Response:
```json
{
  "success": true,
  "message": "Password reset email sent successfully",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Confirm Password Reset

```
POST /api/auth/reset-password/confirm
```

Request body:
```json
{
  "token": "reset-token-here",
  "newPassword": "NewStrongPassword123!"
}
```

Response:
```json
{
  "success": true,
  "message": "Password reset successfully",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

## News Management

### News Generation

#### Generate News (Authenticated Users Only)

```
POST /api/news/generate
```

Authentication: Bearer Token

Request body:
```json
{
  "date": "2023-05-08",
  "place": "City Hall",
  "whatHappened": "A community meeting was held to discuss the new park project",
  "whereHappened": "Main Conference Room, City Hall",
  "whoInvolved": "Mayor John Smith, City Council members, and local residents",
  "whenHappened": "7:00 PM",
  "whoTold": "City Press Office",
  "howHappened": "The meeting was conducted in a town hall format with presentations followed by Q&A",
  "whyHappened": "To gather community feedback on the proposed park design",
  "newsType": "Local"
}
```

Response:
```json
{
  "success": true,
  "message": "News generated successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "Community Meeting Held to Discuss New Park Project - City Hall",
    "content": "Generated news content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PENDING",
    "featured": false,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": null
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

### News Retrieval

#### Get News by ID

```
GET /api/news/{id}
```

Response:
```json
{
  "success": true,
  "message": "News retrieved successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "Community Meeting Held to Discuss New Park Project - City Hall",
    "content": "News content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PUBLISHED",
    "featured": true,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": "2023-05-08T13:45:30.123"
  },
  "timestamp": "2023-05-08T14:00:00.000"
}
```

#### Get All News (Admin Only)

```
GET /api/news?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token (Admin role required)

Response:
```json
{
  "success": true,
  "message": "News retrieved successfully",
  "data": {
    "content": [
      {
        "id": 2,
        "userId": 1,
        "userName": "John Doe",
        "title": "News Title 2",
        "content": "News content 2...",
        "date": "2023-05-08",
        "place": "City Hall",
        "newsType": "Local",
        "status": "PENDING",
        "featured": false,
        "createdAt": "2023-05-08T13:00:00.000",
        "publishedAt": null
      },
      {
        "id": 1,
        "userId": 1,
        "userName": "John Doe",
        "title": "News Title 1",
        "content": "News content 1...",
        "date": "2023-05-07",
        "place": "City Park",
        "newsType": "Local",
        "status": "PUBLISHED",
        "featured": true,
        "createdAt": "2023-05-07T10:00:00.000",
        "publishedAt": "2023-05-07T11:30:00.000"
      }
    ],
    "pageable": {
      "sort": {
        "sorted": true,
        "unsorted": false,
        "empty": false
      },
      "pageNumber": 0,
      "pageSize": 10,
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalPages": 1,
    "totalElements": 2,
    "last": true,
    "first": true,
    "sort": {
      "sorted": true,
      "unsorted": false,
      "empty": false
    },
    "size": 10,
    "number": 0,
    "numberOfElements": 2,
    "empty": false
  },
  "timestamp": "2023-05-08T14:00:00.000"
}
```

#### Get My News (Authenticated Users Only)

```
GET /api/news/my-news?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token

Response: Similar to "Get All News" but filtered to the authenticated user's news.

#### Get News by Status (Admin Only)

```
GET /api/news/status/{status}?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token (Admin role required)

Parameters:
- `status`: PENDING, PUBLISHED, REJECTED, or DRAFT

Response: Similar to "Get All News" but filtered by status.

#### Get Published News (Public)

```
GET /api/news/published?page=0&size=10&sort=createdAt,desc
```

Response: Similar to "Get All News" but filtered to published news.

#### Get Featured News (Public)

```
GET /api/news/featured?page=0&size=10&sort=createdAt,desc
```

Response: Similar to "Get All News" but filtered to featured news.

#### Get News by Type (Public)

```
GET /api/news/type/{type}?page=0&size=10&sort=createdAt,desc
```

Parameters:
- `type`: News type (e.g., "Local", "Politics", "Sports")

Response: Similar to "Get All News" but filtered by news type.

### News Administration (Admin Only)

#### Approve News

```
POST /api/news/{id}/approve
```

Authentication: Bearer Token (Admin role required)

Response:
```json
{
  "success": true,
  "message": "News approved and published successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "News Title",
    "content": "News content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PUBLISHED",
    "featured": false,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": "2023-05-08T13:45:30.123"
  },
  "timestamp": "2023-05-08T13:45:30.123"
}
```

#### Reject News

```
POST /api/news/{id}/reject
```

Authentication: Bearer Token (Admin role required)

Response:
```json
{
  "success": true,
  "message": "News rejected successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "News Title",
    "content": "News content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "REJECTED",
    "featured": false,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": null
  },
  "timestamp": "2023-05-08T13:45:30.123"
}
```

#### Update News Content

```
PUT /api/news/{id}/content
```

Authentication: Bearer Token (Admin role required)

Request body: Raw text content

Response:
```json
{
  "success": true,
  "message": "News content updated successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "News Title",
    "content": "Updated news content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PENDING",
    "featured": false,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": null
  },
  "timestamp": "2023-05-08T13:45:30.123"
}
```

#### Set News as Featured

```
PUT /api/news/{id}/featured?featured=true
```

Authentication: Bearer Token (Admin role required)

Parameters:
- `featured`: true or false

Response:
```json
{
  "success": true,
  "message": "News set as featured successfully",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "John Doe",
    "title": "News Title",
    "content": "News content...",
    "date": "2023-05-08",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PUBLISHED",
    "featured": true,
    "createdAt": "2023-05-08T12:34:56.789",
    "publishedAt": "2023-05-08T13:00:00.000"
  },
  "timestamp": "2023-05-08T13:45:30.123"
}
```

## Broadcast Messaging

### Admin Broadcast Management

#### Create Broadcast Message (Admin Only)

```
POST /api/messages
```

Authentication: Bearer Token (Admin role required)

Request body:
```json
{
  "title": "Important Announcement",
  "content": "This is an important announcement for all users.",
  "type": "ANNOUNCEMENT",
  "targetAllUsers": true,
  "targetPremiumUsers": false,
  "specificUserIds": []
}
```

Response:
```json
{
  "success": true,
  "message": "Broadcast message created successfully",
  "data": {
    "id": 1,
    "senderId": 1,
    "senderName": "Admin User",
    "title": "Important Announcement",
    "content": "This is an important announcement for all users.",
    "type": "ANNOUNCEMENT",
    "targetAllUsers": true,
    "targetPremiumUsers": false,
    "specificUserIds": [],
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Get Broadcast Message by ID (Admin Only)

```
GET /api/messages/{id}
```

Authentication: Bearer Token (Admin role required)

Response:
```json
{
  "success": true,
  "message": "Broadcast message retrieved successfully",
  "data": {
    "id": 1,
    "senderId": 1,
    "senderName": "Admin User",
    "title": "Important Announcement",
    "content": "This is an important announcement for all users.",
    "type": "ANNOUNCEMENT",
    "targetAllUsers": true,
    "targetPremiumUsers": false,
    "specificUserIds": [],
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Get All Broadcast Messages (Admin Only)

```
GET /api/messages?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token (Admin role required)

Response: Paginated list of broadcast messages.

#### Get Delivery Status (Admin Only)

```
GET /api/messages/{messageId}/status/{recipientId}
```

Authentication: Bearer Token (Admin role required)

Response:
```json
{
  "success": true,
  "message": "Delivery status retrieved successfully",
  "data": {
    "id": 1,
    "messageId": 1,
    "recipientId": 2,
    "recipientName": "John Doe",
    "status": "DELIVERED",
    "deliveredAt": "2023-05-08T12:40:00.000",
    "readAt": null,
    "errorMessage": null
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

### User Broadcast Management

#### Get My Messages (Authenticated Users Only)

```
GET /api/messages/my-messages?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token

Response: Paginated list of broadcast messages for the authenticated user.

#### Get Unread Message Count (Authenticated Users Only)

```
GET /api/messages/unread-count
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Unread message count retrieved successfully",
  "data": 5,
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Mark Message as Read (Authenticated Users Only)

```
POST /api/messages/{id}/read
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Message marked as read successfully",
  "data": {
    "id": 1,
    "messageId": 1,
    "recipientId": 2,
    "recipientName": "John Doe",
    "status": "READ",
    "deliveredAt": "2023-05-08T12:40:00.000",
    "readAt": "2023-05-08T13:00:00.000",
    "errorMessage": null
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

## Subscription Management

### Subscription Plans

#### Create Subscription Plan (Admin Only)

```
POST /api/subscriptions/plans
```

Authentication: Bearer Token (Admin role required)

Request body:
```json
{
  "name": "Premium",
  "description": "Premium plan with all features",
  "price": 9.99,
  "currency": "USD",
  "durationMonths": 1,
  "features": "{\"feature1\": true, \"feature2\": true}"
}
```

Response:
```json
{
  "success": true,
  "message": "Subscription plan created successfully",
  "data": {
    "id": 1,
    "name": "Premium",
    "description": "Premium plan with all features",
    "price": 9.99,
    "currency": "USD",
    "durationMonths": 1,
    "active": true,
    "features": "{\"feature1\": true, \"feature2\": true}",
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Get All Subscription Plans

```
GET /api/subscriptions/plans?page=0&size=10&sort=price,asc
```

Response: Paginated list of subscription plans.

#### Get Active Subscription Plans

```
GET /api/subscriptions/plans/active
```

Response:
```json
{
  "success": true,
  "message": "Active subscription plans retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Premium",
      "description": "Premium plan with all features",
      "price": 9.99,
      "currency": "USD",
      "durationMonths": 1,
      "active": true,
      "features": "{\"feature1\": true, \"feature2\": true}",
      "createdAt": "2023-05-08T12:34:56.789"
    },
    {
      "id": 2,
      "name": "Premium Annual",
      "description": "Premium plan with annual discount",
      "price": 99.99,
      "currency": "USD",
      "durationMonths": 12,
      "active": true,
      "features": "{\"feature1\": true, \"feature2\": true}",
      "createdAt": "2023-05-08T12:34:56.789"
    }
  ],
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Get Subscription Plan by ID

```
GET /api/subscriptions/plans/{id}
```

Response: Details of the specified subscription plan.

#### Update Subscription Plan (Admin Only)

```
PUT /api/subscriptions/plans/{id}
```

Authentication: Bearer Token (Admin role required)

Request body:
```json
{
  "name": "Premium Plus",
  "description": "Enhanced premium plan with additional features",
  "price": 14.99,
  "currency": "USD",
  "durationMonths": 1,
  "features": "{\"feature1\": true, \"feature2\": true, \"feature3\": true}",
  "active": true
}
```

Response: Updated subscription plan details.

### User Subscriptions

#### Create Subscription (Authenticated Users Only)

```
POST /api/subscriptions
```

Authentication: Bearer Token

Request body:
```json
{
  "planId": 1,
  "paymentMethod": "CREDIT_CARD",
  "paymentReference": "txn_**********"
}
```

Response:
```json
{
  "success": true,
  "message": "Subscription created successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "John Doe",
    "planId": 1,
    "planName": "Premium",
    "status": "ACTIVE",
    "startDate": "2023-05-08T12:34:56.789",
    "endDate": "2023-06-08T12:34:56.789",
    "autoRenew": true,
    "lastPaymentDate": "2023-05-08T12:34:56.789",
    "nextPaymentDate": "2023-06-01T12:34:56.789",
    "paymentMethod": "CREDIT_CARD",
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

#### Get Active Subscription (Authenticated Users Only)

```
GET /api/subscriptions/active
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Active subscription retrieved successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "John Doe",
    "planId": 1,
    "planName": "Premium",
    "status": "ACTIVE",
    "startDate": "2023-05-08T12:34:56.789",
    "endDate": "2023-06-08T12:34:56.789",
    "autoRenew": true,
    "lastPaymentDate": "2023-05-08T12:34:56.789",
    "nextPaymentDate": "2023-06-01T12:34:56.789",
    "paymentMethod": "CREDIT_CARD",
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Get My Subscriptions (Authenticated Users Only)

```
GET /api/subscriptions/my-subscriptions?page=0&size=10&sort=createdAt,desc
```

Authentication: Bearer Token

Response: Paginated list of the user's subscriptions.

#### Cancel Subscription (Authenticated Users Only)

```
POST /api/subscriptions/{id}/cancel
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Subscription cancelled successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "John Doe",
    "planId": 1,
    "planName": "Premium",
    "status": "CANCELLED",
    "startDate": "2023-05-08T12:34:56.789",
    "endDate": "2023-06-08T12:34:56.789",
    "autoRenew": false,
    "lastPaymentDate": "2023-05-08T12:34:56.789",
    "nextPaymentDate": "2023-06-01T12:34:56.789",
    "paymentMethod": "CREDIT_CARD",
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Renew Subscription (Authenticated Users Only)

```
POST /api/subscriptions/{id}/renew
```

Authentication: Bearer Token

Request body:
```json
{
  "paymentMethod": "CREDIT_CARD",
  "paymentReference": "txn_9876543210"
}
```

Response:
```json
{
  "success": true,
  "message": "Subscription renewed successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "John Doe",
    "planId": 1,
    "planName": "Premium",
    "status": "ACTIVE",
    "startDate": "2023-06-08T12:34:56.789",
    "endDate": "2023-07-08T12:34:56.789",
    "autoRenew": true,
    "lastPaymentDate": "2023-05-08T13:00:00.000",
    "nextPaymentDate": "2023-07-01T12:34:56.789",
    "paymentMethod": "CREDIT_CARD",
    "createdAt": "2023-05-08T12:34:56.789"
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Check Active Subscription (Authenticated Users Only)

```
GET /api/subscriptions/check-active
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "Active subscription check completed",
  "data": true,
  "timestamp": "2023-05-08T13:00:00.000"
}
```

### Admin Subscription Management

#### Get Expiring Subscriptions (Admin Only)

```
GET /api/subscriptions/expiring?days=7
```

Authentication: Bearer Token (Admin role required)

Parameters:
- `days`: Number of days until expiry (default: 7)

Response: Paginated list of subscriptions expiring within the specified number of days.

#### Notify Expiring Subscriptions (Admin Only)

```
POST /api/subscriptions/notify-expiring?days=7
```

Authentication: Bearer Token (Admin role required)

Parameters:
- `days`: Number of days until expiry (default: 7)

Response:
```json
{
  "success": true,
  "message": "Expiring subscription notifications sent successfully",
  "timestamp": "2023-05-08T13:00:00.000"
}
```

## User Management

#### Get Current User (Authenticated Users Only)

```
GET /api/users/me
```

Authentication: Bearer Token

Response:
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "id": 2,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+**********",
    "profilePictureUrl": "https://example.com/profile.jpg",
    "verified": true,
    "roles": ["USER"],
    "createdAt": "2023-05-01T10:00:00.000"
  },
  "timestamp": "2023-05-08T13:00:00.000"
}
```

#### Get User by ID (Admin or Self Only)

```
GET /api/users/{id}
```

Authentication: Bearer Token

Response: Similar to "Get Current User" but for the specified user ID.

## Health Check

#### Check API Health

```
GET /api/health
```

Response:
```json
{
  "status": "UP",
  "message": "Authentication service is running",
  "timestamp": 1683547676789
}
```

## API Response Format

Most API endpoints return responses in the following format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2023-05-08T12:34:56.789"
}
```

For error responses:

```json
{
  "success": false,
  "message": "Error message",
  "timestamp": "2023-05-08T12:34:56.789"
}
```

## Authentication Headers

For protected endpoints, include the JWT token in the Authorization header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Pagination

Endpoints that return collections support pagination with the following query parameters:

- `page`: Page number (0-based, default: 0)
- `size`: Page size (default: 20)
- `sort`: Sort field and direction (e.g., `createdAt,desc`)

Example:
```
GET /api/news?page=0&size=10&sort=createdAt,desc
```

## Error Codes

- 200 OK: Request succeeded
- 201 Created: Resource created successfully
- 400 Bad Request: Invalid request parameters
- 401 Unauthorized: Authentication required or failed
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server-side error

## CORS Configuration

The API allows cross-origin requests from:
```
http://localhost:3000
```

## API Documentation

Swagger UI is available at:
```
http://localhost:8080/swagger-ui.html
```
