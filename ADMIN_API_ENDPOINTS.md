# Admin API Endpoints

This document provides a comprehensive list of all admin API endpoints for the application.

## Authentication Endpoints

### Ad<PERSON>gin (Two-Step Authentication)

Admin login requires two steps: initiating the login and verifying with OTP.

#### 1. Initiate Admin Login

```
POST /api/admin/auth/login/initiate
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Admin@123"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "OTP sent successfully. Please verify to complete login.",
  "data": true,
  "timestamp": "2025-05-09T10:30:45.123Z"
}
```

#### 2. Verify Admin Login OTP

```
POST /api/admin/auth/login/verify
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Admin@123",
  "otp": "123456"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Admin login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6",
    "expirationTime": 1620000000000,
    "tokenType": "Bearer",
    "userId": 1,
    "email": "<EMAIL>",
    "roles": "ADMIN"
  },
  "timestamp": "2025-05-09T10:31:15.456Z"
}
```

## Dashboard Endpoints

### Get Dashboard Statistics

```
GET /api/admin/dashboard/stats
```

**Response:**
```json
{
  "status": "success",
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "totalUsers": 100,
    "totalAdmins": 5,
    "totalNews": 50,
    "pendingNews": 10,
    "publishedNews": 35,
    "rejectedNews": 5,
    "totalSubscriptions": 30,
    "activeSubscriptions": 25
  },
  "timestamp": "2025-05-09T10:32:00.789Z"
}
```

## User Management Endpoints

### Get All Users

```
GET /api/admin/dashboard/users?page=0&size=10&sort=createdAt,desc
```

**Response:**
```json
{
  "status": "success",
  "message": "Users retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "emailVerified": true,
        "phoneNumber": "+1234567890",
        "phoneVerified": false,
        "enabled": true,
        "roles": ["USER"],
        "createdAt": "2025-04-01T10:00:00.000Z",
        "lastLoginAt": "2025-05-08T15:30:00.000Z"
      },
      // More users...
    ],
    "pageable": {
      "sort": {
        "sorted": true,
        "unsorted": false,
        "empty": false
      },
      "pageNumber": 0,
      "pageSize": 10,
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalElements": 100,
    "totalPages": 10,
    "last": false,
    "first": true,
    "size": 10,
    "number": 0,
    "sort": {
      "sorted": true,
      "unsorted": false,
      "empty": false
    },
    "numberOfElements": 10,
    "empty": false
  },
  "timestamp": "2025-05-09T10:33:00.123Z"
}
```

### Get Admin Users

```
GET /api/admin/dashboard/users/admins?page=0&size=10
```

**Response:**
```json
{
  "status": "success",
  "message": "Admin users retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "firstName": "Admin",
        "lastName": "User",
        "email": "<EMAIL>",
        "emailVerified": true,
        "phoneNumber": null,
        "phoneVerified": false,
        "enabled": true,
        "roles": ["ADMIN"],
        "createdAt": "2025-03-15T08:00:00.000Z",
        "lastLoginAt": "2025-05-09T09:45:00.000Z"
      },
      // More admin users...
    ],
    // Pagination info...
  },
  "timestamp": "2025-05-09T10:34:00.456Z"
}
```

## Content Management Endpoints

### Get All News

```
GET /api/admin/dashboard/news?page=0&size=10&sort=createdAt,desc
```

**Response:**
```json
{
  "status": "success",
  "message": "News retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "userId": 2,
        "userName": "Jane Smith",
        "title": "Local Community Event",
        "content": "Lorem ipsum dolor sit amet...",
        "date": "2025-05-15",
        "place": "City Hall",
        "newsType": "Local",
        "status": "PENDING",
        "featured": false,
        "createdAt": "2025-05-08T14:30:00.000Z",
        "publishedAt": null
      },
      // More news...
    ],
    // Pagination info...
  },
  "timestamp": "2025-05-09T10:35:00.789Z"
}
```

### Get News by Status

```
GET /api/admin/dashboard/news/status/PENDING?page=0&size=10
```

**Response:**
```json
{
  "status": "success",
  "message": "News retrieved successfully",
  "data": {
    "content": [
      // News with PENDING status...
    ],
    // Pagination info...
  },
  "timestamp": "2025-05-09T10:36:00.123Z"
}
```

### Approve News

```
POST /api/admin/dashboard/news/1/approve
```

**Response:**
```json
{
  "status": "success",
  "message": "News approved successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "Jane Smith",
    "title": "Local Community Event",
    "content": "Lorem ipsum dolor sit amet...",
    "date": "2025-05-15",
    "place": "City Hall",
    "newsType": "Local",
    "status": "PUBLISHED",
    "featured": false,
    "createdAt": "2025-05-08T14:30:00.000Z",
    "publishedAt": "2025-05-09T10:37:00.456Z"
  },
  "timestamp": "2025-05-09T10:37:00.456Z"
}
```

### Reject News

```
POST /api/admin/dashboard/news/2/reject
```

**Response:**
```json
{
  "status": "success",
  "message": "News rejected successfully",
  "data": {
    "id": 2,
    "userId": 3,
    "userName": "Bob Johnson",
    "title": "Upcoming Sports Event",
    "content": "Lorem ipsum dolor sit amet...",
    "date": "2025-05-20",
    "place": "Sports Stadium",
    "newsType": "Sports",
    "status": "REJECTED",
    "featured": false,
    "createdAt": "2025-05-08T16:45:00.000Z",
    "publishedAt": null
  },
  "timestamp": "2025-05-09T10:38:00.789Z"
}
```

### Set News as Featured

```
PUT /api/admin/dashboard/news/3/featured?featured=true
```

**Response:**
```json
{
  "status": "success",
  "message": "News featured status updated successfully",
  "data": {
    "id": 3,
    "userId": 4,
    "userName": "Alice Williams",
    "title": "Technology Conference",
    "content": "Lorem ipsum dolor sit amet...",
    "date": "2025-06-01",
    "place": "Convention Center",
    "newsType": "Technology",
    "status": "PUBLISHED",
    "featured": true,
    "createdAt": "2025-05-07T10:15:00.000Z",
    "publishedAt": "2025-05-07T14:30:00.000Z"
  },
  "timestamp": "2025-05-09T10:39:00.123Z"
}
```

## Error Responses

All endpoints may return the following error responses:

### Unauthorized

```json
{
  "status": "error",
  "message": "Unauthorized access",
  "data": null,
  "timestamp": "2025-05-09T10:40:00.456Z"
}
```

### Resource Not Found

```json
{
  "status": "error",
  "message": "Resource not found: News with ID 999 not found",
  "data": null,
  "timestamp": "2025-05-09T10:41:00.789Z"
}
```

### Internal Server Error

```json
{
  "status": "error",
  "message": "An unexpected error occurred. Please try again.",
  "data": null,
  "timestamp": "2025-05-09T10:42:00.123Z"
}
```

## Authentication

All admin endpoints require authentication with a valid JWT token. The token must be included in the Authorization header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

The token is obtained from the admin login process and must have the ADMIN role.
