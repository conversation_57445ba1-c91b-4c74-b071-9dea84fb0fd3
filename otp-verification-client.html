<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTP Verification Client</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #4285f4;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #5f6368;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input[type="email"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3367d6;
        }
        .otp-container {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin: 20px 0;
        }
        .otp-input {
            width: 40px;
            height: 50px;
            text-align: center;
            font-size: 24px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #e6f4ea;
            color: #137333;
            border: 1px solid #137333;
        }
        .error {
            background-color: #fce8e6;
            color: #c5221f;
            border: 1px solid #c5221f;
        }
        .info {
            background-color: #e8f0fe;
            color: #1a73e8;
            border: 1px solid #1a73e8;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
            margin-top: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom: 2px solid #4285f4;
            color: #4285f4;
            font-weight: 500;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #4285f4;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OTP Verification Client</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('register')">Register</div>
            <div class="tab" onclick="showTab('verify')">Verify OTP</div>
            <div class="tab" onclick="showTab('resend')">Resend OTP</div>
            <div class="tab" onclick="showTab('login')">Login</div>
        </div>
        
        <div id="register" class="tab-content active">
            <div class="card">
                <h2>Register New User</h2>
                <div class="form-group">
                    <label for="firstName">First Name:</label>
                    <input type="text" id="firstName" placeholder="Enter first name">
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name:</label>
                    <input type="text" id="lastName" placeholder="Enter last name">
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email:</label>
                    <input type="email" id="registerEmail" placeholder="Enter email">
                </div>
                <div class="form-group">
                    <label for="registerPassword">Password:</label>
                    <input type="text" id="registerPassword" placeholder="Enter password">
                </div>
                <button onclick="register()">Register</button>
                <div id="registerResult" class="result"></div>
            </div>
        </div>
        
        <div id="verify" class="tab-content">
            <div class="card">
                <h2>Verify OTP</h2>
                <div class="form-group">
                    <label for="verifyEmail">Email:</label>
                    <input type="email" id="verifyEmail" placeholder="Enter email">
                </div>
                <div class="form-group">
                    <label>OTP Code:</label>
                    <div class="otp-container">
                        <input type="text" class="otp-input" maxlength="1" id="otp1" onkeyup="moveToNext(this, 'otp2')">
                        <input type="text" class="otp-input" maxlength="1" id="otp2" onkeyup="moveToNext(this, 'otp3')">
                        <input type="text" class="otp-input" maxlength="1" id="otp3" onkeyup="moveToNext(this, 'otp4')">
                        <input type="text" class="otp-input" maxlength="1" id="otp4" onkeyup="moveToNext(this, 'otp5')">
                        <input type="text" class="otp-input" maxlength="1" id="otp5" onkeyup="moveToNext(this, 'otp6')">
                        <input type="text" class="otp-input" maxlength="1" id="otp6">
                    </div>
                </div>
                <button onclick="verifyOtp()">Verify OTP</button>
                <div id="verifyResult" class="result"></div>
            </div>
        </div>
        
        <div id="resend" class="tab-content">
            <div class="card">
                <h2>Resend OTP</h2>
                <div class="form-group">
                    <label for="resendEmail">Email:</label>
                    <input type="email" id="resendEmail" placeholder="Enter email">
                </div>
                <button onclick="resendOtp()">Resend OTP</button>
                <div id="resendResult" class="result"></div>
            </div>
        </div>
        
        <div id="login" class="tab-content">
            <div class="card">
                <h2>Login</h2>
                <div class="form-group">
                    <label for="loginEmail">Email:</label>
                    <input type="email" id="loginEmail" placeholder="Enter email">
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password:</label>
                    <input type="text" id="loginPassword" placeholder="Enter password">
                </div>
                <button onclick="login()">Login</button>
                <div id="loginResult" class="result"></div>
            </div>
        </div>
        
        <div class="card">
            <h2>API Response</h2>
            <pre id="apiResponse">No response yet</pre>
        </div>
    </div>

    <script>
        // Base URL for API calls
        const API_BASE_URL = 'http://localhost:8080/api/v1/authentication';
        
        // Show the selected tab
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');
            
            // Add active class to the clicked tab
            Array.from(document.querySelectorAll('.tab')).find(tab => tab.textContent.toLowerCase().includes(tabId)).classList.add('active');
        }
        
        // Move to next OTP input field
        function moveToNext(current, nextId) {
            if (current.value.length === current.maxLength) {
                document.getElementById(nextId).focus();
            }
        }
        
        // Display result message
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
        }
        
        // Display API response
        function showApiResponse(data) {
            document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
        }
        
        // Register a new user
        async function register() {
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            
            if (!firstName || !lastName || !email || !password) {
                showResult('registerResult', 'Please fill in all fields', 'error');
                return;
            }
            
            showResult('registerResult', 'Registering...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        firstName: firstName,
                        lastName: lastName,
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                showApiResponse(data);
                
                if (data.status === 'success') {
                    showResult('registerResult', 'Registration successful! Please check your email for OTP.', 'success');
                    
                    // Auto-fill email in verify tab
                    document.getElementById('verifyEmail').value = email;
                    
                    // Switch to verify tab
                    showTab('verify');
                    
                    // Get OTP for testing
                    getOtpForTesting(email);
                } else {
                    showResult('registerResult', `Registration failed: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showResult('registerResult', `Error: ${error.message}`, 'error');
            }
        }
        
        // Get OTP for testing purposes
        async function getOtpForTesting(email) {
            try {
                const response = await fetch(`${API_BASE_URL}/get-otp-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success' && data.data) {
                    const otpCode = data.data;
                    
                    // Fill OTP inputs
                    for (let i = 0; i < 6; i++) {
                        document.getElementById(`otp${i+1}`).value = otpCode.charAt(i);
                    }
                    
                    showResult('verifyResult', `OTP code retrieved: ${otpCode}`, 'info');
                }
            } catch (error) {
                console.error('Error getting OTP:', error);
            }
        }
        
        // Verify OTP
        async function verifyOtp() {
            const email = document.getElementById('verifyEmail').value;
            
            // Get OTP from inputs
            let otpCode = '';
            for (let i = 1; i <= 6; i++) {
                otpCode += document.getElementById(`otp${i}`).value;
            }
            
            if (!email || !otpCode || otpCode.length !== 6) {
                showResult('verifyResult', 'Please enter email and 6-digit OTP code', 'error');
                return;
            }
            
            showResult('verifyResult', 'Verifying OTP...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/verify-otp`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        otpCode: otpCode
                    })
                });
                
                const data = await response.json();
                showApiResponse(data);
                
                if (data.status === 'success') {
                    showResult('verifyResult', 'Email verified successfully!', 'success');
                    
                    // Auto-fill email in login tab
                    document.getElementById('loginEmail').value = email;
                    
                    // Switch to login tab
                    showTab('login');
                } else {
                    showResult('verifyResult', `Verification failed: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showResult('verifyResult', `Error: ${error.message}`, 'error');
            }
        }
        
        // Resend OTP
        async function resendOtp() {
            const email = document.getElementById('resendEmail').value;
            
            if (!email) {
                showResult('resendResult', 'Please enter email', 'error');
                return;
            }
            
            showResult('resendResult', 'Resending OTP...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/resend-otp`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email
                    })
                });
                
                const data = await response.json();
                showApiResponse(data);
                
                if (data.status === 'success') {
                    showResult('resendResult', 'New OTP code sent successfully. Please check your email.', 'success');
                    
                    // Auto-fill email in verify tab
                    document.getElementById('verifyEmail').value = email;
                    
                    // Switch to verify tab
                    showTab('verify');
                    
                    // Get OTP for testing
                    getOtpForTesting(email);
                } else {
                    showResult('resendResult', `Failed to resend OTP: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showResult('resendResult', `Error: ${error.message}`, 'error');
            }
        }
        
        // Login
        async function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showResult('loginResult', 'Please enter email and password', 'error');
                return;
            }
            
            showResult('loginResult', 'Logging in...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                showApiResponse(data);
                
                if (data.status === 'success') {
                    showResult('loginResult', 'Login successful!', 'success');
                    
                    // Store tokens in localStorage
                    if (data.data) {
                        localStorage.setItem('accessToken', data.data.accessToken);
                        localStorage.setItem('refreshToken', data.data.refreshToken);
                        localStorage.setItem('userId', data.data.userId);
                        localStorage.setItem('email', data.data.email);
                    }
                } else {
                    showResult('loginResult', `Login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showResult('loginResult', `Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
