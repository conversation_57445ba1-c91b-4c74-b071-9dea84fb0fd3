<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refresh Token Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Refresh <PERSON>ken Test</h1>
    
    <div class="container">
        <h2>Login</h2>
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="Enter your email">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter your password">
        </div>
        <div style="margin-top: 10px;">
            <button onclick="login()">Login</button>
        </div>
        <div id="loginResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Refresh Token</h2>
        <div>
            <label for="refreshToken">Refresh Token:</label>
            <input type="text" id="refreshToken" placeholder="Enter refresh token">
        </div>
        <div style="margin-top: 10px;">
            <button onclick="refreshToken()">Refresh Token</button>
        </div>
        <div id="refreshResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Logout</h2>
        <div style="margin-top: 10px;">
            <button onclick="logout()">Logout</button>
        </div>
        <div id="logoutResult" style="margin-top: 10px;"></div>
    </div>
    
    <div class="container">
        <h2>Current Tokens</h2>
        <div>
            <label>Access Token:</label>
            <pre id="accessToken">None</pre>
        </div>
        <div>
            <label>Refresh Token:</label>
            <pre id="refreshTokenDisplay">None</pre>
        </div>
        <div>
            <label>Expiration Time:</label>
            <pre id="expirationTime">None</pre>
        </div>
    </div>
    
    <div class="container">
        <h2>Response</h2>
        <pre id="responseData"></pre>
    </div>

    <script>
        // Display stored tokens on page load
        document.addEventListener('DOMContentLoaded', function() {
            displayStoredTokens();
        });
        
        // Display stored tokens
        function displayStoredTokens() {
            const accessToken = localStorage.getItem('accessToken') || 'None';
            const refreshToken = localStorage.getItem('refreshToken') || 'None';
            const expirationTime = localStorage.getItem('expirationTime') || 'None';
            
            document.getElementById('accessToken').textContent = accessToken;
            document.getElementById('refreshTokenDisplay').textContent = refreshToken;
            document.getElementById('expirationTime').textContent = expirationTime;
            
            // Auto-fill refresh token input
            if (refreshToken !== 'None') {
                document.getElementById('refreshToken').value = refreshToken;
            }
        }
        
        // Login function
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginResult = document.getElementById('loginResult');
            
            if (!email || !password) {
                loginResult.innerHTML = '<span class="error">Please enter email and password</span>';
                return;
            }
            
            loginResult.innerHTML = 'Logging in...';
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/authentication/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success' && data.data) {
                    loginResult.innerHTML = '<span class="success">Login successful!</span>';
                    
                    // Store tokens
                    localStorage.setItem('accessToken', data.data.accessToken);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('expirationTime', data.data.expirationTime);
                    
                    // Display tokens
                    displayStoredTokens();
                } else {
                    loginResult.innerHTML = `<span class="error">Login failed: ${data.message}</span>`;
                }
            } catch (error) {
                console.error('Error:', error);
                loginResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
        
        // Refresh token function
        async function refreshToken() {
            const refreshToken = document.getElementById('refreshToken').value;
            const refreshResult = document.getElementById('refreshResult');
            
            if (!refreshToken) {
                refreshResult.innerHTML = '<span class="error">Please enter refresh token</span>';
                return;
            }
            
            refreshResult.innerHTML = 'Refreshing token...';
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/authentication/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refreshToken: refreshToken
                    })
                });
                
                const data = await response.json();
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success' && data.data) {
                    refreshResult.innerHTML = '<span class="success">Token refreshed successfully!</span>';
                    
                    // Store new tokens
                    localStorage.setItem('accessToken', data.data.accessToken);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('expirationTime', data.data.expirationTime);
                    
                    // Display tokens
                    displayStoredTokens();
                } else {
                    refreshResult.innerHTML = `<span class="error">Token refresh failed: ${data.message}</span>`;
                }
            } catch (error) {
                console.error('Error:', error);
                refreshResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
        
        // Logout function
        async function logout() {
            const accessToken = localStorage.getItem('accessToken');
            const logoutResult = document.getElementById('logoutResult');
            
            if (!accessToken) {
                logoutResult.innerHTML = '<span class="error">Not logged in</span>';
                return;
            }
            
            logoutResult.innerHTML = 'Logging out...';
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/authentication/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
                
                const data = await response.json();
                document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    logoutResult.innerHTML = '<span class="success">Logged out successfully!</span>';
                    
                    // Clear tokens
                    localStorage.removeItem('accessToken');
                    localStorage.removeItem('refreshToken');
                    localStorage.removeItem('expirationTime');
                    
                    // Display tokens
                    displayStoredTokens();
                } else {
                    logoutResult.innerHTML = `<span class="error">Logout failed: ${data.message}</span>`;
                }
            } catch (error) {
                console.error('Error:', error);
                logoutResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
