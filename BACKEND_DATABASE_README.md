# PressMeet247 Backend & Database Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [System Architecture](#system-architecture)
3. [Project Structure](#project-structure)
4. [Data Flow Diagrams](#data-flow-diagrams)
5. [Database Structure](#database-structure)
6. [Authentication Flow](#authentication-flow)
7. [Admin System](#admin-system)
8. [AI News Generation](#ai-news-generation)
9. [Subscription System](#subscription-system)
10. [Email System](#email-system)
11. [API Endpoints](#api-endpoints)
12. [Security Implementation](#security-implementation)
13. [Common Utilities](#common-utilities)
14. [Testing Guide](#testing-guide)
15. [Troubleshooting](#troubleshooting)

## Introduction

PressMeet247 is a comprehensive news platform that combines traditional news management with AI-powered news generation. The system includes user authentication, admin management, subscription handling, and AI integration for generating news content.

This documentation provides a detailed overview of the backend architecture, database structure, and data flow to help developers understand and work with the system.

## System Architecture

The PressMeet247 backend is built using the following technologies:

- **Spring Boot**: Core framework for building the application
- **Spring Security**: For authentication and authorization
- **Spring Data JPA**: For database access and ORM
- **MySQL**: Primary database for storing application data
- **Redis**: For caching and session management
- **JWT**: For stateless authentication
- **OAuth2**: For social login integration
- **Azure OpenAI**: For AI-powered news generation

The system follows a modular architecture organized by domain, with each module handling a specific aspect of the application:

```
┌─────────────────────────────────────────────────────────────────┐
│                      PressMeet247 Backend                       │
└─────────────────────────────────────────────────────────────────┘
          │                │                │                │
          ▼                ▼                ▼                ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     Auth    │    │    Admin    │    │   AI News   │    │ Subscription │
│    Module   │    │    Module   │    │    Module   │    │    Module    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
          │                │                │                │
          └────────────────┴────────────────┴────────────────┘
                                    │
                                    ▼
                          ┌─────────────────────┐
                          │    Common Module    │
                          │  (Shared Utilities) │
                          └─────────────────────┘
                                    │
                                    ▼
                          ┌─────────────────────┐
                          │      Database       │
                          └─────────────────────┘
```

## Project Structure

The project follows a modular structure organized by domain. Each domain has its own set of controllers, services, repositories, entities, and DTOs.

```
src/main/java/com/pressmeet247/
├── AuthServiceApplication.java (Main application class)
├── auth/ (Authentication module)
│   ├── controller/ (REST controllers)
│   ├── dto/ (Data Transfer Objects)
│   │   ├── request/ (Request DTOs)
│   │   └── response/ (Response DTOs)
│   ├── entity/ (JPA entities)
│   ├── repository/ (Spring Data repositories)
│   ├── service/ (Business logic)
│   ├── exception/ (Custom exceptions)
│   └── token/ (Token management)
├── admin/ (Admin module)
│   ├── controller/ (Admin REST controllers)
│   ├── dto/ (Admin DTOs)
│   ├── service/ (Admin business logic)
│   └── util/ (Admin utilities)
├── ai_news/ (AI News module)
│   ├── controller/ (News REST controllers)
│   ├── dto/ (News DTOs)
│   ├── entity/ (News entities)
│   ├── repository/ (News repositories)
│   └── service/ (News business logic)
├── subscription/ (Subscription module)
│   ├── controller/ (Subscription REST controllers)
│   ├── dto/ (Subscription DTOs)
│   ├── entity/ (Subscription entities)
│   ├── repository/ (Subscription repositories)
│   └── service/ (Subscription business logic)
├── broadcast/ (Broadcast messaging module)
│   ├── controller/ (Broadcast REST controllers)
│   ├── dto/ (Broadcast DTOs)
│   ├── entity/ (Broadcast entities)
│   ├── repository/ (Broadcast repositories)
│   └── service/ (Broadcast business logic)
├── email/ (Email module)
│   ├── dto/ (Email DTOs)
│   └── service/ (Email business logic)
└── common/ (Common utilities)
    ├── base/ (Base classes)
    ├── config/ (Configuration classes)
    ├── exception/ (Global exception handling)
    ├── jwt/ (JWT utilities)
    ├── oauth2/ (OAuth2 utilities)
    ├── security/ (Security configuration)
    └── util/ (Utility classes)
```

## Data Flow Diagrams

### High-Level Data Flow

```
┌──────────────┐     ┌───────────────┐     ┌──────────────┐
│              │     │               │     │              │
│    Client    │◄───►│  API Gateway  │◄───►│   Backend    │
│              │     │               │     │              │
└──────────────┘     └───────────────┘     └──────┬───────┘
                                                  │
                                                  ▼
                                          ┌──────────────┐
                                          │              │
                                          │   Database   │
                                          │              │
                                          └──────────────┘
```

### Authentication Flow

```
┌──────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│          │     │               │     │               │     │               │
│  Client  │────►│  Auth Request │────►│ Authentication│────►│  JWT Token    │
│          │     │               │     │   Service     │     │  Generation   │
└──────────┘     └───────────────┘     └───────┬───────┘     └───────┬───────┘
     ▲                                         │                     │
     │                                         ▼                     │
     │                                 ┌───────────────┐             │
     │                                 │               │             │
     └─────────────────────────────────┤  JWT Response │◄────────────┘
                                       │               │
                                       └───────────────┘
```

### Admin Authentication Flow

```
┌──────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│          │     │               │     │               │     │               │
│  Admin   │────►│  Login        │────►│ Authentication│────►│  OTP          │
│  Client  │     │  Request      │     │   Service     │     │  Generation   │
└──────────┘     └───────────────┘     └───────┬───────┘     └───────┬───────┘
     ▲                                         │                     │
     │                                         ▼                     ▼
     │                                 ┌───────────────┐     ┌───────────────┐
     │                                 │               │     │               │
     │                                 │  Email OTP    │     │  Email        │
     │                                 │  to Admin     │     │  Service      │
     │                                 │               │     │               │
     │                                 └───────┬───────┘     └───────────────┘
     │                                         │
     │                                         ▼
     │                                 ┌───────────────┐     ┌───────────────┐
     │                                 │               │     │               │
     │                                 │  OTP          │────►│  JWT Token    │
     │                                 │  Verification │     │  Generation   │
     │                                 │               │     │               │
     │                                 └───────────────┘     └───────┬───────┘
     │                                                               │
     │                                                               ▼
     │                                                       ┌───────────────┐
     │                                                       │               │
     └───────────────────────────────────────────────────────┤  JWT Response │
                                                             │               │
                                                             └───────────────┘
```

### News Generation Flow

```
┌──────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│          │     │               │     │               │     │               │
│  Client  │────►│  News Request │────►│  AI Service   │────►│  Azure OpenAI │
│          │     │               │     │               │     │               │
└──────────┘     └───────────────┘     └───────┬───────┘     └───────┬───────┘
     ▲                                         │                     │
     │                                         │                     │
     │                                         │                     ▼
     │                                         │             ┌───────────────┐
     │                                         │             │               │
     │                                         │             │  AI Generated │
     │                                         │             │  Content      │
     │                                         │             │               │
     │                                         │             └───────┬───────┘
     │                                         │                     │
     │                                         ▼                     │
     │                                 ┌───────────────┐             │
     │                                 │               │             │
     │                                 │  News Service │◄────────────┘
     │                                 │               │
     │                                 └───────┬───────┘
     │                                         │
     │                                         ▼
     │                                 ┌───────────────┐
     │                                 │               │
     └─────────────────────────────────┤  News Response│
                                       │               │
                                       └───────────────┘
```

## Database Structure

### Entity Relationship Diagram

```
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│               │       │               │       │               │
│     User      │───┐   │     Role      │       │   UserPrefs   │
│               │   │   │               │       │               │
└───────┬───────┘   │   └───────┬───────┘       └───────┬───────┘
        │           │           │                       │
        │           │           │                       │
        │           │   ┌───────────────┐               │
        │           └──►│               │               │
        └──────────────►│   UserRole    │◄──────────────┘
                        │               │
                        └───────────────┘
                                │
                                │
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│               │       │               │       │               │
│  AccountVerif │◄──────┤ SecurityEvent │       │  OauthToken   │
│               │       │               │       │               │
└───────────────┘       └───────────────┘       └───────────────┘
        │                       │                       │
        │                       │                       │
        │                       │                       │
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│               │       │               │       │               │
│ ResetPassword │       │ LoginHistory  │       │ Subscription  │
│               │       │               │       │               │
└───────────────┘       └───────────────┘       └───────┬───────┘
                                                        │
                                                        │
                                                ┌───────────────┐
                                                │               │
                                                │  SubsPlan     │
                                                │               │
                                                └───────────────┘
                                                        │
                                                        │
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│               │       │               │       │               │
│     News      │       │  NewsCategory │       │  BroadcastMsg │
│               │       │               │       │               │
└───────────────┘       └───────────────┘       └───────────────┘
```

### Database Tables

#### User Management Tables

1. **users**
   - `id` (PK): User ID
   - `email`: User email (unique)
   - `password`: Encrypted password
   - `first_name`: User's first name
   - `last_name`: User's last name
   - `enabled`: Account status
   - `email_verified`: Email verification status
   - `phone_number`: User's phone number
   - `phone_verified`: Phone verification status
   - `profile_image_url`: Profile image URL
   - `last_login_at`: Last login timestamp
   - `failed_login_attempts`: Failed login counter
   - `locked_until`: Account lockout timestamp
   - `two_factor_enabled`: 2FA status
   - `two_factor_secret`: 2FA secret key
   - `created_at`: Creation timestamp
   - `updated_at`: Last update timestamp
   - `created_by`: Creator
   - `updated_by`: Last updater
   - `deleted`: Soft delete flag
   - `deleted_at`: Deletion timestamp
   - `deleted_by`: Deleter
   - `version`: Optimistic locking version

2. **roles**
   - `id` (PK): Role ID
   - `name`: Role name (e.g., "ADMIN", "USER")
   - `description`: Role description
   - `is_default`: Default role flag
   - `priority`: Role priority
   - Standard audit fields

3. **user_roles** (Junction table)
   - `user_id` (FK): User ID
   - `role_id` (FK): Role ID

4. **user_preferences**
   - `id` (PK): Preference ID
   - `user_id` (FK): User ID
   - `notifications_enabled`: Notifications toggle
   - `email_notifications`: Email notifications toggle
   - `push_notifications`: Push notifications toggle
   - `sms_notifications`: SMS notifications toggle
   - `language`: Preferred language
   - `theme`: UI theme preference
   - `timezone`: User's timezone
   - `date_format`: Preferred date format
   - `time_format`: Preferred time format
   - Standard audit fields

#### Authentication Tables

5. **account_verifications**
   - `id` (PK): Verification ID
   - `user_id` (FK): User ID
   - `token`: Verification token
   - `otp_code`: OTP verification code
   - `expires_at`: Expiration timestamp
   - `verified_at`: Verification timestamp
   - Standard audit fields

6. **reset_password_tokens**
   - `id` (PK): Token ID
   - `user_id` (FK): User ID
   - `token`: Reset token
   - `expiration_time`: Expiration timestamp
   - `used_at`: Usage timestamp
   - Standard audit fields

7. **oauth_tokens**
   - `id` (PK): Token ID (UUID)
   - `user_id` (FK): User ID
   - `access_token`: JWT access token
   - `refresh_token`: Refresh token
   - `expiration_time`: Expiration timestamp
   - `ip_address`: Client IP
   - `user_agent`: Client user agent
   - `device_type`: Client device type
   - `location`: Client location
   - `revoked`: Revocation flag
   - `revoked_at`: Revocation timestamp
   - Standard audit fields

8. **login_history**
   - `id` (PK): History ID
   - `user_id` (FK): User ID
   - `attempt_time`: Login attempt timestamp
   - `successful`: Success flag
   - `ip_address`: Client IP
   - `user_agent`: Client user agent
   - `device_type`: Client device type
   - `location`: Client location
   - `failure_reason`: Failure reason if unsuccessful
   - Standard audit fields

9. **security_events**
   - `id` (PK): Event ID
   - `user_id` (FK): User ID
   - `event_type`: Event type (e.g., "PASSWORD_CHANGE")
   - `description`: Event description
   - `ip_address`: Client IP
   - `user_agent`: Client user agent
   - `device_type`: Client device type
   - `location`: Client location
   - `additional_data`: Additional event data
   - Standard audit fields

#### News Tables

10. **news**
    - `id` (PK): News ID
    - `title`: News title
    - `content`: News content
    - `summary`: News summary
    - `author_id` (FK): Author user ID
    - `category_id` (FK): Category ID
    - `status`: News status (DRAFT, PENDING, PUBLISHED, REJECTED)
    - `published_at`: Publication timestamp
    - `featured`: Featured flag
    - `image_url`: Featured image URL
    - `source`: News source
    - `source_url`: Source URL
    - `ai_generated`: AI generation flag
    - `ai_prompt`: Prompt used for AI generation
    - `view_count`: View counter
    - `like_count`: Like counter
    - `share_count`: Share counter
    - Standard audit fields

11. **news_categories**
    - `id` (PK): Category ID
    - `name`: Category name
    - `description`: Category description
    - `parent_id` (FK): Parent category ID
    - `icon`: Category icon
    - `display_order`: Display order
    - Standard audit fields

#### Subscription Tables

12. **subscription_plans**
    - `id` (PK): Plan ID
    - `name`: Plan name
    - `description`: Plan description
    - `price`: Plan price
    - `currency`: Currency code
    - `billing_cycle`: Billing cycle (MONTHLY, YEARLY)
    - `features`: Plan features (JSON)
    - `active`: Active flag
    - `trial_days`: Trial period in days
    - Standard audit fields

13. **subscriptions**
    - `id` (PK): Subscription ID
    - `user_id` (FK): User ID
    - `plan_id` (FK): Plan ID
    - `status`: Subscription status
    - `start_date`: Start date
    - `end_date`: End date
    - `trial_end_date`: Trial end date
    - `payment_method`: Payment method
    - `payment_id`: Payment reference ID
    - `auto_renew`: Auto-renewal flag
    - `cancelled_at`: Cancellation timestamp
    - Standard audit fields

#### Broadcast Tables

14. **broadcast_messages**
    - `id` (PK): Message ID
    - `title`: Message title
    - `content`: Message content
    - `sender_id` (FK): Sender user ID
    - `message_type`: Message type
    - `status`: Message status
    - `scheduled_at`: Scheduled timestamp
    - `sent_at`: Sent timestamp
    - `target_audience`: Target audience criteria (JSON)
    - Standard audit fields

15. **message_delivery_status**
    - `id` (PK): Delivery ID
    - `message_id` (FK): Message ID
    - `user_id` (FK): Recipient user ID
    - `status`: Delivery status
    - `delivered_at`: Delivery timestamp
    - `read_at`: Read timestamp
    - `error`: Error message if delivery failed
    - Standard audit fields

## Authentication Flow

### Regular User Authentication

1. **Registration Process**:
   - User submits registration form with email, password, and personal details
   - System validates input and checks for existing email
   - System creates a new user with status `enabled=false` and `emailVerified=false`
   - System generates a verification token and sends it to the user's email
   - User clicks the verification link or enters OTP code
   - System verifies the token/OTP and updates user status to `enabled=true` and `emailVerified=true`

2. **Login Process**:
   - User submits login form with email and password
   - System validates credentials
   - System generates JWT access and refresh tokens
   - System returns tokens to the client
   - Client stores tokens and includes them in subsequent requests

3. **Token Refresh Process**:
   - Client sends refresh token to refresh endpoint
   - System validates refresh token
   - System generates new access token
   - System returns new access token to the client

4. **Password Reset Process**:
   - User requests password reset with email
   - System generates reset token and sends it to the user's email
   - User clicks reset link and enters new password
   - System validates token and updates password

### OAuth2 Authentication

1. **OAuth2 Login Process**:
   - User clicks "Login with Google" button
   - System redirects to Google authentication page
   - User authenticates with Google
   - Google redirects back to the application with authorization code
   - System exchanges code for access token
   - System retrieves user info from Google
   - System creates or updates user record
   - System generates JWT tokens
   - System returns tokens to the client

### Admin Authentication

1. **Admin Login Process**:
   - Admin submits login form with email and password
   - System validates credentials and checks for admin role
   - System generates and sends OTP to admin's email
   - Admin enters OTP
   - System validates OTP
   - System generates JWT tokens with admin privileges
   - System returns tokens to the client

## Admin System

The admin system provides a comprehensive interface for managing all aspects of the application:

### Admin Dashboard

- **Overview**: Key metrics and statistics
- **User Management**: View, create, update, and delete users
- **Role Management**: Manage user roles and permissions
- **News Management**: Approve, reject, edit, and delete news articles
- **Subscription Management**: Manage subscription plans and user subscriptions
- **Broadcast Management**: Send and manage broadcast messages

### Admin API Endpoints

All admin endpoints are secured with JWT authentication and require the ADMIN role:

- **Admin Authentication**: `/api/admin/auth/**`
- **Admin Dashboard**: `/api/admin/dashboard/**`
- **Admin User Management**: `/api/admin/users/**`
- **Admin News Management**: `/api/admin/news/**`
- **Admin Subscription Management**: `/api/admin/subscriptions/**`
- **Admin Broadcast Management**: `/api/admin/broadcasts/**`

## AI News Generation

The AI news generation system uses Azure OpenAI to generate news content based on user prompts:

### News Generation Flow

1. User submits news generation request with topic and parameters
2. System validates request and user subscription status
3. System sends prompt to Azure OpenAI API
4. Azure OpenAI generates news content
5. System processes and formats the generated content
6. System saves the news article with status `DRAFT` and `aiGenerated=true`
7. User can edit and submit the article for review
8. Admin reviews and approves or rejects the article

### AI Service Components

- **AIService**: Main service for AI operations
- **AzureOpenAIClient**: Client for Azure OpenAI API
- **PromptBuilder**: Utility for building effective prompts
- **AIResponseProcessor**: Processes and formats AI responses

## Subscription System

The subscription system manages user subscriptions and access to premium features:

### Subscription Flow

1. User browses available subscription plans
2. User selects a plan and proceeds to checkout
3. System processes payment (integration with payment gateway)
4. System creates subscription record
5. System grants access to premium features
6. System manages subscription lifecycle (renewal, cancellation, etc.)

### Subscription Components

- **SubscriptionService**: Main service for subscription operations
- **SubscriptionPlanService**: Manages subscription plans
- **PaymentService**: Handles payment processing
- **SubscriptionValidator**: Validates subscription status for feature access

## Email System

The email system handles all email communications with users:

### Email Components

- **EmailService**: Main service for email operations
- **EmailTemplateService**: Manages email templates
- **EmailSender**: Sends emails using SMTP

### Email Types

- **Verification Emails**: For account verification
- **Password Reset Emails**: For password reset
- **OTP Emails**: For two-factor authentication
- **Notification Emails**: For system notifications
- **Newsletter Emails**: For newsletters and updates

## API Endpoints

### Authentication Endpoints

- **POST /api/v1/authentication/register**: Register a new user
- **POST /api/v1/authentication/login**: Authenticate a user
- **POST /api/v1/authentication/refresh-token**: Refresh access token
- **POST /api/v1/authentication/logout**: Logout and invalidate tokens
- **GET /api/v1/authentication/validate-email-verification-token**: Validate email verification token
- **POST /api/v1/authentication/send-email-verification-token**: Resend verification email
- **POST /api/v1/authentication/forgot-password**: Request password reset
- **POST /api/v1/authentication/reset-password**: Reset password
- **POST /api/v1/authentication/verify-otp**: Verify OTP code

### User Endpoints

- **GET /api/v1/users/me**: Get current user profile
- **PUT /api/v1/users/me**: Update current user profile
- **PUT /api/v1/users/me/password**: Change password
- **PUT /api/v1/users/me/preferences**: Update user preferences
- **GET /api/v1/users/me/security-events**: Get security events

### News Endpoints

- **GET /api/news/published**: Get published news articles
- **GET /api/news/featured**: Get featured news articles
- **GET /api/news/type/{type}**: Get news by type
- **GET /api/news/{id}**: Get news article by ID
- **POST /api/news/generate**: Generate news with AI
- **POST /api/news**: Create news article
- **PUT /api/news/{id}**: Update news article
- **DELETE /api/news/{id}**: Delete news article

### Subscription Endpoints

- **GET /api/subscriptions/plans**: Get subscription plans
- **GET /api/subscriptions/me**: Get current user subscription
- **POST /api/subscriptions**: Create subscription
- **PUT /api/subscriptions/{id}**: Update subscription
- **DELETE /api/subscriptions/{id}**: Cancel subscription

### Admin Endpoints

- **POST /api/admin/auth/login/initiate**: Initiate admin login
- **POST /api/admin/auth/login/verify**: Verify admin login OTP
- **GET /api/admin/dashboard**: Get dashboard overview
- **GET /api/admin/dashboard/stats**: Get dashboard statistics
- **GET /api/admin/dashboard/users**: Get all users
- **GET /api/admin/dashboard/news**: Get all news
- **PUT /api/admin/news/{id}/approve**: Approve news article
- **PUT /api/admin/news/{id}/reject**: Reject news article
- **GET /api/admin/subscriptions**: Get all subscriptions
- **PUT /api/admin/subscriptions/{id}**: Update subscription

## Security Implementation

### JWT Security

- **JwtUtils**: Utility for JWT operations
- **JwtAuthenticationFilter**: Filter for JWT authentication
- **JwtProperties**: JWT configuration properties

### Password Security

- **PasswordEncoder**: BCrypt password encoder
- **PasswordValidator**: Password strength validator

### Role-Based Access Control

- **SecurityConfig**: Security configuration
- **MethodSecurity**: Method-level security annotations

### Rate Limiting

- **RateLimitingFilter**: Rate limiting implementation
- **RateLimitingProperties**: Rate limiting configuration

## Common Utilities

### Base Classes

- **BaseEntity**: Base class for all entities
- **UuidEntity**: Base class for entities with UUID primary key
- **BaseController**: Base class for controllers
- **BaseService**: Base class for services

### Exception Handling

- **GlobalExceptionHandler**: Global exception handler
- **ApiException**: Base exception class
- **AuthenticationException**: Authentication-related exceptions
- **ResourceNotFoundException**: Resource not found exception

### Audit

- **AuditorAware**: Provides the current auditor
- **AuditingConfig**: Auditing configuration

## Testing Guide

### Unit Testing

- **Controller Tests**: Test controllers in isolation
- **Service Tests**: Test service layer logic
- **Repository Tests**: Test database operations

### Integration Testing

- **API Tests**: Test API endpoints
- **Security Tests**: Test security features
- **Database Tests**: Test database interactions

### End-to-End Testing

- **User Flows**: Test complete user flows
- **Admin Flows**: Test admin workflows

## Troubleshooting

### Common Issues

- **Authentication Issues**: JWT token problems, expired tokens
- **Database Issues**: Connection problems, query errors
- **Email Issues**: SMTP configuration, email delivery
- **AI Integration Issues**: API connectivity, prompt formatting

### Logging

- **Application Logs**: Main application logs
- **Security Logs**: Authentication and authorization logs
- **Database Logs**: Database operation logs
- **API Logs**: API request and response logs

### Monitoring

- **Health Endpoints**: System health monitoring
- **Metrics**: Performance metrics
- **Alerts**: System alerts and notifications

---

This documentation provides a comprehensive overview of the PressMeet247 backend system. For more detailed information on specific components, refer to the code documentation and comments.
