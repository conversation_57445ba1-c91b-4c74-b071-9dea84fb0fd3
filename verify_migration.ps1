# Script to verify the migration is complete and correct

# Function to check package declaration in a file
function Check-Package {
    param (
        [string]$filePath,
        [string]$expectedPackage
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw

        # Check if the package declaration is correct
        $packagePattern = "package\s+([^;]+);"
        $match = [regex]::Match($content, $packagePattern)

        if ($match.Success) {
            $currentPackage = $match.Groups[1].Value

            if ($currentPackage -ne $expectedPackage) {
                Write-Host "ISSUE: Incorrect package in $filePath"
                Write-Host "  Current: $currentPackage"
                Write-Host "  Expected: $expectedPackage"
                return $false
            }
        }
        else {
            Write-Host "ISSUE: No package declaration found in $filePath"
            return $false
        }
    }
    else {
        Write-Host "ISSUE: File not found: $filePath"
        return $false
    }

    return $true
}

# Function to check for old package references in a file
function Check-Imports {
    param (
        [string]$filePath
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        $issues = @()

        # Check for old package references
        if ($content -match "import com.auth.service") {
            $issues += "Old package reference: com.auth.service"
        }

        if ($issues.Count -gt 0) {
            Write-Host "ISSUE: Import issues in $filePath"
            foreach ($issue in $issues) {
                Write-Host "  $issue"
            }
            return $false
        }
    }
    else {
        Write-Host "ISSUE: File not found: $filePath"
        return $false
    }

    return $true
}

# Define package mappings for verification
$packageMappings = @{
    # Main application
    "src\main\java\com\pressmeet247\AuthServiceApplication.java" = "com.pressmeet247"
    "src\main\java\com\pressmeet247\config\ComponentScanConfig.java" = "com.pressmeet247.config"

    # Package info files
    "src\main\java\com\pressmeet247\auth\package-info.java" = "com.pressmeet247.auth"
    "src\main\java\com\pressmeet247\admin\package-info.java" = "com.pressmeet247.admin"
    "src\main\java\com\pressmeet247\ai_news\package-info.java" = "com.pressmeet247.ai_news"
    "src\main\java\com\pressmeet247\broadcast\package-info.java" = "com.pressmeet247.broadcast"
    "src\main\java\com\pressmeet247\subscription\package-info.java" = "com.pressmeet247.subscription"
    "src\main\java\com\pressmeet247\email\package-info.java" = "com.pressmeet247.email"
    "src\main\java\com\pressmeet247\common\package-info.java" = "com.pressmeet247.common"

    # Auth module
    "src\main\java\com\pressmeet247\auth\controller" = "com.pressmeet247.auth.controller"
    "src\main\java\com\pressmeet247\auth\service" = "com.pressmeet247.auth.service"
    "src\main\java\com\pressmeet247\auth\dto\request" = "com.pressmeet247.auth.dto.request"
    "src\main\java\com\pressmeet247\auth\dto\response" = "com.pressmeet247.auth.dto.response"
    "src\main\java\com\pressmeet247\auth\entity" = "com.pressmeet247.auth.entity"
    "src\main\java\com\pressmeet247\auth\repository" = "com.pressmeet247.auth.repository"
    "src\main\java\com\pressmeet247\auth\exception" = "com.pressmeet247.auth.exception"
    "src\main\java\com\pressmeet247\auth\token" = "com.pressmeet247.auth.token"
    "src\main\java\com\pressmeet247\auth\util" = "com.pressmeet247.auth.util"

    # Admin module
    "src\main\java\com\pressmeet247\admin\controller" = "com.pressmeet247.admin.controller"
    "src\main\java\com\pressmeet247\admin\service" = "com.pressmeet247.admin.service"
    "src\main\java\com\pressmeet247\admin\dto" = "com.pressmeet247.admin.dto"
    "src\main\java\com\pressmeet247\admin\config" = "com.pressmeet247.admin.config"

    # AI News module
    "src\main\java\com\pressmeet247\ai_news\controller" = "com.pressmeet247.ai_news.controller"
    "src\main\java\com\pressmeet247\ai_news\service" = "com.pressmeet247.ai_news.service"
    "src\main\java\com\pressmeet247\ai_news\dto" = "com.pressmeet247.ai_news.dto"
    "src\main\java\com\pressmeet247\ai_news\entity" = "com.pressmeet247.ai_news.entity"
    "src\main\java\com\pressmeet247\ai_news\repository" = "com.pressmeet247.ai_news.repository"
    "src\main\java\com\pressmeet247\ai_news\impl" = "com.pressmeet247.ai_news.impl"

    # Broadcast module
    "src\main\java\com\pressmeet247\broadcast\controller" = "com.pressmeet247.broadcast.controller"
    "src\main\java\com\pressmeet247\broadcast\service" = "com.pressmeet247.broadcast.service"
    "src\main\java\com\pressmeet247\broadcast\dto" = "com.pressmeet247.broadcast.dto"
    "src\main\java\com\pressmeet247\broadcast\entity" = "com.pressmeet247.broadcast.entity"
    "src\main\java\com\pressmeet247\broadcast\repository" = "com.pressmeet247.broadcast.repository"

    # Subscription module
    "src\main\java\com\pressmeet247\subscription\controller" = "com.pressmeet247.subscription.controller"
    "src\main\java\com\pressmeet247\subscription\service" = "com.pressmeet247.subscription.service"
    "src\main\java\com\pressmeet247\subscription\dto" = "com.pressmeet247.subscription.dto"
    "src\main\java\com\pressmeet247\subscription\entity" = "com.pressmeet247.subscription.entity"
    "src\main\java\com\pressmeet247\subscription\repository" = "com.pressmeet247.subscription.repository"

    # Email module
    "src\main\java\com\pressmeet247\email\service" = "com.pressmeet247.email.service"
    "src\main\java\com\pressmeet247\email\config" = "com.pressmeet247.email.config"

    # Common module
    "src\main\java\com\pressmeet247\common\base" = "com.pressmeet247.common.base"
    "src\main\java\com\pressmeet247\common\config" = "com.pressmeet247.common.config"
    "src\main\java\com\pressmeet247\common\config\redis\InMemoryRedisConfig.java" = "com.pressmeet247.common.config.redis"
    "src\main\java\com\pressmeet247\common\config\redis\RedisConfig.java" = "com.pressmeet247.common.config.redis"
    "src\main\java\com\pressmeet247\common\exception" = "com.pressmeet247.common.exception"
    "src\main\java\com\pressmeet247\common\jwt" = "com.pressmeet247.common.jwt"
    "src\main\java\com\pressmeet247\common\oauth2" = "com.pressmeet247.common.oauth2"
    "src\main\java\com\pressmeet247\common\security" = "com.pressmeet247.common.security"
    "src\main\java\com\pressmeet247\common\init" = "com.pressmeet247.common.init"
    "src\main\java\com\pressmeet247\common\controller" = "com.pressmeet247.common.controller"
}

# Get all Java files in the new structure
$javaFiles = Get-ChildItem -Path "src\main\java\com\pressmeet247" -Filter "*.java" -Recurse

# Variables to track verification results
$packageIssues = 0
$importIssues = 0
$totalFiles = $javaFiles.Count

# Check each file
foreach ($file in $javaFiles) {
    $expectedPackage = $null

    # First check if this is a specific file with a mapping
    foreach ($specificFile in $packageMappings.Keys) {
        if ($specificFile -like "*.java" -and $file.FullName -like "*$specificFile*") {
            $expectedPackage = $packageMappings[$specificFile]
            break
        }
    }

    # If no specific file mapping found, check directory mappings
    if (-not $expectedPackage) {
        $fileDirectory = Split-Path -Parent $file.FullName

        foreach ($dir in $packageMappings.Keys) {
            if ($dir -notlike "*.java" -and $fileDirectory -like "*$dir*") {
                $expectedPackage = $packageMappings[$dir]
                break
            }
        }
    }

    # If still no expected package found, use the parent directory name
    if (-not $expectedPackage) {
        $fileDirectory = Split-Path -Parent $file.FullName
        $parentDir = Split-Path -Leaf $fileDirectory
        $grandparentDir = Split-Path -Leaf (Split-Path -Parent $fileDirectory)

        if ($grandparentDir -eq "pressmeet247") {
            $expectedPackage = "com.pressmeet247"
        }
        else {
            $expectedPackage = "com.pressmeet247.$parentDir"
        }
    }

    # Check package declaration
    $packageOk = Check-Package -filePath $file.FullName -expectedPackage $expectedPackage
    if (-not $packageOk) {
        $packageIssues++
    }

    # Check imports
    $importsOk = Check-Imports -filePath $file.FullName
    if (-not $importsOk) {
        $importIssues++
    }
}

# Print summary
Write-Host ""
Write-Host "Verification Summary:"
Write-Host "===================="
Write-Host "Total files checked: $totalFiles"
Write-Host "Package declaration issues: $packageIssues"
Write-Host "Import reference issues: $importIssues"

if ($packageIssues -eq 0 -and $importIssues -eq 0) {
    Write-Host ""
    Write-Host "SUCCESS: Migration completed successfully with no issues detected!"
}
else {
    Write-Host ""
    Write-Host "WARNING: Migration completed with some issues. Please fix the issues listed above."
}
