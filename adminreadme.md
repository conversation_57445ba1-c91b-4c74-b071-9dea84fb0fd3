# Admin API Documentation for Postman Testing

This document provides a comprehensive guide for testing all backend admin and user API endpoints using Postman.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [Authentication](#authentication)
   - [Admin Authentication](#admin-authentication)
   - [User Authentication](#user-authentication)
3. [Admin Dashboard](#admin-dashboard)
4. [User Management](#user-management)
5. [News Management](#news-management)
6. [Subscription Management](#subscription-management)
7. [Broadcast Messaging](#broadcast-messaging)

## Environment Setup

Create a Postman environment with the following variables:

- `baseUrl`: The base URL of your API (e.g., `http://localhost:8080`)
- `adminToken`: For storing the admin JWT token
- `userToken`: For storing the user JWT token
- `refreshToken`: For storing the refresh token

## Authentication

### Admin Authentication

Admin login requires a two-step authentication process with OTP verification.

#### 1. Initiate Admin Login

```
POST {{baseUrl}}/api/admin/auth/login/initiate
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Admin@123"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "OTP sent successfully. Please verify to complete login.",
  "data": true,
  "timestamp": "2025-05-09T10:30:45.123Z"
}
```

#### 2. Verify Admin Login OTP

```
POST {{baseUrl}}/api/admin/auth/login/verify
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Admin@123",
  "otp": "123456"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Admin login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2025-05-09T10:31:00.456Z"
}
```

> **Important**: After successful login, set the `adminToken` environment variable with the received access token.

### User Authentication

#### 1. Register User

```
POST {{baseUrl}}/api/v1/authentication/register
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "Password@123",
  "confirmPassword": "Password@123"
}
```

#### 2. Login User

```
POST {{baseUrl}}/api/v1/authentication/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password@123"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2025-05-09T10:32:15.789Z"
}
```

> **Important**: After successful login, set the `userToken` environment variable with the received access token.

#### 3. Verify OTP (For Email Verification)

```
POST {{baseUrl}}/api/v1/authentication/verify-otp
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

#### 4. Refresh Token

```
POST {{baseUrl}}/api/auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "{{refreshToken}}"
}
```

#### 5. Logout

```
POST {{baseUrl}}/api/auth/logout
```

**Request Body:**
```json
{
  "refreshToken": "{{refreshToken}}"
}
```

## Admin Dashboard

### Get Dashboard Statistics

```
GET {{baseUrl}}/api/admin/dashboard/stats
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Response:**
```json
{
  "status": "success",
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "totalUsers": 100,
    "totalAdmins": 5,
    "totalNews": 50,
    "pendingNews": 10,
    "publishedNews": 35,
    "rejectedNews": 5,
    "totalSubscriptions": 30,
    "activeSubscriptions": 25
  },
  "timestamp": "2025-05-09T10:35:00.123Z"
}
```

## User Management

### Get All Users (Admin Only)

```
GET {{baseUrl}}/api/admin/dashboard/users?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Response:**
```json
{
  "status": "success",
  "message": "Users retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "roles": ["USER"],
        "emailVerified": true,
        "createdAt": "2025-05-01T10:00:00.000Z",
        "updatedAt": "2025-05-01T10:00:00.000Z"
      },
      // More users...
    ],
    "pageable": {
      "sort": {
        "sorted": true,
        "unsorted": false,
        "empty": false
      },
      "pageNumber": 0,
      "pageSize": 10,
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalPages": 10,
    "totalElements": 100,
    "last": false,
    "first": true,
    "sort": {
      "sorted": true,
      "unsorted": false,
      "empty": false
    },
    "number": 0,
    "numberOfElements": 10,
    "size": 10,
    "empty": false
  },
  "timestamp": "2025-05-09T10:36:00.456Z"
}
```

### Get Admin Users (Admin Only)

```
GET {{baseUrl}}/api/admin/dashboard/users/admins?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

### Get User by ID (Admin or Self)

```
GET {{baseUrl}}/api/users/{id}
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

### Get User by Email (Admin Only)

```
GET {{baseUrl}}/api/users/by-email/{email}
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

### Get Current User

```
GET {{baseUrl}}/api/users/me
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

## News Management

### Get All News (Admin Only)

```
GET {{baseUrl}}/api/admin/dashboard/news?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Response:**
```json
{
  "status": "success",
  "message": "News retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "userId": 2,
        "userName": "Jane Smith",
        "title": "Local Community Event",
        "content": "Lorem ipsum dolor sit amet...",
        "date": "2025-05-15",
        "place": "City Hall",
        "newsType": "LOCAL",
        "status": "PENDING",
        "featured": false,
        "createdAt": "2025-05-08T14:30:00.000Z",
        "publishedAt": null
      },
      // More news...
    ],
    // Pagination info...
  },
  "timestamp": "2025-05-09T10:37:00.789Z"
}
```

### Get News by Status (Admin Only)

```
GET {{baseUrl}}/api/admin/dashboard/news/status/{status}?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `status`: One of `PENDING`, `PUBLISHED`, `REJECTED`

### Approve News (Admin Only)

```
POST {{baseUrl}}/api/admin/dashboard/news/{id}/approve
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `id`: News ID

**Response:**
```json
{
  "status": "success",
  "message": "News approved successfully",
  "data": {
    "id": 1,
    "userId": 2,
    "userName": "Jane Smith",
    "title": "Local Community Event",
    "content": "Lorem ipsum dolor sit amet...",
    "date": "2025-05-15",
    "place": "City Hall",
    "newsType": "LOCAL",
    "status": "PUBLISHED",
    "featured": false,
    "createdAt": "2025-05-08T14:30:00.000Z",
    "publishedAt": "2025-05-09T10:38:00.123Z"
  },
  "timestamp": "2025-05-09T10:38:00.123Z"
}
```

### Reject News (Admin Only)

```
POST {{baseUrl}}/api/admin/dashboard/news/{id}/reject
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `id`: News ID

### Set News as Featured (Admin Only)

```
PUT {{baseUrl}}/api/admin/dashboard/news/{id}/featured?featured=true
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `id`: News ID

**Query Parameters:**
- `featured`: Boolean (true/false)

### Get Published News (Public)

```
GET {{baseUrl}}/api/news/published?page=0&size=10&sort=publishedAt,desc
```

### Get Featured News (Public)

```
GET {{baseUrl}}/api/news/featured?page=0&size=10&sort=publishedAt,desc
```

### Get News by Type (Public)

```
GET {{baseUrl}}/api/news/type/{type}?page=0&size=10&sort=publishedAt,desc
```

**Path Parameters:**
- `type`: News type (e.g., `LOCAL`, `INTERNATIONAL`, `SPORTS`, etc.)

### Get News by ID

```
GET {{baseUrl}}/api/news/{id}
```

**Path Parameters:**
- `id`: News ID

### Get My News (Authenticated User)

```
GET {{baseUrl}}/api/news/my-news?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Generate News using AI (Authenticated User)

```
POST {{baseUrl}}/api/news/generate
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

**Request Body:**
```json
{
  "prompt": "Write a news article about recent technological advancements",
  "newsType": "TECHNOLOGY"
}
```

## Subscription Management

### Create Subscription Plan (Admin Only)

```
POST {{baseUrl}}/api/subscriptions/plans
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Request Body:**
```json
{
  "name": "Premium Plan",
  "description": "Access to all premium features",
  "price": 9.99,
  "currency": "USD",
  "durationMonths": 1,
  "features": ["Unlimited news generation", "Priority support", "Ad-free experience"]
}
```

### Update Subscription Plan (Admin Only)

```
PUT {{baseUrl}}/api/subscriptions/plans/{id}
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `id`: Plan ID

**Request Body:**
```json
{
  "name": "Premium Plus Plan",
  "description": "Enhanced premium features",
  "price": 14.99,
  "currency": "USD",
  "durationMonths": 1,
  "features": ["Unlimited news generation", "Priority support", "Ad-free experience", "Early access to new features"],
  "active": true
}
```

### Get All Subscription Plans

```
GET {{baseUrl}}/api/subscriptions/plans?page=0&size=10&sort=price,asc
```

### Create Subscription (Authenticated User)

```
POST {{baseUrl}}/api/subscriptions
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

**Request Body:**
```json
{
  "planId": 1,
  "paymentMethod": "CREDIT_CARD",
  "paymentReference": "payment_ref_123456"
}
```

### Get Active Subscription (Authenticated User)

```
GET {{baseUrl}}/api/subscriptions/active
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Get My Subscriptions (Authenticated User)

```
GET {{baseUrl}}/api/subscriptions/my-subscriptions?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Cancel Subscription (Authenticated User)

```
POST {{baseUrl}}/api/subscriptions/{id}/cancel
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

**Path Parameters:**
- `id`: Subscription ID

### Renew Subscription (Authenticated User)

```
POST {{baseUrl}}/api/subscriptions/{id}/renew
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

**Path Parameters:**
- `id`: Subscription ID

**Request Body:**
```json
{
  "paymentMethod": "CREDIT_CARD",
  "paymentReference": "payment_ref_789012"
}
```

### Check Active Subscription (Authenticated User)

```
GET {{baseUrl}}/api/subscriptions/check-active
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Get Expiring Subscriptions (Admin Only)

```
GET {{baseUrl}}/api/subscriptions/expiring?days=7&page=0&size=10&sort=expiresAt,asc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Query Parameters:**
- `days`: Number of days until expiry (default: 7)

### Notify Expiring Subscriptions (Admin Only)

```
POST {{baseUrl}}/api/subscriptions/notify-expiring?days=7
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Query Parameters:**
- `days`: Number of days until expiry (default: 7)

## Broadcast Messaging

### Create Broadcast Message (Admin Only)

```
POST {{baseUrl}}/api/messages
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Request Body:**
```json
{
  "title": "Important Announcement",
  "content": "This is an important announcement for all users.",
  "type": "ANNOUNCEMENT",
  "targetAllUsers": true,
  "targetPremiumUsers": false,
  "specificUserIds": []
}
```

### Get Broadcast Message by ID (Admin Only)

```
GET {{baseUrl}}/api/messages/{id}
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `id`: Message ID

### Get All Broadcast Messages (Admin Only)

```
GET {{baseUrl}}/api/messages?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

### Get My Broadcast Messages (Authenticated User)

```
GET {{baseUrl}}/api/messages/my-messages?page=0&size=10&sort=createdAt,desc
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Get Unread Message Count (Authenticated User)

```
GET {{baseUrl}}/api/messages/unread-count
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

### Mark Message as Read (Authenticated User)

```
POST {{baseUrl}}/api/messages/{id}/read
```

**Headers:**
```
Authorization: Bearer {{userToken}}
```

**Path Parameters:**
- `id`: Message ID

### Get Delivery Status (Admin Only)

```
GET {{baseUrl}}/api/messages/{messageId}/status/{recipientId}
```

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

**Path Parameters:**
- `messageId`: Message ID
- `recipientId`: Recipient User ID

## Testing Tips

1. **Authentication Flow**:
   - Always start by authenticating as an admin or user
   - Store the received tokens in environment variables
   - Use the appropriate token for each request

2. **Pagination**:
   - Most endpoints that return collections support pagination
   - Use `page`, `size`, and `sort` query parameters to control pagination
   - Example: `?page=0&size=10&sort=createdAt,desc`

3. **Error Handling**:
   - Check response status codes and error messages
   - 400-level errors indicate client-side issues
   - 500-level errors indicate server-side issues

4. **Content Types**:
   - All requests and responses use JSON format
   - Set `Content-Type: application/json` header for POST and PUT requests

5. **Testing Sequence**:
   - Test authentication endpoints first
   - Then test read operations
   - Finally test write operations

## Postman Collection Setup

1. Create a new Postman collection named "PressMeet247 Admin API"
2. Create folders for each section (Authentication, Dashboard, Users, News, etc.)
3. Add requests to each folder
4. Set up pre-request scripts to automatically handle authentication
5. Use environment variables for dynamic values

Example pre-request script for authentication:

```javascript
// Check if token is expired and refresh if needed
const tokenExpiry = pm.environment.get("tokenExpiry");
const now = new Date().getTime();

if (tokenExpiry && now > tokenExpiry) {
    // Token is expired, refresh it
    pm.sendRequest({
        url: pm.environment.get("baseUrl") + "/api/auth/refresh",
        method: "POST",
        header: {
            "Content-Type": "application/json"
        },
        body: {
            mode: "raw",
            raw: JSON.stringify({
                refreshToken: pm.environment.get("refreshToken")
            })
        }
    }, function (err, res) {
        if (!err && res.code === 200) {
            const responseJson = res.json();
            if (responseJson.data) {
                pm.environment.set("adminToken", responseJson.data.accessToken);
                pm.environment.set("refreshToken", responseJson.data.refreshToken);
                
                // Set expiry time (current time + expiresIn seconds)
                const expiryTime = now + (responseJson.data.expiresIn * 1000);
                pm.environment.set("tokenExpiry", expiryTime);
            }
        }
    });
}
```
