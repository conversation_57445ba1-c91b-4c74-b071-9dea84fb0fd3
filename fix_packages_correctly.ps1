# Script to fix package declarations correctly

# Function to fix package declaration in a file
function Fix-Package {
    param (
        [string]$filePath,
        [string]$correctPackage
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Check if the package declaration is correct
        $packagePattern = "package\s+([^;]+);"
        $match = [regex]::Match($content, $packagePattern)
        
        if ($match.Success) {
            $currentPackage = $match.Value
            
            if ($currentPackage -ne "package $correctPackage;") {
                Write-Host "Fixing package in $filePath"
                Write-Host "  From: $currentPackage"
                Write-Host "  To:   package $correctPackage;"
                
                $content = $content -replace [regex]::Escape($currentPackage), "package $correctPackage;"
                Set-Content -Path $filePath -Value $content
            }
        }
    }
    else {
        Write-Host "File not found: $filePath"
    }
}

# Fix auth module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\controller" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.controller"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.service"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\dto\request" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.dto.request"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\dto\response" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.dto.response"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\entity" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.entity"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\repository" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.repository"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\exception" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.exception"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\auth\token" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.auth.token"
}

# Fix admin module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\admin\controller" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.admin.controller"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\admin\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.admin.service"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\admin\dto" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.admin.dto"
}

# Fix ai_news module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\ai_news\controller" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.ai_news.controller"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\ai_news\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.ai_news.service"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\ai_news\entity" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.ai_news.entity"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\ai_news\repository" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.ai_news.repository"
}

# Fix broadcast module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\broadcast\controller" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.broadcast.controller"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\broadcast\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.broadcast.service"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\broadcast\entity" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.broadcast.entity"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\broadcast\repository" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.broadcast.repository"
}

# Fix subscription module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\subscription\controller" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.subscription.controller"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\subscription\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.subscription.service"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\subscription\entity" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.subscription.entity"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\subscription\repository" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.subscription.repository"
}

# Fix email module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\email\service" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.email.service"
}

# Fix common module packages
Get-ChildItem -Path "src\main\java\com\pressmeet247\common\base" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.base"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\common\config" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.config"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\common\exception" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.exception"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\common\jwt" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.jwt"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\common\security" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.security"
}

Get-ChildItem -Path "src\main\java\com\pressmeet247\common\init" -Filter "*.java" | ForEach-Object {
    Fix-Package -filePath $_.FullName -correctPackage "com.pressmeet247.common.init"
}

# Fix package-info.java files
Fix-Package -filePath "src\main\java\com\pressmeet247\auth\package-info.java" -correctPackage "com.pressmeet247.auth"
Fix-Package -filePath "src\main\java\com\pressmeet247\admin\package-info.java" -correctPackage "com.pressmeet247.admin"
Fix-Package -filePath "src\main\java\com\pressmeet247\ai_news\package-info.java" -correctPackage "com.pressmeet247.ai_news"
Fix-Package -filePath "src\main\java\com\pressmeet247\broadcast\package-info.java" -correctPackage "com.pressmeet247.broadcast"
Fix-Package -filePath "src\main\java\com\pressmeet247\subscription\package-info.java" -correctPackage "com.pressmeet247.subscription"
Fix-Package -filePath "src\main\java\com\pressmeet247\email\package-info.java" -correctPackage "com.pressmeet247.email"
Fix-Package -filePath "src\main\java\com\pressmeet247\common\package-info.java" -correctPackage "com.pressmeet247.common"

# Fix main application class
Fix-Package -filePath "src\main\java\com\pressmeet247\AuthServiceApplication.java" -correctPackage "com.pressmeet247"
Fix-Package -filePath "src\main\java\com\pressmeet247\config\ComponentScanConfig.java" -correctPackage "com.pressmeet247.config"

Write-Host "Package declarations fixed correctly!"
