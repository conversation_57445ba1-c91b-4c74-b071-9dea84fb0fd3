# Admin Authentication Fix: HTTP-Only Cookies Implementation

## Problem Analysis

Based on the logs, we can see the following issues:

1. The token is successfully generated on the backend:
   ```
   2025-05-12 22:16:09 INFO c.p.a.controller.AdminAuthController - Generated token for admin user: <EMAIL>
   2025-05-12 22:16:09 INFO c.p.a.controller.AdminAuthController - Token type: Bearer
   2025-05-12 22:16:09 INFO c.p.a.controller.AdminAuthController - Token expiration: 2025-05-12T23:16:09.345107900
   ```

2. But subsequent requests fail with:
   ```
   2025-05-12 22:16:11 ERROR c.pressmeet247.common.jwt.JwtUtils - Malformed JWT token: JWT strings must contain exactly 2 period characters. Found: 0
   ```

3. This indicates that the JWT token is not being properly sent with the requests, resulting in authentication failures.

## Root Cause

The root cause is that the frontend is not properly configured to:
1. Handle HTTP-only cookies correctly
2. Include credentials with cross-origin requests
3. Properly manage the authentication flow with cookies instead of localStorage/sessionStorage

## Solution: Frontend Implementation for HTTP-Only Cookies

### 1. Update Axios Configuration

```javascript
// src/api/axiosConfig.js
import axios from 'axios';

// Create an axios instance with the correct configuration for cookies
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080/api',
  withCredentials: true, // CRITICAL: This enables sending cookies with cross-origin requests
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add a response interceptor for automatic token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If error is 401 and not a retry, attempt to refresh token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // The refresh token is in the cookie, no need to send it in the request body
        await api.post('/auth/refresh');
        
        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        // You might want to use your router's navigation method instead
        window.location.href = '/admin/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;
```

### 2. Update Admin Authentication Service

```javascript
// src/services/adminAuthService.js
import api from '../api/axiosConfig';

const AdminAuthService = {
  // Step 1: Initiate admin login
  initiateLogin: async (email, password) => {
    try {
      const response = await api.post('/admin/auth/login/initiate', { email, password });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Login initiation failed' };
    }
  },

  // Step 2: Verify OTP
  verifyOtp: async (email, password, otp) => {
    try {
      const response = await api.post('/admin/auth/login/verify', { 
        email, 
        password, 
        otp 
      });
      
      // The JWT token is now stored in an HTTP-only cookie
      // No need to extract and store it manually
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'OTP verification failed' };
    }
  },

  // Get current admin user
  getCurrentAdmin: async () => {
    try {
      const response = await api.get('/admin/auth/me');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to get admin profile' };
    }
  },

  // Logout admin
  logout: async () => {
    try {
      const response = await api.post('/auth/logout');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Logout failed' };
    }
  },

  // Debug authentication status (for development)
  checkAuthStatus: async () => {
    try {
      const response = await api.get('/admin/debug/auth-info');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to check auth status' };
    }
  }
};

export default AdminAuthService;
```

### 3. Update Admin Login Component

```jsx
// src/components/admin/AdminLogin.jsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminAuthService from '../../services/adminAuthService';

const AdminLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleInitiateLogin = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await AdminAuthService.initiateLogin(email, password);
      
      if (response.status === 'success') {
        setShowOtpForm(true);
        setError('');
      } else {
        setError(response.message || 'Login initiation failed');
      }
    } catch (error) {
      setError(error.message || 'Login initiation failed');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await AdminAuthService.verifyOtp(email, password, otp);
      
      if (response.status === 'success') {
        // No need to store tokens - they're in cookies now
        // Just redirect to the admin dashboard
        navigate('/admin/dashboard');
      } else {
        setError(response.message || 'OTP verification failed');
      }
    } catch (error) {
      setError(error.message || 'OTP verification failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-login-container">
      <h2>Admin Login</h2>
      
      {!showOtpForm ? (
        // Step 1: Email and Password Form
        <form onSubmit={handleInitiateLogin}>
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" disabled={loading}>
            {loading ? 'Sending OTP...' : 'Login'}
          </button>
        </form>
      ) : (
        // Step 2: OTP Verification Form
        <form onSubmit={handleVerifyOtp}>
          <div className="form-group">
            <label htmlFor="otp">Enter OTP sent to your email</label>
            <input
              type="text"
              id="otp"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              required
              autoFocus
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" disabled={loading}>
            {loading ? 'Verifying...' : 'Verify OTP'}
          </button>
          
          <button 
            type="button" 
            onClick={() => setShowOtpForm(false)} 
            className="back-button"
          >
            Back
          </button>
        </form>
      )}
    </div>
  );
};

export default AdminLogin;
```

### 4. Update Admin Dashboard Component

```jsx
// src/components/admin/AdminDashboard.jsx
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminAuthService from '../../services/adminAuthService';
import AdminDashboardService from '../../services/adminDashboardService';

const AdminDashboard = () => {
  const [admin, setAdmin] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        // Get current admin user
        const adminResponse = await AdminAuthService.getCurrentAdmin();
        if (adminResponse.status === 'success') {
          setAdmin(adminResponse.data);
        }

        // Get dashboard stats
        const statsResponse = await AdminDashboardService.getDashboardStats();
        if (statsResponse.status === 'success') {
          setStats(statsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching admin data:', error);
        setError('Failed to load admin data. Please try logging in again.');
        
        // If authentication error, redirect to login
        if (error.response?.status === 401 || error.response?.status === 403) {
          navigate('/admin/login');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, [navigate]);

  const handleLogout = async () => {
    try {
      await AdminAuthService.logout();
      navigate('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading admin dashboard...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="admin-dashboard">
      <header className="dashboard-header">
        <h1>Admin Dashboard</h1>
        {admin && (
          <div className="admin-info">
            <span>Welcome, {admin.firstName} {admin.lastName}</span>
            <button onClick={handleLogout} className="logout-button">Logout</button>
          </div>
        )}
      </header>

      <div className="dashboard-content">
        {stats && (
          <div className="stats-cards">
            <div className="stat-card">
              <h3>Total Users</h3>
              <p className="stat-value">{stats.totalUsers}</p>
            </div>
            <div className="stat-card">
              <h3>Total News</h3>
              <p className="stat-value">{stats.totalNews}</p>
            </div>
            <div className="stat-card">
              <h3>Active Subscriptions</h3>
              <p className="stat-value">{stats.activeSubscriptions}</p>
            </div>
            {/* Add more stat cards as needed */}
          </div>
        )}

        {/* Add more dashboard sections as needed */}
      </div>
    </div>
  );
};

export default AdminDashboard;
```

### 5. Create Admin Dashboard Service

```javascript
// src/services/adminDashboardService.js
import api from '../api/axiosConfig';

const AdminDashboardService = {
  // Get dashboard statistics
  getDashboardStats: async () => {
    try {
      const response = await api.get('/admin/dashboard/stats');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to get dashboard stats' };
    }
  },

  // Get all users
  getAllUsers: async (page = 0, size = 10) => {
    try {
      const response = await api.get(`/admin/dashboard/users?page=${page}&size=${size}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to get users' };
    }
  },

  // Get all news
  getAllNews: async (page = 0, size = 10) => {
    try {
      const response = await api.get(`/admin/dashboard/news?page=${page}&size=${size}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to get news' };
    }
  },

  // Add more admin dashboard methods as needed
};

export default AdminDashboardService;
```

### 6. Update Authentication Context (if using React Context)

```jsx
// src/contexts/AuthContext.jsx
import React, { createContext, useState, useEffect, useContext } from 'react';
import AdminAuthService from '../services/adminAuthService';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const response = await AdminAuthService.getCurrentAdmin();
        if (response.status === 'success') {
          setCurrentUser(response.data);
        }
      } catch (error) {
        console.error('Error loading user:', error);
        setError(error);
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  const logout = async () => {
    try {
      await AdminAuthService.logout();
      setCurrentUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    currentUser,
    loading,
    error,
    logout,
    setCurrentUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

### 7. Create Protected Route Component

```jsx
// src/components/common/ProtectedRoute.jsx
import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const ProtectedRoute = ({ adminOnly = false }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/admin/login" replace />;
  }

  if (adminOnly) {
    const isAdmin = currentUser.roles?.includes('ADMIN');
    if (!isAdmin) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return <Outlet />;
};

export default ProtectedRoute;
```

### 8. Update App Routes

```jsx
// src/App.jsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AdminLogin from './components/admin/AdminLogin';
import AdminDashboard from './components/admin/AdminDashboard';
import ProtectedRoute from './components/common/ProtectedRoute';
import NotFound from './components/common/NotFound';
import Unauthorized from './components/common/Unauthorized';

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          {/* Public routes */}
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/unauthorized" element={<Unauthorized />} />
          
          {/* Protected admin routes */}
          <Route element={<ProtectedRoute adminOnly={true} />}>
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            {/* Add more admin routes here */}
          </Route>
          
          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;
```

## Debugging Tips

If you're still experiencing issues after implementing these changes:

1. **Check Browser Developer Tools**:
   - Open the Network tab to see if cookies are being sent with requests
   - Look for the `jwt_token` cookie in the Application/Storage tab
   - Check for CORS errors in the Console tab

2. **Use the Debug Endpoints**:
   - Call `/api/admin/debug/auth-info` to check authentication status
   - This will show if cookies are being received by the server

3. **Common Issues and Solutions**:

   - **CORS Issues**: Make sure your backend CORS configuration allows credentials and your frontend origin
   ```
   Access-Control-Allow-Origin: http://localhost:3000
   Access-Control-Allow-Credentials: true
   ```

   - **Cookie Not Set**: Check if the Set-Cookie header is present in the login response
   
   - **Cookie Not Sent**: Ensure `withCredentials: true` is set for all requests
   
   - **SameSite Issues**: If your frontend and backend are on different domains, you may need to set SameSite=None and Secure=true for cookies

## Production Considerations

For production deployment:

1. **Enable HTTPS**: HTTP-only cookies should always be used with HTTPS in production
   - Update `app.jwt.cookieSecure=true` in your backend properties

2. **Domain Configuration**: Set the cookie domain to your production domain
   - Add `app.jwt.cookieDomain=yourdomain.com` to your backend properties

3. **CORS Configuration**: Update allowed origins to your production frontend URL
   - Update `app.security.allowedOrigins[0]=https://yourdomain.com` in your backend properties

4. **Environment Variables**: Use environment variables for API URLs
   ```javascript
   baseURL: process.env.REACT_APP_API_URL || 'https://api.yourdomain.com',
   ```

5. **Security Headers**: Add security headers to your frontend server
   ```
   Strict-Transport-Security: max-age=31536000; includeSubDomains
   X-Content-Type-Options: nosniff
   X-Frame-Options: DENY
   ```

By implementing these changes, your frontend will properly work with HTTP-only cookies for JWT authentication, providing a more secure authentication mechanism for your admin dashboard.
